.spinner,
.tp-loader.spinner0,
#fancybox-loading div {
    border-left: 3px solid rgba(26,187,156,.15);
    border-right: 3px solid rgba(26,187,156,.15);
    border-bottom: 3px solid rgba(26,187,156,.15);
    border-top: 3px solid rgba(26,187,156,.8);
}
a {
    color: #1abb9c;
}
.nocolor:hover {
    color: #1abb9c
}
.post-title a:hover {
    color: #1abb9c
}
.main-title.text-center:after {
    color: #1abb9c;
}
ul.circled li:before {
    color: #1abb9c;
}
.meta a:hover,
.more:hover {
    color: #1abb9c
}
footer a:hover {
    color: #1abb9c !important
}
.btn,
.vanilla-form label.custom-select span {
    background: #1abb9c;
}
.btn:hover,
.btn:focus,
.btn:active,
.btn.active,
.pagination ul > li > a:hover,
.pagination ul > li > a:focus,
.pagination ul > .active > a,
.pagination ul > .active > span {
    background: #17a78b;
}
.navbar .navbar-nav > li > a:focus,
.navbar .nav > li > a:hover,
.navbar .nav > li.current > a {
    color: #1abb9c !important;
}
.navbar .dropdown-menu li a:hover,
.navbar .dropdown-menu li a.active {
    color: #1abb9c !important;
}
.navbar .nav .open > a,
.navbar .nav .open > a:hover,
.navbar .nav .open > a:focus {
    color: #1abb9c;
}
.navbar .dropdown-menu > li > a:hover,
.navbar .dropdown-menu > li > a:focus,
.navbar .dropdown-submenu:hover > a,
.navbar .dropdown-submenu:focus > a,
.navbar .dropdown-menu > .active > a,
.navbar .dropdown-menu > .active > a:hover,
.navbar .dropdown-menu > .active > a:focus {
    color: #1abb9c
}
.navbar .top-bar a:hover {
	color: #1abb9c;
}
.yamm .yamm-content a:hover {
    color: #1abb9c
}
.steps .icon {
    color: #1abb9c;
}
.steps .steps-item .number {
    background: #1abb9c;
}
.steps .steps-item:hover {
    border-color: #1abb9c
}
.feature .icon {
    color: #1abb9c;
}
.icon-large {
    color: #1abb9c;
}
.owl-item .item:hover .box {
    box-shadow: 0 2px 0 #1abb9c
}
.post-types .date-wrapper {
    background: #1abb9c;
}
.post-types .post .format-wrapper {
    background: #1abb9c;
}
.sidebox a:hover {
    color: #1abb9c
}
.widget .post-list h5 a:hover {
    color: #1abb9c
}
.widget .post-list .meta em a:hover {
    color: #1abb9c
}
footer ul.tag-list li a:hover {
    background: #1abb9c !important
}
.testimonials2 .quote:hover {
    border: 2px solid #1abb9c
}
.isotope-filter ul li a:hover,
.isotope-filter ul li a.is-checked {
    color: #1abb9c
}
.isotope-filter ul li:after {
    color: #1abb9c;
}
.price {
    color: #1abb9c;
}
.progress-list li em {
    color: #1abb9c;
}
.progress.plain .bar {
    background: #1abb9c;
}
.bordered .progress.plain {
    border: 1px solid #1abb9c;
}
.bordered .progress.plain .bar {
    background: #1abb9c
}
.tabs-top.bordered .tab a {
    color: #1abb9c;
    border: 2px solid #1abb9c;
}
.tabs-top .tab a:hover,
.tabs-top .tab.active a {
    color: #1abb9c;
    box-shadow: 0 2px 0 #1abb9c;
}
.tabs-top.bordered .tab a:hover,
.tabs-top.bordered .tab.active a {
    border: 2px solid #1abb9c;
    background: #1abb9c;
}
.tabs-bottom .tab:hover .icon.box,
.tabs-bottom .tab.active .icon.box {
    box-shadow: 0 2px 0 #1abb9c
}
.panel-group .panel-active a,
.panel-group .panel-title > a:hover {
    color: #1abb9c
}
.panel-group .panel-heading .panel-title:hover,
.panel-group .panel-active .panel-heading .panel-title {
    box-shadow: 0 2px 0 #1abb9c;
    color: #1abb9c;
}
.bordered .panel-heading .panel-title {
    color: #1abb9c;
    border: 2px solid #1abb9c;
}
.bordered .panel-heading .panel-title:hover {
    background: #1abb9c;
    border: 2px solid #1abb9c;
}
.bordered .panel-title > a {
    color: #1abb9c
}
.bordered .panel-title:hover,
.bordered .panel-active .panel-heading .panel-title,
.bordered .panel-active .panel-heading .panel-title:hover {
    border: 2px solid #1abb9c;
    background: #1abb9c;
}
.tooltip-inner {
    background-color: #1abb9c;
}
.tooltip.top .tooltip-arrow,
.tooltip.top-left .tooltip-arrow,
.tooltip.top-right .tooltip-arrow {
    border-top-color: #1abb9c
}
.tooltip.right .tooltip-arrow {
    border-right-color: #1abb9c
}
.tooltip.left .tooltip-arrow {
    border-left-color: #1abb9c
}
.tooltip.bottom .tooltip-arrow,
.tooltip.bottom-left .tooltip-arrow,
.tooltip.bottom-right .tooltip-arrow {
    border-bottom-color: #1abb9c
}
.vanilla-form input[type="radio"]:focus + span,
.vanilla-form input[type="checkbox"]:focus + span,
.vanilla-form input[type="radio"]:active + span,
.vanilla-form input[type="checkbox"]:active + span {
    border-color: #1abb9c
}
.vanilla-form input[type="radio"] + span::after {
    background-color: #1abb9c;
    border-color: #1abb9c;
}
.vanilla-form input[type="radio"]:checked + span,
.vanilla-form input[type="checkbox"]:checked + span {
    border: 2px solid #1abb9c
}
.vanilla-form input[type="checkbox"] + span::after {
    border: 0 solid #1abb9c;
}
#comments .info h2 a:hover {
    color: #1abb9c
}
#comments a.reply-link:hover {
    color: #1abb9c
}
#contact-info-button:hover,
#btn-close-modal:hover {
    color: #1abb9c
}
@media (max-width: 991px) { 
	.navbar .navbar-nav .open .dropdown-menu > li > a:hover,
	.navbar .navbar-nav .open .dropdown-menu > li > a:focus {
	    color: #1abb9c !important
	}
}
@media (max-width: 767px) { 
	.isotope-filter ul li a:hover,
	.isotope-filter ul li a.is-checked {
	    box-shadow: 0 2px 0 #1abb9c !important
	}
}