<?php
defined('BASEPATH') OR exit('No direct script access allowed');
function str2($amount,$number,$pin,$ussd){
    			

$vars = array(
  '%amount%'       => $amount,
  '%number%'        => $number,
  '%pin%' => $pin
);

$usd= strtr($ussd, $vars);
$newString = str_replace(" ","",$usd);
if (strlen($newString) <= 0) {
$usd="no"; }
return $usd; 
}
class Modemcon extends CI_Controller {
	
	function __construct()
    {
        parent::__construct();
        
        date_default_timezone_set('Asia/Dhaka');
     
		$this->load->library('form_validation');
		$this->load->library('user_agent');
    }
	
	 private function date_time(){
        return $create_date = date('Y-m-d H:i:s');
     }
	 

	
	
	

  
  

public function resendmmi(){
 
	$sqlr="SELECT * from `sendflexi` where (`apiresponse` LIKE '%default%' or `apiresponse`='') and `status`='4' and `resend_count` < 3 order by id desc limit 5"; 

	$quer=$this->db->query($sqlr);
		
			if($quer->num_rows()> 0) {
	       	
				foreach ($quer->result() as $user_datam)
					{
                  $timeA = strtotime($user_datam->submitted_date);
                  
                  $timeB = time();

//five minutes in seconds
$fiveMinutes = 60 * 2;

//check if current time is after 5 minutes the initial time
if ( ($timeA+$fiveMinutes) <= $timeB) {
                  
                  
                  
		  $mup = "UPDATE `sendflexi` SET `status`='0',`local`='0',`resend_count`=resend_count+1, apiresponse='rsend' WHERE `id`='$user_datam->id'"; 
		 
	$this->db->query($mup);
		
		$modem = $this->db->get_where('security_option',array('id' =>1))->row()->modem;
 if($modem==0){
      
     $this->mdb->real_time($user_datam->id);
     
 }			
echo $user_datam->id;
					
	echo"<br/>";
}
				}
			    
			}
	
	}	
  
  
  
  
  
  public function auto_cancel(){
 $cancel_list = $this->db->get_where('company',array('id' =>'1'))->row()->cancel_list;
	$sqlr="SELECT * from `sendflexi` where  `status`='4' order by id asc limit 5"; 

	$quer=$this->db->query($sqlr);
		
			if($quer->num_rows()> 0) {
	       	
				foreach ($quer->result() as $user_datam)
					{
                
                  if (preg_match("/$cancel_list/isU", $user_datam->apiresponse)){ 
                  	if($user_datam->status!=3){
                      $decideid=$user_datam->id;
$amount = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->balance;
$service = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->service;
	$source = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->result;

	$create_date=date('Y-m-d H:i:s'); 
	$idate = date('Y-m-d'); 
	$sql="UPDATE `sendflexi` SET `actual_send_time`='$create_date', `idate`='$idate', `trxid` = 'auto cancel', `status` = '3', `local` = '3', `refund` = '3', `api` = '1' WHERE `sendflexi`.`id` ='$decideid' ";

	$this->db->query($sql); 

	$cansq="SELECT * FROM `trans` WHERE send_sl='$decideid'"; 
	$query = $this->db->query($cansq);
	foreach ($query->result() as $row_cancel){
 	$debitamt=$row_cancel->debit; 
  	$canuserid=$row_cancel->userid; 
  	$prebalance=$this->mit->accountBalance($canuserid,$source); 
	$accountbalance=$prebalance+$debitamt; 
  	$cantypr="plus"; 
 
	$resellercustomerName = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->username;
		$resName = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->mame;
	$parent_id = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->p_id;

	$mobile = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->phone;
	$amount = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->balance;
	$sid333 = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->sid;
	

	$this->mit->balanceUpdate($canuserid,$debitamt,$cantypr,$source); 
      
    $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `service`, `flex_id`, `status`, `send_sl`, `p_id`, `date`, `time`) VALUES (NULL, '$canuserid', '$title_op Cancell By Admin Num: $mobile and amount : $amount Res: $resellercustomerName', '$prebalance', '', '$debitamt', '$accountbalance', '$cantypr', '$service', '$sid333', '3', '$decideid', '$parent_id', '$idate', '$sucdate');";

     $this->db->query($sql_tr); 

 	}
 	
 	$robi=substr($resellercustomerName , 0, 2);
 			
    $reamin=($prebalance+$debitamt);
    
$msg ="$title_op Tk $amount to $mobile Faild for $trid Reaming BDT $reamin";
$fb = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->facebook; 	

	
   $sms_type = $this->db->get_where('security_option',array('id' =>1))->row()->sms_type;
	if($sms_type==1){
      if($robi=='88'){
    $sents='1';
      }
    }
  
  
  if($sms_type==0){
    if($req_type=='offline'){
    $sents='1';
    }
    }
  
if($sents=='1'){

			    $this->mdb->sms_send($resellercustomerName,$msg);
				
  

}

if($req_type=='fb'){
$this->mdb->sendfb("flexiload","SMS*$fb*$msg*DD"); 
}


	}
	
                  
                  
                  
echo $user_datam->id;
					
	echo"<br/>";
                  }
				}
			    
			}
	
	}	
  
  
  
  
  
  
  
  
  
  
  
  
   public function device(){
		
 
	
		
	if($_POST) {
	$var=$this->input->post();

		$m1 = $this->mdb->pcode($var['m1']);
			$m2 = $this->mdb->pcode($var['m2']);
		$m3 = $var['m3'];
			$m4 = $var['m4'];
		$m5 = $var['m5'];
			$m6 = $var['m6'];
		$m7 = $var['m7'];
			$m8 = $var['m8'];
	   	$name = $var['name'];
	      if($m1=='GP' OR $m2=='GP' OR $m3=='GP'){
	      $sk='SK';  
	    }
			$dt = new DateTime('now', new DateTimezone('Asia/Dhaka')); 
	$create_date=$dt->format('j F Y g:i A'); 


	
$apiuserm = "Select * from modem_device where device_id='$var[device]' order by id asc limit 1"; 
		
			$queryrtsloadm=$this->db->query($apiuserm);
			
			if($queryrtsloadm->num_rows()> 0) {
			    
			    
			    $mup = "UPDATE `modem_device` SET `m1`='$m1',`m2`='$m2',`m3`='$m3',`m4`='$m4',`m5`='$m5',`m6`='$m6',`m7`='$m7',`m8`='$m8' ,`m9`='$sk' WHERE `device_id`='$var[device]'"; 
		 
		$this->db->query($mup);
			    
			}else{
			    
			    
			    $sql_tr="INSERT INTO `modem_device` (`id`, `m1`, `m2`, `m3`, `m4`, `m5`, `m6`, `m7`, `m8`,`m9`, `device_id`,`name`) VALUES (NULL, '$m1', '$m2', '$m3', '$m4', '$m5', '$m6', '$m7', '$m8','$sk', '$var[device]','$name')"; 

		 $this->db->query($sql_tr); 
			    
			    
			}
			
			
								
		
		 
		 
		 
           
			
			header('Content-type: application/json'); 
			
			 $age = array("status"=>1);

echo json_encode($age);

	}
	

	}	


  
  
			public function updateres(){
		if($_POST) {	
	
	$get=$this->input->get();
		$var=$this->input->post();
				$pass = $var['pass'];
				$appreffer = $var['ref'];

					
			$str = $var['body'];
		$sender = $var['sender'];
	    
			
 

  $queryi = "UPDATE `sendflexi` SET `apiresponse`='$str' WHERE `id` ='$sender'";
  
	
   $this->db->query($queryi);

   
 
				



if(!empty($appreffer)){
    $age = array("status"=>1);

echo json_encode($age);
    
}else{
echo "suc";
}

}
	
	}	



	
	
	
	
	

	public function update(){
		if($_POST) {
		    function paymentsimBalance($msg){
$searchd = preg_match_all("/Balance ([a-zA-Z0-9]+)(.+)\.[\w]/isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    if(empty($searchd)){
        $searchd = preg_match_all("/Balance: ([a-zA-Z0-9]+)(.+)\.[\w]/isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
        
    }

$int = str_replace('Balance', '', $matche[0][0]);
$int = str_replace('is', '', $int);
$int = str_replace('TK', '', $int);
$int = str_replace('Tk', '', $int);
$int = str_replace(',', '', $int);
$int = str_replace(':', '', $int);
$int = str_replace(' ', '', $int);
$int=utf8_encode(money_format('%.0n', floor($int)));
  if(empty($int)){
              $searchd = preg_match_all("/Balance Tk. ([a-zA-Z0-9]+)(.+)\./isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    
$int = str_replace('Balance Tk', '', $matche[0][0]);
$int = str_replace(' ', '', $int);
$int = str_replace('.', '', $int);
              }
return $int; 
}
		    
		     function trxidn($msg){
preg_match('[TxnID:]', $msg,$m);

$searchd = preg_match_all("/$m[0].([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
$int = str_replace('Balance', '', $pieces[1]);
$int = str_replace('Bal', '', $int);
$int = str_replace(':', '', $int);
$int= trim(preg_replace('/\s\s+/', ' ', $int));
$int = str_replace("\r", "", $int);
$int = str_replace("\n", "", $int);
 $array = explode("\\",$int);   
 $int=$array[0];
return $int; 
}

function trxid2($msg){
preg_match('[TxnId]', $msg,$m);

$searchd = preg_match_all("/$m[0].([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
if(empty($matche[0][0])){
    
    preg_match('[TxnId:]', $msg,$m);
 $searchd = preg_match_all("/$m[0] ([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
    
    
}

$pieces = explode(":",$matche[0][0]);
$int = str_replace(' ', '', $pieces[1]);
$int = str_replace('TxnId', '', $int);
return $int; 
}


function trxidbk($msg){
preg_match('[TrxID]', $msg,$m);

$searchd = preg_match_all("/$m[0].([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);

return $pieces[1]; 
}
		 function fee($msg){
$searchd = preg_match_all("/Fee ([a-zA-Z0-9]+)(.+)\.[\w]/isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    if(empty($searchd)){
        $searchd = preg_match_all("/Fee: ([a-zA-Z0-9]+)(.+)\.[\w]/isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
        
    }if(empty($searchd)){
        $searchd = preg_match_all("/Fee:([a-zA-Z0-9]+)(.+)\.[\w]/isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
      //print_r($matche);  
    }
    
    
 
$int = str_replace('Fee', '', $matche[0][0]);
$int = str_replace('is', '', $int);
$int = str_replace('TK', '', $int);
$int = str_replace('Tk', '', $int);
$int = str_replace(',', '', $int);
$int = str_replace(':', '', $int);
$int=utf8_encode(money_format('%.0n', floor($int)));
return $int; 
}
		    
		    
		  function bill_contact($msg){
$searchd = preg_match_all("/contact no: ([a-zA-Z0-9]+)(.+)\./isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    $int= $pieces[2];
$int = str_replace('.', '', $int);
 $int = mb_substr($int, 2);
$pis = explode(",",$msg);
$pi = explode(" ",$pis[0],2);
    
return "$int ".strtolower($pi[1]).""; 
}  
		    
function curl_bill($url)
{
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, $url);

    $data = curl_exec($ch);
    curl_close($ch);

    return $data;
}

function bill_contact_name($msg){

$piecesd = explode(",",$msg);
    $ints= $piecesd[0];
$ints = str_replace('Congrats', '', $ints);

$pieces = explode(":",$msg);
    $int= $pieces[1];
$int = str_replace(' ', '', $int);
$msisdn = preg_replace("/^(00)?(88)?0/", "", $int);
  
$tr=strtolower("0$msisdn $ints");
return $tr; 
}
		function bill_account($msg){
$searchd = preg_match_all("/Account no: ([a-zA-Z0-9]+)(.+)\./isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    $int= $pieces[2];
$int = str_replace('.', '', $int);
return $int; 
}

	function bill_id($msg){
preg_match('#is \((.*?)\)#', $msg, $match);
return $match[1];
}


	function billpin($msg){
preg_match('#Pin\((.*?)\)#', $msg, $match);
return $match[1];
}


function billno($msg){
preg_match('#No\((.*?)\)#', $msg, $matchs);
$no=$matchs[1];
if(empty($no)){
    
  preg_match('#Number\((.*?)\)#', $msg, $match);
$no=$match[1];  
}
if(empty($no)){
    
  preg_match('#number\((.*?)\)#', $msg, $match);
$no=$match[1];  
}
return $no;
}


function billamount($msg){
preg_match('#Amount\((.*?)\)#', $msg, $match);
return $match[1];
}
function billtrnxid($msg){
$searchd = preg_match_all("/Transaction Id:([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
$pieces = explode(":",$matche[0][0]);
    $int= $pieces[1];
$int = str_replace('.', '', $int);
return $int; 
}	    
		    
 function trxidtel($msg){
preg_match('[ID is]', $msg,$m);

$searchd = preg_match_all("/$m[0].([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);

return $pieces[2]; 
}
function ratettel($msg){
preg_match('[with]', $msg,$m);

$searchd = preg_match_all("/$m[0].([0-9]+)(.+)\./isU",$msg,$matche);
$int = filter_var($matche[0][0], FILTER_SANITIZE_NUMBER_INT);
if(strlen($int)>8)return false;
return $int; 

}

function mnumbertele($msg){
 

$searchd = preg_match_all("/$m[0].([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);

//return $pieces[1]; 
 
return "0".$matche[0][3];

}


function mnumberteleold($msg){
 
$search = preg_match_all("/(\d+|\-|\+|\(|\)|\ ){0,}(1)(\d+|\ |\-){9,10}/",$msg,$matches);

    $msg = str_replace(' ', '', $matches[0][1]);
 
return "0".$msg;

}


function skito($msg){
$searchd = preg_match_all("/recharge ([a-zA-Z0-9]+)(.+)\.[\w]/isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    
$int = str_replace('recharge', '', $pieces[1]);

$int = filter_var($int, FILTER_SANITIZE_NUMBER_INT);
if(strlen($int)>8)return false;
return $int; 
}

function simBalanceal($msg){
$searchd = preg_match_all("/balance is ([a-zA-Z0-9]+)(.+)\.[\w]/isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    
$int = str_replace('balance is', '', $matche[0][0]);

$int = str_replace('TK', '', $int);

return $int; 
}

function simBalanceal2($msg){
$searchd = preg_match_all("/balance is TK ([a-zA-Z0-9]+)(.+)\./isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    
$int = str_replace('balance is TK', '', $matche[0][0]);

$int = str_replace('.', '', $int);
if(empty($int)){
$searchd = preg_match_all("/Balance Tk. ([a-zA-Z0-9]+)(.+)\./isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    
$int = str_replace('Balance Tk', '', $matche[0][0]);
$int = str_replace(' ', '', $int);
$int = str_replace('.', '', $int);
}
return $int; 
}

function mnumbergp($text){
 
$search = preg_match_all("/(\d+|\-|\+|\(|\)|\ ){0,}(1)(\d+|\ |\-){8,14}/",$text,$matches);
if (preg_match('/\s/',$matches[0][0])){
  $pieces = explode(" ",$matches[0][0],5);  
  
  $msg=$pieces[1];
}
if(empty($msg))$msg=$matches[0][0];

    $msg = str_replace(' ', '', $msg);
   $m = mb_substr($msg, 0, 1);
if($m!='0'){
 $msg='0'.$msg.'';}else{ $msg=$msg;}
 
 if(strlen($msg)>12){
     if (preg_match('/\s/',$matches[0][1])){
  $pieces = explode(" ",$matches[0][1],5);  
  
  $msg=$pieces[1];
}
if(empty($msg))$msg=$matches[0][1];
     
     $msg = str_replace(' ', '', $msg);
   $m = mb_substr($msg, 0, 1);
if($m!='0'){
 $msg='0'.$msg.'';}else{ $msg=$msg;}
     
     
 }else{ $msg=$msg;}
 
 
 if(strlen($msg)>12){
     
    $searchd = preg_match_all("/to ([0-9]+)(.+)\./isU",$text,$matche);
$pieces = explode(" ",$matche[0][0]);
    $int= $pieces[1];
$int = str_replace('.', '', $int);
$int = str_replace(' ', '', $int);
$msisdn = preg_replace("/^(00)?(88)?0/", "", $int);
  
$msg=strtolower("0$msisdn $ints");
  
     
     
 }


if(empty($msg)){
     
    $searchd = preg_match_all("/to ([0-9]+)(.+)\ /isU",$text,$matche);
$pieces = explode(" ",$matche[0][0]);
    $int= $pieces[1];
$int = str_replace('.', '', $int);
$int = str_replace(' ', '', $int);
$msisdn = preg_replace("/^(00)?(88)?0/", "", $int);
  
$msg=strtolower("0$msisdn $ints");
  
     
     
 }

 
 if(strlen($msg)<10){
     
     preg_match_all("/(\d+|\-|\+|\(|\)|\ ){0,}(1)(\d+|\ |\-){8,14}/",$text,$matches);
 $int=$matches[0][0];

 
 $pieces = explode(" ",$int,5);
   
   $msisdn = preg_replace("/^(00)?(88)?0/", "", $pieces[1]);
   $msg=strtolower("0$msisdn");
     
 }
 


 


return $msg;

} 



function trxidrobib($msg){
preg_match('[ID]', $msg,$m);

$searchd = preg_match_all("/$m[0].([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);

$pieces = explode(" ",$matche[0][0]);
    $int= $pieces[1];
$pieces = explode("Your",$pieces[1]);
    $int= $pieces[0];
return $int; 
}


 function mnumber($msg){
 
$search = preg_match_all("/(\d+|\-|\+|\(|\)|\ ){0,}(1)(\d+|\ |\-){8,14}/",$msg,$matches);

    $msg = str_replace(' ', '', $matches[0][0]);
 
return $msg;

}

function trxidas($msg){
$searchd = preg_match_all("/Ref: ([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    $int= $pieces[1];
$int = str_replace('.', '', $int);
return $int; 
}

function trxid($msg){
$searchd = preg_match_all("/TrxId:([0-9]+)(.+)\./isU",$msg,$matche);
return $matche[0][0]; 
}

function trxidbl($msg){
preg_match('[ID]', $msg,$m);

$searchd = preg_match_all("/$m[0].([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    $int= $pieces[1];
    if(empty($int)){
       $pieces = explode(' ', $msg);
$int = array_pop($pieces);
     $int = str_replace('.', '', $int);
        
    }
return $int;
}
function trxidrobi($msg){
preg_match('[is]', $msg,$m);

$searchd = preg_match_all("/$m[0].([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);

$pieces = explode(" ",$matche[0][1]);
    $int= $pieces[1];
$pieces = explode("Your",$pieces[1]);
    $int= $pieces[0];
    
    if(strlen($int)<7){
        
       preg_match('[number]', $msg,$m);

$searchd = preg_match_all("/$m[0] ([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);

$pieces = explode(" ",$matche[0][0]);
    $int= $pieces[1];

        
    }
    
    
    
     if(strlen($int)<7){
        
       preg_match('[number is]', $msg,$m);

$searchd = preg_match_all("/$m[0] ([a-zA-Z0-9]+)(.+)\.Your/isU",$msg,$matche);

$int=$matche[0][0];
  

        
    }
    
    
    $int = str_replace('number', '', $int);
    $int = str_replace('is', '', $int);
    $int = str_replace('Your', '', $int);
    
return $int; 
}
function timeget($msg){
$searchd = preg_match_all("/@([0-9]+)(.+)\:/isU",$msg,$matche);
  $msg = str_replace('@', '', $matche[0][0]);
 
return $msg;
}

function rate($msg){
preg_match('[BK|DB|GP|GPP|RB|RBP|BL|BLP|AT|ATP|TT|TTP]', $msg,$m);

$searchd = preg_match_all("/$m[0].([0-9]+)(.+)\./isU",$msg,$matche);
$int = filter_var($matche[0][0], FILTER_SANITIZE_NUMBER_INT);
if(strlen($int)>8)return false;
return $int; 

}

function rate_nagad($msg){
preg_match('[Amount: Tk]', $msg,$m);

$searchd = preg_match_all("/$m[0] ([0-9]+)(.+)\./isU",$msg,$matche);
$int = filter_var($matche[0][0], FILTER_SANITIZE_NUMBER_INT);





return $int; 

}


function rate2($msg){
preg_match('[BK|DB|GP|GPP|RB|RBP|BL|BLP|AT|ATP|TT|TTP|TK|BDT]', $msg,$m);

$searchd = preg_match_all("/$m[0].([0-9]+)(.+)\./isU",$msg,$matche);
$int = filter_var($matche[0][0], FILTER_SANITIZE_NUMBER_INT);
if(strlen($int)>8)return false;
if(empty($int)){
    preg_match('[BK|DB|GP|GPP|RB|RBP|BL|BLP|AT|ATP|TT|TTP|Tk|BDT]', $msg,$m);

$searchd = preg_match_all("/$m[0].([0-9]+)(.+)\./isU",$msg,$matche);
$int = filter_var($matche[0][0], FILTER_SANITIZE_NUMBER_INT);
if(strlen($int)>8)return false;
    
    
}



if(empty($int)){
    preg_match('[Recharge]', $msg,$m);

$searchd = preg_match_all("/$m[0] ([a-zA-Z0-9]+)(.+)\./isU",$msg,$matche);
$int = filter_var($matche[0][0], FILTER_SANITIZE_NUMBER_INT);
if(strlen($int)>8)return false;
    
    
}
return $int; 

}

function rate3($msg){
$pieces = explode(" ", $msg);
    $int= $pieces[5];
$int= preg_replace('/Tk/', '', $int);
$int = explode(".", $int);
    $int= $int[0];
$int= preg_replace('/,/', '', $int);
  if(!is_numeric($int)){
   $int= $pieces[0];
$int= preg_replace('/Tk/', '', $int);
$int = explode(".", $int);
    $int= $int[0];
$int= preg_replace('/,/', '', $int);
  
  
  }
return $int; 

}


function bn2en($text){
    			

$vars = array(
 'নম্বরে' => 'amount',
  'নম্বর'       => 'number',
  'ট্রানজ্যাকশন'        => 'Transaction',
   'টাকা'        => 'TK',
    'রিচার্জ'        => 'Recharge',
	 'সফল'        => 'successful',
	  'নতুন '        => 'new ',
	   'ব্যালেন্স'        => 'balance',
	    'আপনার'        => 'your',
		 'হল'        => 'is',
		  'হয়েছে৷'        => ' ',
		   '।'        => ' ',
 
);

$usd= strtr($text, $vars);

$usd = preg_replace('!\s+!', ' ', $usd);
return $usd; 
}


function rate8($msg){
$pieces = explode(" ", $msg);
$int = filter_var($pieces[1], FILTER_SANITIZE_NUMBER_INT);
if(strlen($int)>8 or empty($int)){
    
    $searchd = preg_match_all("/(*UTF8)(amount )([0-9]+)(.+)\./isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    $int= $pieces[1];  
    
}

if(strlen($int)>8)return false;

return $int; 

}


function ratebl($msg){
$pieces = explode(" ", $msg);
$int = filter_var($pieces[4], FILTER_SANITIZE_NUMBER_INT);
if(strlen($int)>8)return false;
return $int; 

}
function ratear($msg){
$pieces = explode(" ", $msg);
$int = filter_var($pieces[1], FILTER_SANITIZE_NUMBER_INT);
if(strlen($int)>8)return false;
return $int; 

}
	
	$get=$this->input->get();
		$var=$this->input->post();
				$pass = $this->db->escape_like_str($var['pass']);
			$str = $this->db->escape_like_str($var['body']);
		$sender = $this->db->escape_like_str($var['sender']);
	    	$appreffer = $this->db->escape_like_str($var['ref']);
	    		$test = $this->db->escape_like_str($var['test']);
				$dt = new DateTime('now', new DateTimezone('Asia/Dhaka')); 
	$create_date=$dt->format('j F Y g:i A'); 
	$idate = $dt->format('Y-m-d'); 

		

$simBalancealance=simBalanceal($str);
if(empty($simBalancealance)){
$simBalancealance=simBalanceal2($str);    
    
}

$simbalance=$simBalancealance;




if (preg_match('/ok/isU', $str)){ 
   $pieces = explode(" ",$str);
    $number= $pieces[1];
     $pin= $pieces[2];
$billorder=$this->mdb->getData('sendflexi',array('phone'=>$number,'status'=>4)); 



$type=$billorder[0]['type'];
if($type==2){
  
$msg="REGS YES $pin";
$domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);
$url="http://$domain/offline/sms?pa=5463243&type=bill&to=727&message=".urlencode($msg).""; 

    $data=curl_bill($url);   
    
    
    
   //echo  $data;
}
}













if (preg_match('/727/isU', $sender)){
$str=html_entity_decode($str);
if (preg_match('/successfully registration completed for REB/isU', $str)){ 

 $number= bill_contact($str) ;
 	$bid=bill_id($str);
     
  
   $strignupt = "UPDATE `sendflexi` SET `phone`='$bid', `status`='0', `local`='0',`type`='1' WHERE LOWER(`name`)='$number' and `status`='4' and `local`='4' and `type`='2'"; 
 $this->db->query($strignupt);
    
 
}

if (preg_match('/REGS<>acc_no<>contact_no<>name/isU', $str)){ 
   
$number= bill_account($str) ;

$billorder=$this->mdb->getData('sendflexi',array('phone'=>$number,'status'=>4)); 
  $type=$billorder[0]['type'];
 
 
 if($type==2){
   $no= $billorder[0]['phone'];
  $na=$billorder[0]['name'];
$msg="REGS $no $na";
$domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);
$url="http://$domain/offline/sms?pa=5463243&type=bill&to=727&message=".urlencode($msg).""; 

    $data=curl_bill($url);   
    
    
 // echo  $data;
}
 
 
}

if (preg_match('/pin is sent/isU', $str)){ 

 $number= billno($str) ;
 $billorder=$this->mdb->getData('sendflexi',array('phone'=>$number,'status'=>4)); 
       $type=$billorder[0]['type'];
       
      
 if($type==2){
     
   $nom= $billorder[0]['phone'];
   $pieces = explode(" ",$billorder[0]['name'],2);
    $na= $pieces[1];
     $no= $pieces[0];
  
$msg="Dear $na, Your Received a pin code. To confirm, type ok<sp>$nom<sp>pin_no and reply this sms";
$domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);
$url="http://$domain/offline/sms?pa=5463243&to=$no&message=".urlencode($msg).""; 

    $data=curl_bill($url);   
    
    
 // echo  $data;
}

}

if (preg_match('/Yes/isU', $str)){ 
   
$number= billno($str);
$rate=billamount($str);
$pin=billpin($str);
$billorder=$this->mdb->getData('sendflexi',array('phone'=>$number,'status'=>4)); 
}


$amo=$billorder[0]['balance'];
$type=$billorder[0]['type'];
if($type==1){
 if($rate==$amo){
$msg="YES $pin %pin%";
$domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);
$url="http://$domain/offline/sms?pa=5463243&type=bill&to=727&message=".urlencode($msg).""; 

    $data=curl_bill($url);   
   //echo  $data;
}
}






 if (preg_match('/SUCCESSFUL|success|Successful|Transaction/isU', $str)){ 

$number= billno($str);
$rate=billamount($str);
$trnx=billtrnxid($str);
$work_done="done";

}

}



if (preg_match('/FlexiLoad|Skitto/isU', $sender)){

$number= mnumbergp($str);

if (preg_match('/Skitto/isU', $sender)){
$rate=skito($str);
}else{
$rate=rate2($str);
}
$trnx=trxidbl($str);
 if (preg_match('/SUCCESSFUL|success|successful|Transaction/isU', $str) && !preg_match('/failed /isU', $str)){ 
$work_done="done";
}

}


if (preg_match('/461|492|iTopUP/isU', $sender)){

$number= mnumbergp($str);
$rate=ratebl($str);
$trnx=trxidbl($str);


 if (preg_match('/SUCCESSFUL|success|successful|accepted/isU', $str)){ 

$work_done="done";

}

}




if (preg_match('/8383/isU', $sender)){
$str=bn2en($str);
$number= mnumbergp($str);
$rate=rate8($str);
if($rate==null){
$rate=ratear($str);
}
$trnx=trxidrobi($str);

 if (preg_match('/SUCCESSFUL|accepted|success|successful|commission/isU', $str)){
     
    
$work_done="done";

}
//if($test=='1')echo $str;
}





if (preg_match('/telecharge/isU', $sender)){

$number= mnumbertele($str);
if($number==0){$number=mnumberteleold($str);}
$rate=ratettel($str);
$trnx=trxidtel($str);


 if (preg_match('/SUCCESSFUL|successfully|successful|accepted/isU', $str)){ 
$work_done="done";


}

}



if (preg_match('/bKash/isU', $sender)){

$number= mnumber($str);
$rate=rate2($str);
$ctrnx=trxidbk($str);
 if (preg_match('/Comm|success|successful|Cash-In|Cash|cash/isU', $str)){
  $work_done="done"; 
   $trnx = $this->db->get_where('modem',array('sender' =>$sender))->row()->number;
  //$trnx = $ctrnx;
  
  
 }
    
}

          
 if (preg_match('/upay/isU', $sender)){

$number= mnumber($str);
$rate=rate2($str);
$ctrnx=trxidbk($str);
 if (preg_match('/received|success|successful|Cash-In|Cash|cash/isU', $str)){
  $work_done="done";   
  $trnx = $this->db->get_where('modem',array('sender' =>$sender))->row()->number;
  
  
 }
    
}         
          
          

if (preg_match('/16216/isU', $sender)){

$number= substr(mnumber($str), 0, 11);
$rate=rate3($str);
$ctrnx=trxid2($str);
 if (preg_match('/Comm|success|successful|Cash-In|Cash|cash/isU', $str)){
$work_done="done";
 $trnx = $this->db->get_where('modem',array('sender' =>$sender))->row()->number;
  


}

}













if (preg_match('/NAGAD/isU', $sender)){
$sender='NAGAD';
$number= mnumber($str);
$rate=rate_nagad($str);
$ctrnx=trxidn($str);
  
 if (preg_match('/Comm|success|Successful|Cash-In|Cash|cash/isU', $str)){
$work_done="done";
 $trnx = $this->db->get_where('modem',array('sender' =>$sender))->row()->number;
  


}

}


if(!empty($work_done)){
    
 	$stype='4';
    
 if(empty($rate)){
     
 

 $orde=$this->mdb->getData('sendflexi',array('phone'=>$number,'status'=>$stype));
     
 }else{
     
     $orde=$this->mdb->getData('sendflexi',array('phone'=>$number,'status'=>$stype,'balance'=>$rate)); 
   
 }   
    
$oid=$orde[0]['userid'];
$id=$orde[0]['id'];
$com=$orde[0]['commision'];
	$ank_p = $this->db->get_where('reseller',array('id' =>$oid))->row()->username;
	
		$ank_fb = $this->db->get_where('reseller',array('id' =>$oid))->row()->facebook;
		
		$ank_p_id = $this->db->get_where('reseller',array('id' =>$oid))->row()->p_id;
  if(empty($simbalance))$simbalance="not update";
   

  $queryi = "UPDATE `sendflexi` SET `status`='1',`actual_send_time`='$create_date',`trxid`='$trnx',`simBalance`='$simbalance' WHERE `id` ='$id' AND `status`='$stype'";
  
		$ifexds=$this->db->query($queryi);
 
  $ank_name = $this->db->get_where('reseller',array('id' =>$oid))->row()->name;
   $ank_name_r = $this->db->get_where('reseller',array('id' =>$ank_p_id))->row()->name;
 
   if($orde[0]['status']==0){}
       $pb=$orde[0]['prebalance'];
       $co=$orde[0]['cost'];
       $remark=$orde[0]['remark'];
  $pe=($pb-$co);
 if($ank[name]==NULL){$an=$ank_name_r;}else{$an=$ank_name;}
 $bal=$orde[0]['balance'];
 $msg="$an TrxId $trnx No $number to recharge $bal Tk  successful Cost $co, New BDT $pe";
$robi=substr($ank_p , 0, 2);
  
   $sms_type = $this->db->get_where('security_option',array('id' =>1))->row()->sms_type;
	if($sms_type==1){
      if($robi=='88'){
    $sents='1';
      }
    }
  
  
  if($sms_type==0){
    if($remark=='offline'){
    $sents='1';
    }
    }
  
if($sents=='1'){
    $this->mdb->sms_send($ank_p,$msg);
     
}
   
if($remark=='apps'){
   $ftoken = $this->db->get_where('reseller',array('id' =>$oid))->row()->note;
   if(!empty($ftoken)){
       
      $title ="Successful request $number"; 
  $this->mit->send_fcm_msg($title,$msg,$ftoken,$decideid); 
   }
  
     
 }   
   
 if($remark=='fb'){
     
   
   

 
 
  $this->mdb->sendfb("flexiload","SMS*$ank_fb*$msg*DD"); 
   
   
   
   
     
 }
 
    
}

$se=$orde[0]['service'];
$po=$orde[0]['pcode'];

	$sql = "INSERT INTO `reportserver` (`msgbody`,`sender`,`pcode`,`servicid`,`datetime`,`idate`) VALUES ('$str','$sender','$po','$se','$create_date','$idate')";
	
		$this->db->query($sql);
	



if (preg_match('/bKash|NAGAD|16216|upay/isU', $sender)){
$time=time();
$simbalance=paymentsimBalance($str); 




		
if(!empty($ctrnx) && !empty($simbalance)){
  
 
    $simbalax = $this->db->get_where('modem',array('sender' =>$sender))->row()->simbalance;
  $tolerance = $this->db->get_where('modem',array('sender' =>$sender))->row()->tolerance;
    $ctrnx = $this->mdb->passwordChanger('encrypt', $ctrnx);
$msg = $this->mdb->passwordChanger('encrypt', $str);

    

if (preg_match('/received|Received|Cash-In|Cash|cash/isU', $str)){
$totalbal=($simbalax+$rate);
$plus=1;
}
if (preg_match('/Send|transferred/isU', $str)){
    if (!preg_match('/Sender/isU', $str)){
   $fee=  fee($str);
$totalbal=($simbalax-($rate+$fee));
    }

}

$sql = "SELECT * FROM `trnx` where trnx='$ctrnx'"; 
	$queryrtsloadm=$this->db->query($sql);
/*
if(($queryrtsloadm->num_rows()==0) && ($totalbal==$simbalance)) {
  */


              if(($queryrtsloadm->num_rows()==0)  && (($totalbal==$simbalance) or (abs($totalbal - $simbalance) <= $tolerance && abs($simbalance - $totalbal) <= $tolerance))) {        
		
		     $queryi = "UPDATE `modem` SET `simbalance`='$simbalance' WHERE `sender` ='$sender'";
  
		$ife=$this->db->query($queryi);
		    
		    


if($plus=='1'){
   
$time=time();


 	$sql = "INSERT INTO `trnx` (`time`,`amount`,`type`,`cat`,`sender`,`trnx`,`add_date`,`msg`) VALUES ('$time','$rate','$sender','$sender','$sender','$ctrnx','$create_date','$msg')";
	
		$this->db->query($sql);


		





    
}
}
}

}




if(!empty($appreffer)){
    $age = array("status"=>1);

echo json_encode($age);
    
}else{
echo "suc";
}

			//header('Content-type: application/json'); 
			
		//cho json_encode($response); 
			
		//	$this->sendtoapi();
}
	
	}	
	
	
	public function request(){
		
 
	
		
	if($_POST) {
		


	 $modem = $this->db->get_where('security_option',array('id' =>1))->row()->modem;
	 	$sqlr="SELECT * from sendflexi where (status='4' or status='0') and sent='0' and service!='2' and submitted_date < date_sub(now(), interval 3 minute) order by id desc limit 5"; 
	 
	$quer=$this->db->query($sqlr);
			
			if($quer->num_rows()> 0) {
	       	 $contact_no1 = $this->db->get_where('company',array('id' =>1))->row()->contact_no1;
				foreach ($quer->result() as $user_datam)
					{
		  $mup = "UPDATE `sendflexi` SET `sent`='1' WHERE `id`='$user_datam->id'"; 
		 
		$this->db->query($mup);
$details="Pending no: $user_datam->phone ($user_datam->pcode) Amount: $user_datam->balance";
			
			
		 $this->mdb->sms_send($contact_no1,$details);	
			 
					

					
			
				}
			    
			    
			}
	  $modem_pass = $this->db->get_where('company',array('id' =>1))->row()->pin;
	 $get=$this->input->get();
		$var=$this->input->post();
		$mpass = $var['pin'];
			$appreffer = $var['ref'];
		 if($modem_pass==$mpass){$job='1';}
		 
		 $job='1';
if(($modem==1) && ($job==1)){
  
	
					$m1 = $this->mdb->pcode($var['m1']);
			$m2 = $this->mdb->pcode($var['m2']);
		$m3 = $var['m3'];
			$m4 = $var['m4'];
		$m5 = $var['m5'];
			$m6 = $var['m6'];
		$m7 = $var['m7'];
			$m8 = $var['m8'];
	    if($m1=='GP' OR $m2=='GP' OR $m3=='GP'){
	      $sk='SK';  
	    }
	    
			$dt = new DateTime('now', new DateTimezone('Asia/Dhaka')); 
	$create_date=$dt->format('j F Y g:i A'); 

		$apiuserm = "Select * from modem where status='1' and (pcode='$m8' or pcode='$m7' or pcode='$m6' or pcode='$m5' or pcode='$m4' or pcode='$m3' or pcode='$m2' or pcode='$m1' or pcode='$sk') order by id asc limit 1"; 
		
			$queryrtsloadm=$this->db->query($apiuserm);
			
			if($queryrtsloadm->num_rows()> 0) {
	       	
				foreach ($queryrtsloadm->result() as $user_datam)
					{
		  $mup = "UPDATE `modem` SET `time`='$create_date' WHERE `id`='$user_datam->id'"; 
		 
		$this->db->query($mup);
}
}
		$result = array(); 


		
			$apiuser = "Select * from sendflexi where status='0' and local='0' and (route='modem' or route='') and (SUBSTR(pcode, 1, 2)='$m7' or SUBSTR(pcode, 1, 2)='$m6' or SUBSTR(pcode, 1, 2)='$m5' or SUBSTR(pcode, 1, 2)='$m4' or SUBSTR(pcode, 1, 2)='$m3' or SUBSTR(pcode, 1, 2)='$m2' or SUBSTR(pcode, 1, 2)='$m1' or SUBSTR(pcode, 1, 2)='$sk' or pcode='$m8') ORDER BY RAND() limit 1"; 
		
			$queryrtsload=$this->db->query($apiuser);
			
			if($queryrtsload->num_rows()> 0) {
	       	
				foreach ($queryrtsload->result() as $user_data)
					{
				if($user_data->resend_count==0){$title="Flexiload";}else{$title="Resend";} 
			if($user_data->pcode!='BILL' && $user_data->pcode!='SMS'){
        	$chk_data=$this->mdb->getData('modem',array('pcode'=>substr($user_data->pcode, 0,2)));
				}else{
				   	$chk_data=$this->mdb->getData('modem',array('pcode'=>$user_data->pcode)); 
				    
				}
				$mid=$chk_data[0]['id'];
				$pin=$chk_data[0]['pin'];
				$slot=$chk_data[0]['modem_port'];
				
			
					
					$chk_sim=$this->mdb->getData('siminfo',array('id_modem'=>$mid,'amount'=>$user_data->balance));
					if(empty($chk_sim)){
					$chk_sim=$this->mdb->getData('siminfo',array('id_modem'=>$mid));}
					
				$ussd=$chk_sim[0]['ussd'];
				
				

$vars = array(
  '%amount%'       => $user_data->balance,
  '%number%'        => $user_data->phone,
  '%pin%' => $pin
);



$usd= strtr($ussd, $vars);
if($user_data->pcode=='SMS' OR $user_data->pcode=='BILL'){
    $sms="1";
    $title="SMS";
    $pco="SMS";
    $smstext = $this->mdb->passwordChanger('decrypt', $user_data->smstext); 
   $smstext=  preg_replace('/%pin%/u', $pin,$smstext);
    
    
}else{
    $sms="0";
     $pco=$user_data->pcode;
    
}

if(empty($slot)){$slot="0";}
if(empty($chk_sim[0]['auto'])){$auto="0";}else{$auto=$chk_sim[0]['auto'];}
if(empty($chk_sim[0]['powerload'])){$powerload="0";}else{$powerload=$chk_sim[0]['powerload'];}

if(($user_data->pcode=='BILL') && empty($smstext)){
  $number='727';
  if($user_data->type==1){
      
  $smstext=$user_data->phone;
$id=strlen($user_data->phone);
 if($id<18){
$prevmonth = date('M', strtotime('-1 months'));
  $smstext="$user_data->phone $prevmonth";

}
  }else{ 
      $smstext="REGS $user_data->phone $user_data->name";
      
  } 
}else{$number=$user_data->phone;}

      if($user_data->remark=='offer'){$usd="*888*$number";}            
                  

						   $slid=$user_data->id;
					   $rendomid=$user_data->sid;
									if(!empty($appreffer)){
   
						$response = array( 
                             'powerload' => $powerload,
                          	'resend' => $user_data->resend_count,
						'id' => $user_data->id,
						'sid' => $user_data->sid,
						'userid' => $user_data->userid, 
						'pcode' => $pco,
						'number' => $number, 
						'balance' => $user_data->balance, 
						'service' => $user_data->service, 
						'status' => $user_data->status, 
						'type' => $user_data->type,
						'ussd' => $usd,
						'slot' => $slot,
							'sms' => $sms,
								'smstext' => $smstext,
						'title' => $title,
						'auto' => $auto,
						'line' => $chk_sim[0]['offer'],
							'triger' => $chk_sim[0]['triger'],
							'1st' =>  str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['a']),
								'2nd' =>  str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['b']),
									'3rd' => str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['c']),
										'4rd' => str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['d']),
											'5th' => str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['e'])
					); 
    
}else{		   
						$response[] = array( 
						'id' => $user_data->id,
						'sid' => $user_data->sid,
						'userid' => $user_data->userid, 
						'pcode' => $pco,
						'number' => $number, 
						'balance' => $user_data->balance, 
						'service' => $user_data->service, 
						'status' => $user_data->status, 
						'type' => $user_data->type,
						'ussd' => $usd,
						'slot' => $slot,
							'sms' => $sms,
								'smstext' => $smstext,
						'title' => $title,
						'auto' => $auto,
							'triger' => $chk_sim[0]['triger'],
							'1st' =>  str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['a']),
								'2nd' =>  str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['b']),
									'3rd' => str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['c']),
										'4rd' => str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['d']),
											'5th' => str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['e'])
					); 
				
				
					}	
		//array_push($result, $json_data); 
		if($user_data->pcode=='SMS' OR $user_data->type=='bill'){
		 $strignupt = "UPDATE `sendflexi` SET `status`='1', `local`='1' WHERE `id`='$slid'"; 
		}else{ $strignupt = "UPDATE `sendflexi` SET `status`='4', `local`='4' WHERE `id`='$slid'"; }
 $this->db->query($strignupt);
		 
		 
		 
            }
			}else {
						if(!empty($appreffer)){
				$response = array("msg" => 'Not Found', "status" => 4);
}else{$response[] = array("msg" => 'Not Found', "status" => 4);}
			}			
			
	}else{
	    	if(!empty($appreffer)){
	    	$response = array("msg" => 'modem off', "status" => 4);
	    	}else{	$response[] = array("msg" => 'modem off', "status" => 4);}
	}
			
			header('Content-type: application/json'); 
			
			echo json_encode($response); 
			
	

	}
	

	}	
}
