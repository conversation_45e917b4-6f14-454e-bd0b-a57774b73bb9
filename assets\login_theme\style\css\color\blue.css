.spinner,
.tp-loader.spinner0,
#fancybox-loading div {
    border-left: 3px solid rgba(74,162,209,.15);
    border-right: 3px solid rgba(74,162,209,.15);
    border-bottom: 3px solid rgba(74,162,209,.15);
    border-top: 3px solid rgba(74,162,209,.8);
}
a {
    color: #4aa2d1;
}
.nocolor:hover {
    color: #4aa2d1
}
.post-title a:hover {
    color: #4aa2d1
}
.main-title.text-center:after {
    color: #4aa2d1;
}
ul.circled li:before {
    color: #4aa2d1;
}
.meta a:hover,
.more:hover {
    color: #4aa2d1
}
footer a:hover {
    color: #4aa2d1 !important
}
.btn,
.vanilla-form label.custom-select span {
    background: #4aa2d1;
}
.btn:hover,
.btn:focus,
.btn:active,
.btn.active,
.pagination ul > li > a:hover,
.pagination ul > li > a:focus,
.pagination ul > .active > a,
.pagination ul > .active > span {
    background: #358dbc;
}
.navbar .navbar-nav > li > a:focus,
.navbar .nav > li > a:hover,
.navbar .nav > li.current > a {
    color: #4aa2d1 !important;
}
.navbar .dropdown-menu li a:hover,
.navbar .dropdown-menu li a.active {
    color: #4aa2d1 !important;
}
.navbar .nav .open > a,
.navbar .nav .open > a:hover,
.navbar .nav .open > a:focus {
    color: #4aa2d1;
}
.navbar .dropdown-menu > li > a:hover,
.navbar .dropdown-menu > li > a:focus,
.navbar .dropdown-submenu:hover > a,
.navbar .dropdown-submenu:focus > a,
.navbar .dropdown-menu > .active > a,
.navbar .dropdown-menu > .active > a:hover,
.navbar .dropdown-menu > .active > a:focus {
    color: #4aa2d1
}
.navbar .top-bar a:hover {
	color: #4aa2d1;
}
.yamm .yamm-content a:hover {
    color: #4aa2d1
}
.steps .icon {
    color: #4aa2d1;
}
.steps .steps-item .number {
    background: #4aa2d1;
}
.steps .steps-item:hover {
    border-color: #4aa2d1
}
.feature .icon {
    color: #4aa2d1;
}
.icon-large {
    color: #4aa2d1;
}
.owl-item .item:hover .box {
    box-shadow: 0 2px 0 #4aa2d1
}
.post-types .date-wrapper {
    background: #4aa2d1;
}
.post-types .post .format-wrapper {
    background: #4aa2d1;
}
.sidebox a:hover {
    color: #4aa2d1
}
.widget .post-list h5 a:hover {
    color: #4aa2d1
}
.widget .post-list .meta em a:hover {
    color: #4aa2d1
}
footer ul.tag-list li a:hover {
    background: #4aa2d1 !important
}
.testimonials2 .quote:hover {
    border: 2px solid #4aa2d1
}
.isotope-filter ul li a:hover,
.isotope-filter ul li a.is-checked {
    color: #4aa2d1
}
.isotope-filter ul li:after {
    color: #4aa2d1;
}
.price {
    color: #4aa2d1;
}
.progress-list li em {
    color: #4aa2d1;
}
.progress.plain .bar {
    background: #4aa2d1;
}
.bordered .progress.plain {
    border: 1px solid #4aa2d1;
}
.bordered .progress.plain .bar {
    background: #4aa2d1
}
.tabs-top.bordered .tab a {
    color: #4aa2d1;
    border: 2px solid #4aa2d1;
}
.tabs-top .tab a:hover,
.tabs-top .tab.active a {
    color: #4aa2d1;
    box-shadow: 0 2px 0 #4aa2d1;
}
.tabs-top.bordered .tab a:hover,
.tabs-top.bordered .tab.active a {
    border: 2px solid #4aa2d1;
    background: #4aa2d1;
}
.tabs-bottom .tab:hover .icon.box,
.tabs-bottom .tab.active .icon.box {
    box-shadow: 0 2px 0 #4aa2d1
}
.panel-group .panel-active a,
.panel-group .panel-title > a:hover {
    color: #4aa2d1
}
.panel-group .panel-heading .panel-title:hover,
.panel-group .panel-active .panel-heading .panel-title {
    box-shadow: 0 2px 0 #4aa2d1;
    color: #4aa2d1;
}
.bordered .panel-heading .panel-title {
    color: #4aa2d1;
    border: 2px solid #4aa2d1;
}
.bordered .panel-heading .panel-title:hover {
    background: #4aa2d1;
    border: 2px solid #4aa2d1;
}
.bordered .panel-title > a {
    color: #4aa2d1
}
.bordered .panel-title:hover,
.bordered .panel-active .panel-heading .panel-title,
.bordered .panel-active .panel-heading .panel-title:hover {
    border: 2px solid #4aa2d1;
    background: #4aa2d1;
}
.tooltip-inner {
    background-color: #4aa2d1;
}
.tooltip.top .tooltip-arrow,
.tooltip.top-left .tooltip-arrow,
.tooltip.top-right .tooltip-arrow {
    border-top-color: #4aa2d1
}
.tooltip.right .tooltip-arrow {
    border-right-color: #4aa2d1
}
.tooltip.left .tooltip-arrow {
    border-left-color: #4aa2d1
}
.tooltip.bottom .tooltip-arrow,
.tooltip.bottom-left .tooltip-arrow,
.tooltip.bottom-right .tooltip-arrow {
    border-bottom-color: #4aa2d1
}
.vanilla-form input[type="radio"]:focus + span,
.vanilla-form input[type="checkbox"]:focus + span,
.vanilla-form input[type="radio"]:active + span,
.vanilla-form input[type="checkbox"]:active + span {
    border-color: #4aa2d1
}
.vanilla-form input[type="radio"] + span::after {
    background-color: #4aa2d1;
    border-color: #4aa2d1;
}
.vanilla-form input[type="radio"]:checked + span,
.vanilla-form input[type="checkbox"]:checked + span {
    border: 2px solid #4aa2d1
}
.vanilla-form input[type="checkbox"] + span::after {
    border: 0 solid #4aa2d1;
}
#comments .info h2 a:hover {
    color: #4aa2d1
}
#comments a.reply-link:hover {
    color: #4aa2d1
}
#contact-info-button:hover,
#btn-close-modal:hover {
    color: #4aa2d1
}
@media (max-width: 991px) { 
	.navbar .navbar-nav .open .dropdown-menu > li > a:hover,
	.navbar .navbar-nav .open .dropdown-menu > li > a:focus {
	    color: #4aa2d1 !important
	}
}
@media (max-width: 767px) { 
	.isotope-filter ul li a:hover,
	.isotope-filter ul li a.is-checked {
	    box-shadow: 0 2px 0 #4aa2d1 !important
	}
}