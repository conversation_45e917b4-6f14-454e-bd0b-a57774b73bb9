<?php
use <PERSON>enon\NagadApi\Helper;
use Xenon\NagadApi\Base;
defined('BASEPATH') OR exit('No direct script access allowed');

class Paymentgatway extends CI_Controller {

	function __construct()
    {
		parent::__construct();
		$this->load->library('form_validation');
		$this->load->library('user_agent');
		date_default_timezone_set('Asia/Dhaka');
		require_once APPPATH."/third_party/nagad/vendor/autoload.php";


	}


	 public function index($id="",$source="") {


	     if($_POST){

	         $amount = $this->security->xss_clean($this->input->post('amount'));
	if(!empty($amount)){

	       redirect("/Paymentgatway/checkout/$id/$amount/$source", 'refresh');
	}


	     }



	 	$data['page_name'] = 'amount';
			$data['id'] = $id;
		$this->load->view('checkout/index',$data);

	 }


	 public function checkout($id="",$amount="",$source="") {

	 	$data['page_name'] = 'checkout';
			$data['id'] = $id;
			$data['amount'] = $amount;
				$data['source'] = $source;
		$this->load->view('checkout/index',$data);

	 }




			public function Nagad($id="",$amount="",$source=""){
			   		$payment_ref_id = $this->security->xss_clean($this->input->get('payment_ref_id'));

			     $checkoutv=$this->mdb->getData('gateway',array('id'=>3));

			    if($checkoutv[0]['type']==0){
			        $type="development";

			    }else{
			        $type="production";}



			   $config = [
    'NAGAD_APP_ENV' => $type, //development|production
    'NAGAD_APP_LOG' => '1',
    'NAGAD_APP_MERCHANTID' => $checkoutv[0]['userid'], //demo
    'NAGAD_APP_MERCHANT_PRIVATE_KEY' => $checkoutv[0]['private_key'],
    'NAGAD_APP_MERCHANT_PG_PUBLIC_KEY' => $checkoutv[0]['public_key'],
    'NAGAD_APP_TIMEZONE' => 'Asia/Dhaka',
];
$invoice=Helper::generateFakeInvoice(15, true);
$nagad = new Base($config, [
    'amount' => $amount,
    'invoice' =>$invoice ,
    'merchantCallback' => "".base_url()."Paymentgatway/Nagad/",
]);
if(empty($payment_ref_id)){




$paymentUrl = $nagad->payNowWithoutRedirection($nagad);
if (filter_var($paymentUrl, FILTER_VALIDATE_URL) === FALSE) {
	$this->session->set_flashdata('error', $paymentUrl);
   // echo"<script>alert($id)</script>";
 redirect("Paymentgatway/checkout/$id/$amount/$source", 'refresh');
}else{

    	$xid = $this->db->get_where('reseller',array('username' =>$id))->row()->id;

    	$minsert= array('order_number'=>$invoice,
							'uid'=>$xid,
							'time'=>date('Y-m-d H:i:s'),
							'status'=>0,
							'amount'=>$amount,
							'source'=> $source
							);
		$this->mdb->insert('invoice',$minsert);

    	redirect($paymentUrl, 'refresh');


}

//print_r($paymentUrl);
}else{

$helper = new Helper($config);
$response = $helper->verifyPayment($_GET['payment_ref_id']);

$apists=json_decode($response);

				$status = $apists->status;
if($status=='Success'){
	$invoice_r = $apists->orderId;
	$amount = $apists->amount;
    $order_info=$this->mdb->getData('invoice',array('order_number'=>$invoice_r));
    if($order_info[0]['status']=='0' && $order_info[0]['amount']==$amount){
        $uid=$order_info[0]['uid'];
    	$routeupdate = array('status' =>1,'response' =>$response);
	$this->mdb->update('invoice',$routeupdate,array('order_number'=>$invoice_r));

		$this->balance_upadte_from($order_info[0]['amount'],$order_info[0]['source'],$uid,'NAGAD',$invoice_r);


	echo"balnce_added_success_full";

    }else{redirect("Paymentgatway/checkout/$id/$amount/$source", 'refresh');   }

}else{

 redirect("Paymentgatway/checkout/$id/$amount/$source", 'refresh');


}

//print_r($response);
}


			}

	public function bKash($id="",$amount="",$source="",$invoice=""){
	    if(empty($invoice) && empty($_REQUEST['paymentID'])){
	        $invoice=Helper::generateFakeInvoice(15, true);
	        redirect("/Paymentgatway/bKash/$id/$amount/$source/$invoice", 'refresh');
	        exit;
	    }
	    $checkoutv=$this->mdb->getData('gateway',array('id'=>1));
	    if($checkoutv[0]['fnc_rate']==2){

	       $username=$checkoutv[0]['userid']; //demo
    $app_secret= $checkoutv[0]['private_key'];
   $app_key= $checkoutv[0]['public_key'];
	 $password= $checkoutv[0]['fnc_number'];


	       $token= $this->bkash_token($username,$password,$app_key,$app_secret);



   $header= array(
            'Content-Type:application/json',
            'Authorization:' .$token,
            'X-APP-Key:'.$app_key
        );
       //print_r($_REQUEST);
       if($_REQUEST['paymentID']){
        if($_REQUEST['status']=='success'){
     $resultdatax= $this->execute_token($_REQUEST['paymentID'],$header);
        }

        if(empty($resultdatax)){redirect("Paymentgatway/checkout/$_REQUEST[id]/$_REQUEST[taka]/$_REQUEST[source]?error=Status $_REQUEST[status]", 'refresh');  }
     $p_git = json_decode($resultdatax);
$status=$p_git->transactionStatus;
$invoice_r=$p_git->merchantInvoiceNumber;
$statusMessage=$p_git->statusMessage;
$amount=$p_git->amount;
$order_info=$this->mdb->getData('invoice',array('order_number'=>$invoice_r));
     $uid=$order_info[0]['uid'];
if($status=='Completed'){

	$routeupdate = array('status' =>1,'response' =>$resultdatax);
	$this->mdb->update('invoice',$routeupdate,array('order_number'=>$invoice_r));

		$this->balance_upadte_from($amount,$order_info[0]['source'],$uid,'bKash',$invoice_r);

	echo"balnce_added_success_full";

}else{
   //$this->session->set_flashdata('error', $statusMessage);
  redirect("Paymentgatway/checkout/$_REQUEST[id]/$_REQUEST[taka]/$_REQUEST[source]?error=$statusMessage", 'refresh');

}




    } else {
        $url=$this->create_token($amount,$header,$invoice,$id,$source);
        redirect($url, 'refresh');

   }


	    }else{


	    $data['type'] = $checkoutv[0]['type'];
	    	$data['page_name'] = 'bkash';
			$data['invoice'] = $invoice;
			$data['id'] = $id;
			$data['amount'] = $amount;
				$data['source'] = $source;
		$this->load->view('checkout/index',$data);
	    }




			}







public function balance_upadte_from($amount,$source,$userid,$sender,$invoice_r){

    $xsender=$sender;
     $invoice_r = $this->mdb->passwordChanger('encrypt', $invoice_r);
    $msg = $this->mdb->passwordChanger('encrypt', 'From gateway');
 if(!empty($userid)) {
if(empty($source))$source='main';
$balance_system = $this->db->get_where('security_option',array('id' =>1))->row()->balance_system;

$drive_system = $this->db->get_where('security_option',array('id' =>1))->row()->drive;

$ctype =$this->db->get_where('reseller',array('id' =>$userid))->row()->custype;


$uid= $userid;
$idate=date('Y-m-d');
$ip = $_SERVER['REMOTE_ADDR'];

		$create_date=date('j F Y g:i A');






    $t="plus";

		$idate=date('Y-m-d');
		$create_date=date('j F Y g:i A');


	$obal=$this->mit->accountBalance($userid);


		if($sender=='16216'){
$sender="rocket";
}

if($source=='bank')$sender='b_'.$sender;
if($source=='drive')$sender='d_'.$sender;


	$sql_rate="SELECT * from level_list where name='$ctype'";
		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{
				$p=$raterow->$sender;

}




$com=(($p/100)*$amount);
$tamount=($amount+$com);
             //if($source=='drive'){$tamount=$amount;}
  	$this->mit->balanceUpdate($userid, $tamount,$t,$source);
  		$bal=$this->mit->accountBalance($userid);

  	// Send notification to user
  	$this->mit->send_balance_notification($userid, $tamount, "plus", $source);

  	 $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `service`, `flex_id`, `p_id`, `date`, `time`) VALUES (NULL, '$userid', 'By auto $number in $source', '$obal', '0', '$tamount', '$bal', 'plus', 'transfer', '$number', '', '$idate', '$create_date')";

		 $this->db->query($sql_tr);

  	$sql="INSERT INTO `pay_receive` (`id`, `userid`, `desc`, `remark`, `sender`, `credit`, `account`, `type`, `transfer_type`, `idate`, `date`) VALUES (NULL, '$userid', 'auto', 'By auto $number in $source', '$userid', '$tamount', '$bal', 'receive', 'recharge', '$idate', '$create_date');";

		$this->db->query($sql);

$time=time();
			$sql = "INSERT INTO `trnx` (`time`,`amount`,`type`,`cat`,`sender`,`trnx`,`add_date`,`msg`,`userid`) VALUES ('$time','$amount','$xsender','$xsender','$xsender','$invoice_r','$create_date','$msg','$userid')";

		$this->db->query($sql);


		if($source=='drive' && $drive_system==1){
 $queryi = "UPDATE `trnx` SET `status`='1' where trnx='$invoice_r'";
			$this->db->query($queryi);
}

	if($source=='bank' && $balance_system==1){
           $queryi = "UPDATE `trnx` SET `status`='1' where trnx='$invoice_r'";
			$this->db->query($queryi);
        }
        if($source=='main'){
           $queryi = "UPDATE `trnx` SET `status`='1' where trnx='$invoice_r'";
			$this->db->query($queryi);
        }




		$set = $this->db->get_where('security_option',array('id' =>1))->row()->com_system;
		 if($source=='main' && $set!=2){

		$this->mdb->comupdate($userid,$amount,$sender,$source);


        }

		 if($source=='bank' && $set!=2 && $com > 0){

		$this->mdb->comupdate_bank($userid,$amount,$sender,$source);


        }


        if($source=='drive' && $set!=2 && $com > 0){

		$this->mdb->comupdate_bank($userid,$amount,$sender,$source);


        }








	}







}












 public function createpayment($invoice="")
    {

$checkoutv=$this->mdb->getData('gateway',array('id'=>1));

			    if($checkoutv[0]['type']==0){
			        $createURL="https://checkout.sandbox.bka.sh/v1.2.0-beta/checkout/payment/create";

			    }else{
			       $createURL="https://checkout.pay.bka.sh/v1.2.0-beta/checkout/payment/create";

			    }

     $token = $this->db->get_where('invoice',array('order_number'=>$invoice))->row()->note;
     $amount = $this->db->get_where('invoice',array('order_number'=>$invoice))->row()->amount;


 // must be unique
$intent = "sale";

    $createpaybody=array('amount'=>$amount, 'currency'=>'BDT', 'merchantInvoiceNumber'=>$invoice,'intent'=>$intent);
    $url = curl_init($createURL);

    $createpaybodyx = json_encode($createpaybody);

    $header=array(
        'Content-Type:application/json',
        'authorization:'.$token,
        'x-app-key:'.$checkoutv[0]['public_key']
    );

    curl_setopt($url,CURLOPT_HTTPHEADER, $header);
	curl_setopt($url,CURLOPT_CUSTOMREQUEST, "POST");
	curl_setopt($url,CURLOPT_RETURNTRANSFER, true);
	curl_setopt($url,CURLOPT_POSTFIELDS, $createpaybodyx);
    curl_setopt($url,CURLOPT_FOLLOWLOCATION, 1);

    $resultdata = curl_exec($url);
    curl_close($url);
    echo $resultdata;
    // file_put_contents("createpayment.txt", $resultdata);

    }


  public function executepayment($invoicex="",$paymentID="")
    {
         $checkoutv=$this->mdb->getData('gateway',array('id'=>1));

			    if($checkoutv[0]['type']==0){
			        $executeURL="https://checkout.sandbox.bka.sh/v1.2.0-beta/checkout/payment/execute/";

			    }else{
			       $executeURL="https://checkout.pay.bka.sh/v1.2.0-beta/checkout/payment/execute/";

			    }
     //$strJsonFileContents = file_get_contents("bk/config.json");
     $token = $this->db->get_where('invoice',array('order_number'=>$invoicex))->row()->note;



$url = curl_init($executeURL.$paymentID);

$header=array(
    'Content-Type:application/json',
    'authorization:'.$token,
    'x-app-key:'.$checkoutv[0]['public_key']
);

curl_setopt($url,CURLOPT_HTTPHEADER, $header);
curl_setopt($url,CURLOPT_CUSTOMREQUEST, "POST");
curl_setopt($url,CURLOPT_RETURNTRANSFER, true);
curl_setopt($url,CURLOPT_FOLLOWLOCATION, 1);

$resultdatax=curl_exec($url);
curl_close($url);
echo $resultdatax;
//file_put_contents("executepayment.txt", $resultdatax);
$p_git = json_decode($resultdatax);
$status=$p_git->transactionStatus;
$invoice_r=$p_git->merchantInvoiceNumber;
$statusMessage=$p_git->statusMessage;
$amount=$p_git->amount;
$order_info=$this->mdb->getData('invoice',array('order_number'=>$invoice_r));
     $uid=$order_info[0]['uid'];
if($status=='Completed'){

	$routeupdate = array('status' =>1,'response' =>$resultdatax);
	$this->mdb->update('invoice',$routeupdate,array('order_number'=>$invoice_r));

		$this->balance_upadte_from($amount,$order_info[0]['source'],$uid,'bKash',$invoice_r);



}






    }

    public function bkash_complete()
    {
        echo"balnce_added_success_full";
        }

  public function token($id="",$amount="",$source="",$invoice="")
    {







$request_token=$this->bkash_Get_Token();
$idtoken=$request_token['id_token'];

//$_SESSION['token']=$idtoken;
//$strJsonFileContents = file_get_contents("bk/config.json");
//$array = json_decode($strJsonFileContents, true);

//$array['token']=$idtoken;

//$newJsonString = json_encode($array);
//file_put_contents('bk/config.json',$newJsonString);


$xid = $this->db->get_where('reseller',array('username' =>$id))->row()->id;

    	$minsert= array('order_number'=>$invoice,
							'uid'=>$xid,
							'time'=>date('Y-m-d H:i:s'),
							'status'=>0,
							'amount'=>$amount,
							'source'=> $source,
							'note' =>$idtoken
							);
		$this->mdb->insert('invoice',$minsert);

echo $idtoken;
/*
header('Content-type: application/json');
 $age = array("token"=>$idtoken,"orderid"=>$invoice);

echo json_encode($age);
*/



    }




public function bkash_Get_Token(){

 $checkoutv=$this->mdb->getData('gateway',array('id'=>1));

			    if($checkoutv[0]['type']==0){
			        $tokenURL="https://checkout.sandbox.bka.sh/v1.2.0-beta/checkout/token/grant";

			    }else{
			       $tokenURL="https://checkout.pay.bka.sh/v1.2.0-beta/checkout/token/grant";

			    }


			     $username=$checkoutv[0]['userid']; //demo
    $app_secret= $checkoutv[0]['private_key'];
   $app_key= $checkoutv[0]['public_key'];
	 $password= $checkoutv[0]['fnc_number'];




	$post_token=array(
        'app_key'=>$app_key,
		'app_secret'=>$app_secret
	);

    $url=curl_init($tokenURL);

	$posttoken=json_encode($post_token);
	$header=array(
		'Content-Type:application/json',
		'password:'.$password,
        'username:'.$username
    );

    curl_setopt($url,CURLOPT_HTTPHEADER, $header);
	curl_setopt($url,CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($url,CURLOPT_RETURNTRANSFER, true);
	curl_setopt($url,CURLOPT_POSTFIELDS, $posttoken);
	curl_setopt($url,CURLOPT_FOLLOWLOCATION, 1);

	$resultdata=curl_exec($url);
	curl_close($url);
//file_put_contents("token.txt", $resultdata);

	return json_decode($resultdata, true);
}





public function curlWithBody($url,$header,$method,$body_data_json){

    $checkoutv=$this->mdb->getData('gateway',array('id'=>1));

    if($checkoutv[0]['type']==0){

 $base_url = 'https://tokenized.sandbox.bka.sh/v1.2.0-beta';
}else{
 $base_url = 'https://tokenized.pay.bka.sh/v1.2.0-beta';
}
        $curl = curl_init($base_url.$url);
        curl_setopt($curl,CURLOPT_HTTPHEADER, $header);
        curl_setopt($curl,CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($curl,CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl,CURLOPT_POSTFIELDS, $body_data_json);
        curl_setopt($curl,CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($curl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
        $response = curl_exec($curl);
        curl_close($curl);
        return $response;
    }

    public function bkash_token($user,$pass,$appkey,$app_secret)
    {



         $header = array(
                'Content-Type:application/json',
                'username:'.$user,
                'password:'.$pass
                );


        $body_data = array('app_key'=> $appkey,
        'app_secret'=>$app_secret);
        $body_data_json=json_encode($body_data);

       $response = $this->curlWithBody('/tokenized/checkout/token/grant',$header,'POST',$body_data_json);
   $token = json_decode($response)->id_token;
        return $token;
    }


  public function create_token($amount,$header,$invoice,$id,$source)
    {

       $xid = $this->db->get_where('reseller',array('username' =>$id))->row()->id;

    	$minsert= array('order_number'=>$invoice,
							'uid'=>$xid,
							'time'=>date('Y-m-d H:i:s'),
							'status'=>0,
							'amount'=>$amount,
							'source'=> $source,
							'note' =>''
							);
		$this->mdb->insert('invoice',$minsert);


         $body_data = array(
            'mode' => '0011',
            'payerReference' => ' ',
            'callbackURL' =>  "".base_url()."Paymentgatway/bKash/?id=$id&source=$source&taka=$amount",
            'amount' => $amount,
            'currency' => 'BDT',
            'intent' => 'sale',
            'merchantInvoiceNumber' => $invoice
        );
        $body_data_json=json_encode($body_data);

       $response = $this->curlWithBody('/tokenized/checkout/create',$header,'POST',$body_data_json);

       $bkashURL = json_decode($response)->bkashURL;

        return $bkashURL;
    }

  public function execute_token($paymentID,$header)
    {



        $body_data = array(
            'paymentID' => $paymentID
        );
        $body_data_json=json_encode($body_data);

        $response =  $this->curlWithBody('/tokenized/checkout/execute',$header,'POST',$body_data_json);


        return $response;
    }









}
