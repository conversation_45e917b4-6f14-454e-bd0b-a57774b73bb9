<?php

include 'asik1122/connection.php';

$gateway = $_POST["Gateway"];
$amount = $_POST["Amount"];
$sender = $_POST["Sender"];
$trxid = $_POST["TrxID"];

$data = [
    'Gateway' => $gateway,
    'Amount' => $amount,
    'Sender' => $sender,
    'TrxID' => $trxid
];


$url = 'https://allsimoffer.top/gateway/add54Transaction';


$curl = curl_init($url);


curl_setopt($curl, CURLOPT_POST, true);
curl_setopt($curl, CURLOPT_POSTFIELDS, $data);


curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);


$response = curl_exec($curl);


if (curl_errno($curl)) {
    $error = curl_error($curl);
} else {
    echo $response;
}

curl_close($curl);



// $insert = "INSERT INTO trnx (trnx, amount, time, sender, type) VALUES ('$trxid', '$amount', NOW(), '$sender', '$gateway')";
//     if ($conn->query($insert) === TRUE) {
//         echo "New Payment Added";
//     } else {
//         echo "Add Payment Failed: " . $conn->error;
//     }
    
// $conn->close();
?>