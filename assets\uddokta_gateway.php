<?php
// Include the database connection information
include 'mydata.php';

$query = "SELECT * FROM uddokta_pay";
$result = mysqli_query($conn, $query);

if ($result) {
    $row = mysqli_fetch_assoc($result);

    // Check if the row is not empty
    if ($row) {
        $response = array(
            "success" => true,
            "api_key" => $row['api_key'],
            "checkout_url" => $row['checkout_url'],
            "verify_payment_url" => $row['verify_payment_url'],
            "redirect_url" => $row['redirect_url'],
            "cancel_url" => $row['cancel_url'],
            "code" => $row['code']
        );

        echo json_encode($response);
    } else {
        // If no rows were found
        echo json_encode(array("success" => false, "message" => "No data found"));
    }
} else {
    // If there was an error in the query
    echo json_encode(array("success" => false, "message" => "Error in query"));
}
?>
