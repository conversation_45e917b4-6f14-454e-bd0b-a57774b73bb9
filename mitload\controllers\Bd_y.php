<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Bd_y extends CI_Controller {
	
	
	
		function __construct()
    {
        parent::__construct();
        
        $sessionid=$_SESSION['sessionidadmin'];
		
		if($sessionid!=""){
		redirect('admin', 'refresh');
		}
		
		/*
		
      if ($this->session->userdata('admin_login') == "yes") {

        	redirect('admin', 'refresh');
        }
        */
        date_default_timezone_set('Asia/Dhaka');
     
        //$this->load->model('admin');
		$this->load->library('form_validation');
		$this->load->library('user_agent');
    }
	
	 private function date_time(){
        //$timezone_offset = +6; // BD central time (gmt+6) for me
		//date_default_timezone_set("Asia/Dhaka");
        return $create_date = date('Y-m-d H:i:s');
     }
	 

	protected function rules() {
		$rules = array(
				 array(
					'field' => 'username',
					'label' => "Username",
					'rules' => 'trim|required|min_length[3]|max_length[40]|xss_clean'
				),
				array(
					'field' => 'password',
					'label' => "Password",
					'rules' => 'trim|required|max_length[40]|xss_clean'
				)
			);
		return $rules;
	}
	
	
	public function index()
	{
		
	if($_POST) {
	$this->form_validation->set_rules('adminuser', 'Userid', 'trim|required|min_length[5]|max_length[30]');
	
    if ($this->form_validation->run() == FALSE) {
	$this->session->set_flashdata('error', 'Please Enter Correct Login Info');
	$data['page_name'] = 'login';
	$this->load->view('admin/login/admin_login'); 
	}else {	
	$username = $this->security->xss_clean($this->input->post('adminuser'));
	if(!empty($username) ) {
	$result = $this->mdb->adminlogin($username);
	if ($result) {
	$this->load->view('admin/login/admin_pass');
	}else {
	$this->session->set_flashdata('error', 'Userid Not found');
	$this->load->view('admin/login/admin_login');
	}
	}
	}
	
	
	}else {
	$this->load->view('admin/login/admin_login');
		
	}
			
	}
	
	public function AdminAccess() { 
	
	session_start();
	
	if($_POST) {
	$this->form_validation->set_rules('adminuser', 'Userid', 'trim|required|min_length[3]|max_length[30]');
	$this->form_validation->set_rules('password', 'Password', 'trim|required|min_length[4]|max_length[30]');
   
     
        if ($this->form_validation->run() == FALSE) {
		$this->session->set_flashdata('admin_pass_error', 'Please Enter Correct Login Info');
		$this->load->view('admin/login/admin_pass');
		  //redirect('website', 'refresh');
		}else {
			
	$username = $this->security->xss_clean($this->input->post('adminuser'));
	$password = $this->security->xss_clean($this->input->post('password'));

	$tokencode = $this->security->get_csrf_hash();
	
	if(!empty($username) && !empty($password) ) {
	 
        $result = $this->mit->login($username,$password);
		 if ($result) {

		 $ucidtype=$result->custype;
		 $ucid=$result->id;
		 $logincount=$result->login_count;
		 $enbaleotp=$result->enbale_otp;
		  $choseotp=$result->otp_choice;
		 $uemail=$result->email;
		  $umobile=$result->mobile;
		 $activests =$result->status;

		 if($ucidtype=="admin"){
		 
		 $loginplus = $logincount+1;
		 
		 $queryalowdev= $this->db->query("select * from device_list where tokenid='$tokencode' and userid='$ucid'");
		 
		 
		 
		 if($logincount>0 or $queryalowdev->num_rows() > 0){
			 
		 	$access='no';
			
		 }else {
			 
		 	$access='yes';

		 }

		 $sqlcont="SELECT * FROM device_list WHERE tokenid='$tokencode' and userid='$ucid'";

		$querycount = $this->db->query($sqlcont);
		
		foreach ($querycount->result() as $row3){
		  	 $mysafe_status=$row3->status;
		  	  $myfsttoken=$row3->tokenid;
		  	   $tblremember=$row3->remember;
		  	  
		  	  }
		  
		 if($mysafe_status==1 || $access=='yes') {
		
		 $otppas=rand(111111,999999); 
		 
		 $hashotp = $this->mdb->generate($otppas); 
		
		 if($activests==0) { 
		 $this->session->set_flashdata('error', 'This ID is Block.');
		 $data['page_name'] = 'login';
		 $this->load->view('login');
		 }else {
			 
		$_SESSION['sessionidadmin']=$ucid; 
			 
		 $loginupdate = array(
						"ip" => $_SERVER['REMOTE_ADDR'],
						"login_count" => $loginplus,
						"last_login" => $this->date_time()
					);
		 
		 $this->mdb->update('reseller',$loginupdate,array('id'=>$ucid));
		 	
		 
		$ctype="admin";
		$secretcode=sha1( uniqid(rand(),true)); 

		if($enbaleotp==1) {	
		 $otpupdate = array(
						"otp" => $hashotp
					);
		 
		 $this->mdb->update('reseller',$otpupdate,array('id'=>$ucid));
		 
		   if($choseotp==3)$to=$uemail;
			       if($choseotp==4)$to=$umobile;
			      $subject="$system_name OTP CODE $otppas";
			      $msg="$system_name Dear $uname, Your OTP code is $otppas from ip $_SERVER[REMOTE_ADDR]";
			     
			      if($choseotp==3 or $choseotp==4){
			  $code=$this->mit->mail_send($to, $subject, $msg);
			      }
		 
		 
		}

		$this->mit->insertLog($ucid,$ctype,$secretcode);
		
		
		// admin session start
		$this->session->set_userdata('admin_login', 'yes');
		$this->session->set_userdata('admin_session', $result);
		
		 $sess_data = array(
               'secretcode' => $secretcode,
               'uid'     => $ucid
               
             );
		$this->session->set_userdata('securlog',$sess_data);
		
		
		
		$msg= 'Successful Login';
		$this->mit->InsertActivetUser($ucid,$msg);

		$this->mit->clear_login_attempts($username);



		if($querycount->num_rows() == 0 ) {
		
		$referrer = $this->session->userdata('user_requested_page') ? $this->session->userdata('user_requested_page') : 'adminotp';

	}else if($tblremember==0){
		$referrer = $this->session->userdata('user_requested_page') ? $this->session->userdata('user_requested_page') : 'adminotp';

	}else {

		$referrer = $this->session->userdata('user_requested_page') ? $this->session->userdata('user_requested_page') : 'admin';


	}
          
          redirect($referrer);

		

      }

      }else {
		  
		  $remember=0;

      	$this->mit->devicelist($ucid,$remember,'yes');
      	$msg= 'Access Denai';
		$trytype = 'password';
		
		$this->mit->increase_login_attempts($username,$password,$msg,$trytype);
		
      	$this->session->set_flashdata('success', 'Access dinai Please Contact Your Hosting Company');
		
		//$data['page_name'] = 'device_set_with_number';
		//$this->load->view('device_set_with_number');
		 
		 
       redirect('Bd_y', 'refresh');

      }
      
      }// only Admin login
		
		} else {
		
		//$this->mit->InsertActivetUser($username,$msg);
		$msg= 'Invalid ID and password';
		$trytype = 'password';
		
		$this->mit->increase_login_attempts($username,$password,$msg,$trytype);
		
		 $this->session->set_flashdata('admin_pass_error', 'Invalid ID and password.');
		 
		$this->load->view('admin/login/admin_pass');
		 }
		 
		 }else { // empty user and pass chaking
		  $this->session->set_flashdata('error', 'Please enter ID and password.');
		  redirect('Bd_y', 'refresh');
		 }
		}
		}else {
		redirect('Bd_y', 'refresh');	
		}
		
		
	}
	
	public function numberserify(){
		
		
		
		$var = $this->input->post();
		
		if($_POST) {
			
		$mobile=$var['mobile'];
			
		$queryalowip= $this->db->query("select * from sadmin_phone where uid='1' and phone='$mobile'");
	
		if($queryalowip->num_rows() > 0 ) {
			
		$api_url='sms.sayedkhan.info';
		$api_user='irecharge';
		$api_key='17IUU9Z5RA94S30F22A7LZSS2EI7MWY4N1G63T2COQ56LCLWSS';
	
		
		$rendcode = rand(111111,999999);
		
		$mesg="Your Verification Code For Device Allow ".$rendcode;
		
		$domain=$_SERVER['HTTP_HOST'];
		$domain = str_replace(array('www.'), array(''), $domain);
		
		$postdata = array(
				  "apikey" => $api_key,
				  "username" => $api_user,
				  "mobile" => $mobile,
				  "msg" => $mesg,
				  "domain" => $domain
				);
		
		$urlsend ="http://".$api_url."/smsapi/smsrequest";
			
		$api_status = $this->mdb->sendPostData($urlsend,$postdata);
				
		$apidetails=json_decode($api_status);
		$Apistatus=$apidetails->status; 
		
		//if($Apistatus==1) {
				$serimobiledata = array(
						   'sendmobile' => $mobile,
						   'sendcode'     => $rendcode
						 );
						 
		$bqdev="UPDATE `sadmin_phone` SET `scode` = '$rendcode',`sendcount` = sendcount+1 WHERE `sadmin_phone`.`phone` = '$mobile'"; 

		$blsup = $this->db->query($bqdev);
			
		$this->session->set_userdata('mobilechk',$serimobiledata);
		
		
		
		$this->load->view('devcie_set_number_verify');
		//}else {
		
		//$this->load->view('device_set_with_number');
		//}
		
			
		}else {
		$this->session->set_flashdata('error', 'Invalid Number');
		$this->load->view('device_set_with_number');	
		}
			
		}else {
			
		redirect('Bd_y', 'refresh');
		
		}
		
	}
	
	
	public function numberserifyfinal(){
		
		$var = $this->input->post();
		
		if($_POST) {
			
		$mobile=$var['mobile'];
		$code=$var['code'];
			
		$queryalowip= $this->db->query("select * from sadmin_phone where phone='$mobile'");
	
		if($queryalowip->num_rows() > 0 ) {
		
		$getverydata= $this->session->userdata('mobilechk');
		$vmobile = $getverydata['sendmobile'];
		$getcode = $getverydata['sendcode'];
		
		$getcode = $this->db->get_where('sadmin_phone',array('phone' =>$mobile))->row()->scode;
		if($getcode==$code) {
		
		$tokencode = $this->security->get_csrf_hash();
			
		$bqdev="UPDATE `device_list` SET `status` =1 WHERE `device_list`.`tokenid` = '$tokencode'"; 

		$blsup = $this->db->query($bqdev);
			
		$this->session->unset_userdata('mobilechk');
		
		$this->session->set_flashdata('success', 'Your Device Successfully Allow');
		
		redirect('Bd_y', 'refresh');	
			
		}else {
		$this->session->set_flashdata('error', 'Invalid Code');
		$this->load->view('devcie_set_number_verify');
			
		}
		
		}else {
		
		$this->session->set_flashdata('error', 'Invalid Number');
		$this->load->view('device_set_with_number');	
		
		}
			
		}else {
		redirect('Bd_y', 'refresh');	
		}
		
		
	}// function ses
	
	
	
	public function forgot(){
	
		
		
		if($_POST) {
			$var = $this->input->post();
		$mobile=$var['mobile'];
		
		$stype=$var['stype'];
			
		$queryalowip= $this->db->query("select * from sadmin_phone where uid='1' and phone='$mobile'");
	
		if($queryalowip->num_rows() > 0 ) {
			
		$api_url='sms.sayedkhan.info';
		$api_user='irecharge';
		$api_key='17IUU9Z5RA94S30F22A7LZSS2EI7MWY4N1G63T2COQ56LCLWSS';
	
		
		$newpassword =  uniqid();
		
		$newpin = rand(1111,9999);
		
		$spass = $this->mdb->generate($newpassword);
		$spin = $this->mdb->generate($newpin);
		
		if($stype=="pass") {
			
		$mesg="Your New Password  ".$newpassword." And New PIN ".$newpin;
		$bqdev="UPDATE `reseller` SET `password` = '$spass', `pincode` = '$spin',`login_allow` = 3 WHERE `reseller`.`id` = '1'";
		$blsup = $this->db->query($bqdev);
		
		}else {
			
		$mesg="Your OTP Disable New Password  ".$newpassword." And New PIN ".$newpin;
		$bqdev="UPDATE `reseller` SET `password` = '$spass', `pincode` = '$spin', `enbale_otp` = '0', `login_allow` = 3 WHERE `reseller`.`id` = '1'"; 
		$blsup = $this->db->query($bqdev);
		
		}
		
		
		$domain=$_SERVER['HTTP_HOST'];
		$domain = str_replace(array('www.'), array(''), $domain);
		
		$postdata = array(
				  "apikey" => $api_key,
				  "username" => $api_user,
				  "mobile" => $mobile,
				  "msg" => $mesg,
				  "domain" => $domain
				);
		
		$urlsend ="http://".$api_url."/smsapi/smsrequest";
			
		$api_status = $this->mdb->sendPostData($urlsend,$postdata);
				
		$apidetails=json_decode($api_status);
		$Apistatus=$apidetails->status; 
	
		
		$this->session->set_flashdata('success', 'Forgot Successfully Please Check Your Mobile');
		
		redirect('Bd_y', 'refresh');	
			
			
	
		}else {
		$this->session->set_flashdata('error', 'Invalid Number');
		$this->load->view('forgot_pass');	
		}
			
		}else {
			
			
		$this->load->view('forgot_pass');	
		
		}
		
	}
	
			
			
			
	
}
