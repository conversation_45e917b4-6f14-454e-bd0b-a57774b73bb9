.spinner,
.tp-loader.spinner0,
#fancybox-loading div {
    border-left: 3px solid rgba(238,119,87,.15);
    border-right: 3px solid rgba(238,119,87,.15);
    border-bottom: 3px solid rgba(238,119,87,.15);
    border-top: 3px solid rgba(238,119,87,.8);
}
a {
    color: #ee7757;
}
.nocolor:hover {
    color: #ee7757
}
.post-title a:hover {
    color: #ee7757
}
.main-title.text-center:after {
    color: #ee7757;
}
ul.circled li:before {
    color: #ee7757;
}
.meta a:hover,
.more:hover {
    color: #ee7757
}
footer a:hover {
    color: #ee7757 !important
}
.btn,
.vanilla-form label.custom-select span {
    background: #ee7757;
}
.btn:hover,
.btn:focus,
.btn:active,
.btn.active,
.pagination ul > li > a:hover,
.pagination ul > li > a:focus,
.pagination ul > .active > a,
.pagination ul > .active > span {
    background: #d96c4f;
}
.navbar .navbar-nav > li > a:focus,
.navbar .nav > li > a:hover,
.navbar .nav > li.current > a {
    color: #ee7757 !important;
}
.navbar .dropdown-menu li a:hover,
.navbar .dropdown-menu li a.active {
    color: #ee7757 !important;
}
.navbar .nav .open > a,
.navbar .nav .open > a:hover,
.navbar .nav .open > a:focus {
    color: #ee7757;
}
.navbar .dropdown-menu > li > a:hover,
.navbar .dropdown-menu > li > a:focus,
.navbar .dropdown-submenu:hover > a,
.navbar .dropdown-submenu:focus > a,
.navbar .dropdown-menu > .active > a,
.navbar .dropdown-menu > .active > a:hover,
.navbar .dropdown-menu > .active > a:focus {
    color: #ee7757
}
.navbar .top-bar a:hover {
	color: #ee7757;
}
.yamm .yamm-content a:hover {
    color: #ee7757
}
.steps .icon {
    color: #ee7757;
}
.steps .steps-item .number {
    background: #ee7757;
}
.steps .steps-item:hover {
    border-color: #ee7757
}
.feature .icon {
    color: #ee7757;
}
.icon-large {
    color: #ee7757;
}
.owl-item .item:hover .box {
    box-shadow: 0 2px 0 #ee7757
}
.post-types .date-wrapper {
    background: #ee7757;
}
.post-types .post .format-wrapper {
    background: #ee7757;
}
.sidebox a:hover {
    color: #ee7757
}
.widget .post-list h5 a:hover {
    color: #ee7757
}
.widget .post-list .meta em a:hover {
    color: #ee7757
}
footer ul.tag-list li a:hover {
    background: #ee7757 !important
}
.testimonials2 .quote:hover {
    border: 2px solid #ee7757
}
.isotope-filter ul li a:hover,
.isotope-filter ul li a.is-checked {
    color: #ee7757
}
.isotope-filter ul li:after {
    color: #ee7757;
}
.price {
    color: #ee7757;
}
.progress-list li em {
    color: #ee7757;
}
.progress.plain .bar {
    background: #ee7757;
}
.bordered .progress.plain {
    border: 1px solid #ee7757;
}
.bordered .progress.plain .bar {
    background: #ee7757
}
.tabs-top.bordered .tab a {
    color: #ee7757;
    border: 2px solid #ee7757;
}
.tabs-top .tab a:hover,
.tabs-top .tab.active a {
    color: #ee7757;
    box-shadow: 0 2px 0 #ee7757;
}
.tabs-top.bordered .tab a:hover,
.tabs-top.bordered .tab.active a {
    border: 2px solid #ee7757;
    background: #ee7757;
}
.tabs-bottom .tab:hover .icon.box,
.tabs-bottom .tab.active .icon.box {
    box-shadow: 0 2px 0 #ee7757
}
.panel-group .panel-active a,
.panel-group .panel-title > a:hover {
    color: #ee7757
}
.panel-group .panel-heading .panel-title:hover,
.panel-group .panel-active .panel-heading .panel-title {
    box-shadow: 0 2px 0 #ee7757;
    color: #ee7757;
}
.bordered .panel-heading .panel-title {
    color: #ee7757;
    border: 2px solid #ee7757;
}
.bordered .panel-heading .panel-title:hover {
    background: #ee7757;
    border: 2px solid #ee7757;
}
.bordered .panel-title > a {
    color: #ee7757
}
.bordered .panel-title:hover,
.bordered .panel-active .panel-heading .panel-title,
.bordered .panel-active .panel-heading .panel-title:hover {
    border: 2px solid #ee7757;
    background: #ee7757;
}
.tooltip-inner {
    background-color: #ee7757;
}
.tooltip.top .tooltip-arrow,
.tooltip.top-left .tooltip-arrow,
.tooltip.top-right .tooltip-arrow {
    border-top-color: #ee7757
}
.tooltip.right .tooltip-arrow {
    border-right-color: #ee7757
}
.tooltip.left .tooltip-arrow {
    border-left-color: #ee7757
}
.tooltip.bottom .tooltip-arrow,
.tooltip.bottom-left .tooltip-arrow,
.tooltip.bottom-right .tooltip-arrow {
    border-bottom-color: #ee7757
}
.vanilla-form input[type="radio"]:focus + span,
.vanilla-form input[type="checkbox"]:focus + span,
.vanilla-form input[type="radio"]:active + span,
.vanilla-form input[type="checkbox"]:active + span {
    border-color: #ee7757
}
.vanilla-form input[type="radio"] + span::after {
    background-color: #ee7757;
    border-color: #ee7757;
}
.vanilla-form input[type="radio"]:checked + span,
.vanilla-form input[type="checkbox"]:checked + span {
    border: 2px solid #ee7757
}
.vanilla-form input[type="checkbox"] + span::after {
    border: 0 solid #ee7757;
}
#comments .info h2 a:hover {
    color: #ee7757
}
#comments a.reply-link:hover {
    color: #ee7757
}
#contact-info-button:hover,
#btn-close-modal:hover {
    color: #ee7757
}
@media (max-width: 991px) { 
	.navbar .navbar-nav .open .dropdown-menu > li > a:hover,
	.navbar .navbar-nav .open .dropdown-menu > li > a:focus {
	    color: #ee7757 !important
	}
}
@media (max-width: 767px) { 
	.isotope-filter ul li a:hover,
	.isotope-filter ul li a.is-checked {
	    box-shadow: 0 2px 0 #ee7757 !important
	}
}