dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    background: transparent;
	font-size:11px;
	font-family: "Lucida Grande", Verdana, Arial, sans-serif;
	text-decoration:none;
	color:#333;
}

a{
color:#000;
cursor:pointer;
}
body {
    line-height: 1;
}
ol, ul {
    list-style: none;
}
blockquote, q {
    quotes: none;
}
blockquote:before, blockquote:after,
q:before, q:after {
    content: '';
    content: none;
}
ins {
    text-decoration: none;
}
del {
    text-decoration: line-through;
}
html,
body {
    width: 100%;
	display:block;
	margin:0px;
	padding:0px;
	background: #fff;
	color:#333;
}

body,
td,
textarea,
input,
select {
    font-family: "Lucida Grande", Verdana, Arial, sans-serif;
    font-size: 11px;
	
}

body,
textarea {
    line-height: 1.4em;
}

input,
select {
    line-height: 1em;
}

p {
    margin: 1em 0;
}

blockquote {
    margin: 1em;
}

label {
    cursor: pointer;
}

li,
dd {
    margin-bottom: 6px;
}

p,
li,
dl,
dd,
dt {
    line-height: 140%;
}

textarea,
input,
select {
    margin: 1px;
    padding: 3px;
}

h1 {
  display: block;
  font-size: 2em;
  font-weight: bold;
  margin: .20em 0;
}

h2 {
  display: block;
  font-size: 1.5em;
  font-weight: bold;
  margin: .83em 0;
}

h3 {
  display: block;
  font-size: 1.17em;
  font-weight: bold;
  margin: 1em 0;
}

h4 {
  display: block;
  font-size: 1em;
  font-weight: bold;
  margin: 1.33em 0;
}

h5 {
  display: block;
  font-size: 0.83em;
  font-weight: bold;
  margin: 1.67em 0;
}

h6 {
  display: block;
  font-size: 0.67em;
  font-weight: bold;
  margin: 2.33em 0;
}
* {
	margin: 0;
	padding: 0;
}
html {
	height: 100%;
}
body {
	font-size:12px;
	font-family: Helvetica Narrow, sans-serif;
	text-decoration:none;
	color:#333;
	height: 100%;
	text-decoration:none;
}
a {
	color: #3B5998;
	text-decoration:none;
}
a:hover {
	text-decoration: none;
}
p {
	margin: 0 0 18px
}
img {
	border: none;
}
input {
	vertical-align: middle;
}

nav {
  vertical-align: bottom;
}

#payment{
width:100%;
height:100%;
margin:0px;
padding:0px;
}

#payment h2{border-bottom:1px solid #ddd;font-size:12px;margin:0px;padding-bottom:5px;}

#payment input[type=text], #payment select {width:300px;border:1px solid #B2B2B2;padding:5px;}
#payment table.pay td{padding:2px 0px;}
#payment input.button{
height:22px;
cursor:pointer;
width:80px;
border:none;
background:#0072C6;
color:#fff;}
