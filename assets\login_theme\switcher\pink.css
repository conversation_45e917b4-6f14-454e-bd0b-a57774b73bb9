.spinner,
.tp-loader.spinner0,
#fancybox-loading div {
    border-left: 3px solid rgba(212,135,190,.15);
    border-right: 3px solid rgba(212,135,190,.15);
    border-bottom: 3px solid rgba(212,135,190,.15);
    border-top: 3px solid rgba(212,135,190,.8);
}
a {
    color: #d487be;
}
.nocolor:hover {
    color: #d487be
}
.post-title a:hover {
    color: #d487be
}
.main-title.text-center:after {
    color: #d487be;
}
ul.circled li:before {
    color: #d487be;
}
.meta a:hover,
.more:hover {
    color: #d487be
}
footer a:hover {
    color: #d487be !important
}
.btn,
.vanilla-form label.custom-select span {
    background: #d487be;
}
.btn:hover,
.btn:focus,
.btn:active,
.btn.active,
.pagination ul > li > a:hover,
.pagination ul > li > a:focus,
.pagination ul > .active > a,
.pagination ul > .active > span {
    background: #bd78a9;
}
.navbar .navbar-nav > li > a:focus,
.navbar .nav > li > a:hover,
.navbar .nav > li.current > a {
    color: #d487be !important;
}
.navbar .dropdown-menu li a:hover,
.navbar .dropdown-menu li a.active {
    color: #d487be !important;
}
.navbar .nav .open > a,
.navbar .nav .open > a:hover,
.navbar .nav .open > a:focus {
    color: #d487be;
}
.navbar .dropdown-menu > li > a:hover,
.navbar .dropdown-menu > li > a:focus,
.navbar .dropdown-submenu:hover > a,
.navbar .dropdown-submenu:focus > a,
.navbar .dropdown-menu > .active > a,
.navbar .dropdown-menu > .active > a:hover,
.navbar .dropdown-menu > .active > a:focus {
    color: #d487be
}
.navbar .top-bar a:hover {
	color: #d487be;
}
.yamm .yamm-content a:hover {
    color: #d487be
}
.steps .icon {
    color: #d487be;
}
.steps .steps-item .number {
    background: #d487be;
}
.steps .steps-item:hover {
    border-color: #d487be
}
.feature .icon {
    color: #d487be;
}
.icon-large {
    color: #d487be;
}
.owl-item .item:hover .box {
    box-shadow: 0 2px 0 #d487be
}
.post-types .date-wrapper {
    background: #d487be;
}
.post-types .post .format-wrapper {
    background: #d487be;
}
.sidebox a:hover {
    color: #d487be
}
.widget .post-list h5 a:hover {
    color: #d487be
}
.widget .post-list .meta em a:hover {
    color: #d487be
}
footer ul.tag-list li a:hover {
    background: #d487be !important
}
.testimonials2 .quote:hover {
    border: 2px solid #d487be
}
.isotope-filter ul li a:hover,
.isotope-filter ul li a.is-checked {
    color: #d487be
}
.isotope-filter ul li:after {
    color: #d487be;
}
.price {
    color: #d487be;
}
.progress-list li em {
    color: #d487be;
}
.progress.plain .bar {
    background: #d487be;
}
.bordered .progress.plain {
    border: 1px solid #d487be;
}
.bordered .progress.plain .bar {
    background: #d487be
}
.tabs-top.bordered .tab a {
    color: #d487be;
    border: 2px solid #d487be;
}
.tabs-top .tab a:hover,
.tabs-top .tab.active a {
    color: #d487be;
    box-shadow: 0 2px 0 #d487be;
}
.tabs-top.bordered .tab a:hover,
.tabs-top.bordered .tab.active a {
    border: 2px solid #d487be;
    background: #d487be;
}
.tabs-bottom .tab:hover .icon.box,
.tabs-bottom .tab.active .icon.box {
    box-shadow: 0 2px 0 #d487be
}
.panel-group .panel-active a,
.panel-group .panel-title > a:hover {
    color: #d487be
}
.panel-group .panel-heading .panel-title:hover,
.panel-group .panel-active .panel-heading .panel-title {
    box-shadow: 0 2px 0 #d487be;
    color: #d487be;
}
.bordered .panel-heading .panel-title {
    color: #d487be;
    border: 2px solid #d487be;
}
.bordered .panel-heading .panel-title:hover {
    background: #d487be;
    border: 2px solid #d487be;
}
.bordered .panel-title > a {
    color: #d487be
}
.bordered .panel-title:hover,
.bordered .panel-active .panel-heading .panel-title,
.bordered .panel-active .panel-heading .panel-title:hover {
    border: 2px solid #d487be;
    background: #d487be;
}
.tooltip-inner {
    background-color: #d487be;
}
.tooltip.top .tooltip-arrow,
.tooltip.top-left .tooltip-arrow,
.tooltip.top-right .tooltip-arrow {
    border-top-color: #d487be
}
.tooltip.right .tooltip-arrow {
    border-right-color: #d487be
}
.tooltip.left .tooltip-arrow {
    border-left-color: #d487be
}
.tooltip.bottom .tooltip-arrow,
.tooltip.bottom-left .tooltip-arrow,
.tooltip.bottom-right .tooltip-arrow {
    border-bottom-color: #d487be
}
.vanilla-form input[type="radio"]:focus + span,
.vanilla-form input[type="checkbox"]:focus + span,
.vanilla-form input[type="radio"]:active + span,
.vanilla-form input[type="checkbox"]:active + span {
    border-color: #d487be
}
.vanilla-form input[type="radio"] + span::after {
    background-color: #d487be;
    border-color: #d487be;
}
.vanilla-form input[type="radio"]:checked + span,
.vanilla-form input[type="checkbox"]:checked + span {
    border: 2px solid #d487be
}
.vanilla-form input[type="checkbox"] + span::after {
    border: 0 solid #d487be;
}
#comments .info h2 a:hover {
    color: #d487be
}
#comments a.reply-link:hover {
    color: #d487be
}
#contact-info-button:hover,
#btn-close-modal:hover {
    color: #d487be
}
@media (max-width: 991px) { 
	.navbar .navbar-nav .open .dropdown-menu > li > a:hover,
	.navbar .navbar-nav .open .dropdown-menu > li > a:focus {
	    color: #d487be !important
	}
}
@media (max-width: 767px) { 
	.isotope-filter ul li a:hover,
	.isotope-filter ul li a.is-checked {
	    box-shadow: 0 2px 0 #d487be !important
	}
}