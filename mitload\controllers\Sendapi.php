<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>api extends CI_Controller {
	
	
	
		function __construct()
    {
        parent::__construct();
       
        date_default_timezone_set('Asia/Dhaka');
     
        //$this->load->model('admin');
		$this->load->library('form_validation');
		$this->load->library('user_agent');
    }
	
	 private function date_time(){
        //$timezone_offset = +6; // BD central time (gmt+6) for me
		//date_default_timezone_set("Asia/Dhaka");
        return $create_date = date('Y-m-d H:i:s');
     }
	 

	
	
	public function index()
	{
	
		
			
	}
	
	public function status() {
		
	$user = $this->security->xss_clean($this->input->post('user',TRUE));
	$key = $this->security->xss_clean($this->input->post('key',TRUE));
	
	$sid = $this->security->xss_clean($this->input->post('id',TRUE));
	
	//$getip=$_SERVER['REMOTE_ADDR'];
		$getip=0;
	$uid = $this->db->get_where('api_key_user',array('username' =>$user, 'apikey' =>$key))->row()->userid;
	
	$queryalowip= $this->db->query("select * from api_key_ip where username='$user' and api_key='$key' and ip='$getip'");
	
	$apiallow = $this->db->get_where('reseller',array('username' =>$user))->row()->api;
	
	if(!empty($uid) && $apiallow==1) {
		
	if($queryalowip->num_rows() > 0 ) {
			
			 $apiload = "Select * from sendflexi where sid='$sid' order by id asc limit 1"; 
			 
			 $queryload = $this->db->query($apiload);
				
				foreach ($queryload->result() as $row_load)
					{
			
                        $loguserid=$row_load->id;  
                        $eid=$row_load->sid; 
                        $logonuid=$row_load->number; 
                        $amount=$row_load->amount; 
                        $trxid=$row_load->trxid; 
                        $status=$row_load->status; 
						$fluserid=$row_load->userid; 
      

	$accountbalance = $this->db->get_where('trans',array('flex_id' =>$eid,'userid' =>$fluserid))->row()->accountbalance;	
	
	           
     } 
			
			
	$response = array("posotion" => $status, "trxid" => $trxid, "lastbalance" => $accountbalance,"status" => 1); 
		
		} //empty
		else {
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => $getip.' Tthis IP Not Allow'
					);
		}
		
		
		} //empty
		else {
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Not User'
					);
		}
		
		header('Content-type: application/json'); 
		echo json_encode($response); 
			
	}
	
	public function balance() {
		
	$user = $this->security->xss_clean($this->input->post('user',TRUE));
	$key = $this->security->xss_clean($this->input->post('key',TRUE));
	
	//$getip=$_SERVER['REMOTE_ADDR'];
	$getip=0;
	$uid = $this->db->get_where('api_key_user',array('username' =>$user, 'apikey' =>$key))->row()->userid;
	
	$queryalowip= $this->db->query("select * from api_key_ip where username='$user' and api_key='$key' and ip='$getip'");
	
	$apiallow = $this->db->get_where('reseller',array('username' =>$user))->row()->api;
	$band = $this->security->xss_clean($_SERVER['HTTP_BAND_KEY']);
      if($band!='flexisoftwarebd')$apiallow=0;
	if(!empty($uid) && $apiallow==1) {
		
	if($queryalowip->num_rows() > 0 ) {
	
	$balance = $this->db->get_where('reseller',array('username' =>$user))->row()->balance;
			
			$response = array(
					'success' => true,
					'status'  => 1,
					'balance' => $balance
					);
	
		} //empty
		else {
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => $getip.' Tthis IP Not Allow'
					);
		}
		
		
		} //empty
		else {
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Not User'
					);
		}
		
		header('Content-type: application/json'); 
		echo json_encode($response); 
		
	}
	
	public function request()
	{
	
	if($_POST) {
		
			
      
      
	$number = $this->security->xss_clean($this->input->post('number',TRUE));
	$amount = $this->security->xss_clean($this->input->post('amount',TRUE));
	$type = $this->security->xss_clean($this->input->post('type',TRUE));
	$sid = $this->security->xss_clean($this->input->post('id',TRUE));
	$service = $this->security->xss_clean($this->input->post('service',TRUE));
		
	$user = $this->security->xss_clean($this->input->post('user',TRUE));
	$key = $this->security->xss_clean($this->input->post('key',TRUE));
		$pcode = $this->security->xss_clean($this->input->post('operator',TRUE));

	
	//$getip=$_SERVER['REMOTE_ADDR'];
		$getip=0;
	$uid = $this->db->get_where('api_key_user',array('username' =>$user, 'apikey' =>$key))->row()->userid;
	
	$queryalowip= $this->db->query("select * from api_key_ip where username='$user' and api_key='$key' and ip='$getip'");
	
	$apiallow = $this->db->get_where('reseller',array('username' =>$user))->row()->api;
	$band = $this->security->xss_clean($_SERVER['HTTP_BAND_KEY']);
      if($band!='flexisoftwarebd')$apiallow=0;
      
	if(!empty($uid) && $apiallow==1) {
		
	if($queryalowip->num_rows() > 0 ) {
			
		$this->irs->recharge($service,$number,$amount,$type,$uid,'api','yes',$sid,"","","","","",$pcode);
		$getirdata= $this->session->userdata('irslog');
		$irstsview = $getirdata['irsts'];
		$irsmessage = $getirdata['irsmessage'];
		
		if($irstsview=="error") {
			$response = array(
		'success' => false,
		'status'  => 2,
		'message' => $irsmessage
		);
		}else {
		$response = array(
		'success' => true,
		'status'  => 1,
		'message' => $irsmessage
		);
		}
		
		} //empty
		else {
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => $getip.' Tthis IP Not Allow'
					);
		}
		
		
		} //empty
		else {
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Not User'
					);
		}
		
		} // post
		else {

			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty not work'
					);


		}

	header('Content-type: application/json'); 
		echo json_encode($response); 
	
	}
	
	
	
	
	
}
