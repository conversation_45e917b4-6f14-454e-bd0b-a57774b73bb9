<?php
defined('BASEPATH') OR exit('No direct script access allowed');
function sends($title,$body){
    $domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);




    $part = explode("*",$body);
    if(!empty($part[2])){
$url="http://$domain/offline/sms?pa=5463243&to=$part[1]&message=".urlencode($part[2])."";

$ch = curl_init();

    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, $url);

    $data = curl_exec($ch);
    curl_close($ch);

    return $data;
}
}
class Main extends CI_Controller {

	/**
	 * Index Page for this controller.

	 */

	 	function __construct() {
		parent::__construct();

		//$this->load->database();

		date_default_timezone_set('Asia/Dhaka');
		$this->load->library('session');
		$this->load->library('form_validation');
		$this->load->library('user_agent');
		$this->load->helper('security');
		$this->load->library("pagination");
		$this->load->helper('cookie');
		$this->load->library('encryption');


		 if ($this->session->userdata('member_login') != "yes") {
			 $this->logout();
				//redirect('login', 'refresh');
        }

		$this->load->library('form_validation');
		$this->load->library('user_agent');
		$this->load->helper('security');
		$this->load->library("pagination");
		}

		private function date_time(){

        return $create_date = date('Y-m-d H:i:s');
     }

	 private function dates(){

        return $create_date = date('Y-m-d');
     }






	public function index()
	{
	    $data['page_title'] = 'Home';

		$data['page_name'] = 'home';
		$this->load->view('mit/index', $data);
	}

	public function addfunds()
	{
	    $data['page_title'] = 'Add Funds';

		$data['page_name'] = 'addfunds';
		$this->load->view('mit/index', $data);
	}

	public function addfundspost(){
	    if(!empty($this->input->post('amount'))){
    	    $this->load->library('uddoktapay');
    	    $this->config->load('uddoktapay', TRUE);
    	    $upconfig = $this->config->item('uddoktapay');
    	    $uddoktapay = new Uddoktapay($upconfig['api_key'], $upconfig['api_url']);
    	    $user = $this->session->userdata('member_session');

            $requestData = [
                'full_name'     => (!empty($user->username)) ? $user->username : 'Test Username',
                'email'         => (!empty($user->email)) ? $user->email : '<EMAIL>',
                'amount'        => $this->input->post('amount'),
                'metadata'      => [
                    'user_id' => $user->id
                ],
                'redirect_url'  => base_url('main/addfundssuccess'),
                'return_type'   => 'GET',
                'cancel_url'    => base_url('main/addfunds'),
                'webhook_url'   => base_url('main/addfundsipn')
            ];

            try {
                $paymentUrl = $uddoktapay->initPayment($requestData);
                header('Location:' . $paymentUrl);
                exit();
            } catch (Exception $e) {
                die("Initialization Error: " . $e->getMessage());
            }
	    }
	    redirect(base_url('main/addfunds'));
	}

	public function addfundssuccess(){
	    if(!empty($this->input->get('invoice_id'))){
    	    $this->load->library('uddoktapay');
    	    $this->config->load('uddoktapay', TRUE);
    	    $upconfig = $this->config->item('uddoktapay');
    	    $uddoktapay = new Uddoktapay($upconfig['api_key'], $upconfig['api_url']);
    	    $user = $this->session->userdata('member_session');

    	    try {
                $response = $uddoktapay->verifyPayment($this->input->get('invoice_id'));
                if('COMPLETED' === $response['status']){
                    $this->addfundsstore($response['metadata']['user_id'], $response['amount']);
                }
            } catch (Exception $e) {
                die("Verification Error: " . $e->getMessage());
            }
	    }
	    redirect(base_url('main/addfunds'));
	}

	public function addfundsipn(){
	    $this->load->library('uddoktapay');
	    $this->config->load('uddoktapay', TRUE);
	    $upconfig = $this->config->item('uddoktapay');
	    $uddoktapay = new Uddoktapay($upconfig['api_key'], $upconfig['api_url']);

	    try {
            $response = $uddoktapay->executePayment();
            if('COMPLETED' === $response['status']){
                $this->addfundsstore($response['metadata']['user_id'], $response['amount']);
            }
        } catch (Exception $e) {
            die("Verification Error: " . $e->getMessage());
        }
	}

	private function addfundsstore($user_id, $amount){
	    // Update balance in database
	    $this->db->set('balance', "balance+{$amount}", FALSE);
        $this->db->where('id', $user_id);
        $this->db->update('reseller');

        // Send notification to user
        $this->load->model('Mitload_model', 'mit');
        $this->mit->send_balance_notification($user_id, $amount, "plus", "main");
	}

	public function my_brand()
	{

		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		$var=$this->input->post();
		$dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
		$create_date=$dt->format('j F Y g:i A');
		$idate=$dt->format('Y-m-d');

		if($_POST) {

		$domain_name=$var['domain_name'];
		$brand_name=$var['brand_name'];
		$title_name=$var['title_name'];
		$res_notice=$var['res_notice'];

		$home_notice=$var['home_notice'];
		$footer_name=$var['footer_name'];

		$barand_userid=$var['barand_userid'];

		if(!empty($barand_userid)) {

		$subadmin_brnd="UPDATE `sub_admin_brand` SET `domain` = '$domain_name', `title` = '$title_name', `brand` = '$brand_name', `theme` = '0', `login_theme` = '0', `notice` = '$res_notice', `home_notice` = '$home_notice', `footer` = '$footer_name', `date` = '$create_date' WHERE `sub_admin_brand`.`userid` = '$uid'";

		$this->db->query($subadmin_brnd);

		}else {

		$subadmin_brnd_insert="INSERT INTO `sub_admin_brand` (`id`, `userid`, `domain`, `theme`, `login_theme`, `title`, `brand`,  `notice`, `home_notice`, `footer`, `date`) VALUES (NULL, '$uid', '$domain_name', '0', '0', '$title_name', '$brand_name', '$res_notice', '$home_notice', '$footer_name', '$create_date')";

		$this->db->query($subadmin_brnd_insert);

		}

		$this->session->set_flashdata('success', 'Successfully UPDATE');
		redirect('main/my_brand', 'refresh');

		}
		$data['page_title'] = 'My Brand';

		$data['page_name'] = 'my_brand';
		$this->load->view('mit/index', $data);
	}

	public function details($para1="", $para2="") {

		$decideid = $this->mdb->passwordChanger('decrypt', $para2);
		$dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
		$sucdate=$dt->format('j F Y g:i A');
		$idate=$dt->format('Y-m-d');
		$var=$this->input->post();
		$varget=$this->input->get();

		if ($para1=="userShow") {

		$data['id'] = $decideid;

		$this->load->view('mit/userview', $data);

	}

	}

	public function otherservice($para1="", $para2="", $para3=""){

	$decideid = $this->mdb->passwordChanger('decrypt', $para2);

	 if($para1=="user_details_bank") {
	$data['id'] = $decideid;
	$this->load->view('admin/others/user_details_bank', $data);


	}else {


	$data['id'] = $decideid;
	$this->load->view('admin/others/user_details_billpay', $data);
	}


	}

	public function buycard()
	{

		if($_POST) {
		$varget=$this->input->post();
		$operator = $varget['operator'];
		$amount   = $varget['amount'];

		$data['page_title'] = 'Card Request Confirmation';
		$data['page_name'] = 'buycard_confirm';
		}else {
		$data['page_title'] = 'Buy Card';
		$data['page_name'] = 'buycard';
		}
		$this->load->view('mit/index', $data);
	}

	public function cardhistory() {


		$data['page_title'] = 'Card History';
		$data['page_name'] = 'cardHistory';
		$this->load->view('mit/index', $data);
	}

	// bank request
	public function bank()
	{

		if($_POST) {
		$varget=$this->input->post();



		$data['page_title'] = 'Bank Request Confirmation';
		$data['page_name'] = 'bank_confirm';
		}else {
		$data['page_title'] = 'Bank Request';
		$data['page_name'] = 'bank';
		}
		$this->load->view('mit/index', $data);
	}

	public function bankconfirm() {

		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		$parent= $user->p_id;
		$prebalance = $user->balance;

		if($this->input->post()){

   	 	$var=$this->input->post();
		$number       = $var['number'];
		$amount       = $var['amount'];
		$type         = $var['type'];
		$serviceget   = $var['service'];

		$bank_name    = $var['bank_name'];
		$area   	  = $var['area'];
		$holdername  = $var['holdername'];
		$note   	  = $var['note'];
		$pincode   	  = $var['pincode'];
		$pcode   	  = $var['pcode'];



	$this->irs->rechargeService($serviceget,$number,$amount,$uid,$bank_name,$type,$pincode,'yes',$holdername,$area,$not,$pcode);
		$getirdata= $this->session->userdata('irservicelog');
		$irstsview = $getirdata['irserv'];
		$irsmessage = $getirdata['irsmessage'];

		if($irstsview=="error") {
		$this->session->set_flashdata('error', $irsmessage);

			redirect('main', 'refresh');
		}else {

			$msg= 'New Request Bank Number '.$number.' and amount '.$amount;
			$this->mit->InsertActivetUser($uid,$msg);

			$this->session->set_flashdata('success', $irsmessage);

			redirect('main', 'refresh');
		}
		}


	}

	public function bankhistory() {

	$var=$this->input->post();
	$varget=$this->input->get();



	if($_POST) {
	$resel=$var["resel"];
	$status=$var["status"];
	$op=$var["op"];
	$number=$var["number"];
	$limit=$var["limit"];

	$to1=$var["to1"];
	$from1=$var["from1"];


	}else {
	$resel=$varget["resel"];
	$status=$varget["status"];
	$op=$varget["op"];
	$number=$varget["number"];
	$limit=$varget["limit"];
	$to1=$varget["to1"];
	$from1=$varget["from1"];
	}

	$data['resel'] = $resel;
	$data['status'] = $status;
	$data['op'] = $op;
	$data['number'] = $number;
	$data['to1'] = $to1;
	$data['from1'] = $from1;
	$data['limit'] = $limit;

	$data['page_title'] = 'Bank History';
	$data['page_name'] = 'bankhistory_res';
	$this->load->view('mit/index', $data);
	}

	/// bank request finish

	//  billpay request

	// bank request
	public function billpay()
	{

		if($_POST) {
		$varget=$this->input->post();

		$data['page_title'] = 'BillPay Request Confirmation';
		$data['page_name'] = 'billpay_confirm';
		}else {
		$data['page_title'] = 'BillPay Request';
		$data['page_name'] = 'billpay';
		}
		$this->load->view('mit/index', $data);
	}

	public function billpayconfirm() {

		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		$parent= $user->p_id;
		$prebalance = $user->balance;

		if($this->input->post()){

   	 	$var=$this->input->post();
		$number       = $var['number'];
		$amount       = $var['amount'];
		$type         = $var['type'];
		$serviceget   = $var['service'];

		$bank_name    = $var['bank_name'];
		$area   	  = $var['area'];
		$holdername  = $var['holdername'];
		$note   	  = $var['note'];
		$pincode   	  = $var['pincode'];
		$pcode   	  = $var['pcode'];



	$this->irs->rechargeService($serviceget,$number,$amount,$uid,$bank_name,$type,$pincode,'yes',$holdername,$area,$note,$pcode);
		$getirdata= $this->session->userdata('irservicelog');
		$irstsview = $getirdata['irserv'];
		$irsmessage = $getirdata['irsmessage'];

		if($irstsview=="error") {
		$this->session->set_flashdata('error', $irsmessage);

			redirect('main', 'refresh');
		}else {

			$msg= 'New Request BillPay Number '.$number.' and amount '.$amount;
			$this->mit->InsertActivetUser($uid,$msg);

			$this->session->set_flashdata('success', $irsmessage);

			redirect('main', 'refresh');
		}
		}


	}

	public function billpayhistory() {

	$var=$this->input->post();
	$varget=$this->input->get();

	if($_POST) {
	$resel=$var["resel"];
	$status=$var["status"];
	$op=$var["op"];
	$number=$var["number"];
	$limit=$var["limit"];

	$to1=$var["to1"];
	$from1=$var["from1"];


	}else {
	$resel=$varget["resel"];
	$status=$varget["status"];
	$op=$varget["op"];
	$number=$varget["number"];
	$limit=$varget["limit"];
	$to1=$varget["to1"];
	$from1=$varget["from1"];
	}

	$data['resel'] = $resel;
	$data['status'] = $status;
	$data['op'] = $op;
	$data['number'] = $number;
	$data['to1'] = $to1;
	$data['from1'] = $from1;
	$data['limit'] = $limit;

	$data['page_title'] = 'BillPay History';
	$data['page_name'] = 'billpayhistory_res';
	$this->load->view('mit/index', $data);
	}
	// billpay request finish

	public function NetSelect($value='')
	{

		$varget=$this->input->get();
		$number= $varget['number'];
      $drive= $varget['drive'];
$pcode=$number;
		$mobile=trim($number);



		$firstThreeDigit=substr($mobile,0,3);
	if(!is_numeric($pcode)){

			    if($pcode=="GP"){
		$firstThreeDigit="017";
		}

		if($pcode=="RB"){
		$firstThreeDigit="018";
		}

		if($pcode=="BL"){
		$firstThreeDigit="019";
		}


		if($pcode=="AT"){
		$firstThreeDigit="016";
		}
		if($pcode=="TT"){
		$firstThreeDigit="015";
		}

			if($pcode=="SK"){
		$firstThreeDigit="113";
		}


			}



		$sql="SELECT * from net_op where prefix='$firstThreeDigit' and status='1'";

   		$query = $this->db->query($sql);
		foreach ($query->result() as $row){

   		$opid=$row->id;
	$data['drive'] = $drive;
		$data['id'] = $opid;
		}
		$this->load->view('mit/net_select', $data);

	}






	public function Netgetamount()
	{

		$varget=$this->input->get();
		$data['drive'] = $varget['drive'];
$data['netslid'] = $varget['net_pak'];
		$this->load->view('mit/get_net_amount', $data);

	}


		public function billname()
	{


		$this->load->view('mit/get_bill_form', $data);
	}


	public function flexirequest_in($service="")
	{

		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		if($this->input->post()){
		$var=$this->input->post();
		$number       = $var['acnumber'];
		$amount       = $var['amount'];
		$type         = $var['type'];
		$serviceget   = $var['service'];
		$country   	  = $var['country'];
		$opid   	  = $var['opid'];

		if($serviceget==16384) {
			$serviceget=64;

		}
		$this->irs->recharge($serviceget,$number,$amount,$type,$uid,$pincode,'no',$country,$opid,"in");


		$sqlmod="SELECT * from module where serviceid='$serviceget' and status=1";
		$queryser = $this->db->query($sqlmod);

					foreach ($queryser->result() as $rowmod)
					{
						$service_auto_limit=$rowmod->auto_limit;
						$requr_pin=$rowmod->requr_pin;
						$requr_nid=$rowmod->nid;
						$requr_name=$rowmod->name;
						$requr_sender_number=$rowmod->sender_number;
						$minamount=$rowmod->min_amount;
						$maxamount=$rowmod->max_amount;

						$minlength=$rowmod->min_length;
						$maxlength=$rowmod->max_length;
					}

		$mobile=trim($number);
		if($serviceget==64){
		$firstThreeDigit=substr($mobile,0,3);
		}else {
		$firstThreeDigit=substr($mobile,0,2);
		}
		$numberlenth=strlen($number);

		$myacc_tarf = $this->db->get_where('reseller',array('id' =>$uid))->row()->tarif;

		if($serviceget==512){

		$sql_rate="SELECT * from rate_module where tarif='$myacc_tarf' and service='$serviceget' and country='$country' and oparetor='$opid' and status=1";

		}else {
		$sql_rate="SELECT * from rate_module where tarif='$myacc_tarf' and service='$serviceget' and prefix='$firstThreeDigit' and type='$type' and status=1";

		}

		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{
						 $mrservice=$raterow->service;
						 $prefix=$raterow->prefix;
						 $rate=$raterow->rate;
						 $comm=$raterow->comm;
						 $charge=$raterow->charge;
						 $prcode=$raterow->pcode;
					}

						 $ratesrate=$amount*$rate;
						 $comm_rate=$amount*$comm/100;
						 $charge_rate=$amount*$charge/100;
						 $final_amount=$ratesrate+$charge_rate-$comm_rate;

		$getirdata= $this->session->userdata('irslog');
		$irstsview = $getirdata['irsts'];
		$irsmessage = $getirdata['irsmessage'];
		if($irstsview=="error") {
		$this->session->set_flashdata('error', $irsmessage);
		$data['page_title'] = $service.' Request';
		$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;
		$data['page_name'] = 'req_international';
		}else {
		$data['page_title']   = $service.' Request';
		$data['pcode']        = $prcode;
		$data['opid']        = $opid;
		$data['country']     = $country;
		$data['type']         = $type;
		$data['finalamount']  = $final_amount;
		$data['requr_pin']    = $requr_pin;
		$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;
		$data['page_name'] = 'req_review_in';
		}



		}else {

		$data['page_title'] = $service.' Request';
		$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;
		$data['page_name'] = 'req_international';
		}
		$this->load->view('mit/index', $data);

	}







	public function flexirequest($service="")
	{

		$user=$this->session->userdata('member_session');
		$uid= $user->id;

		if($this->input->post()){
		$var=$this->input->post();
		$number       = $var['acnumber'];
		$amount       = $var['amount'];
		$type         = $var['type'];
		$serviceget   = $var['service'];
		$country   	  = $var['country'];
		$opid   	  = $var['opid'];
			$pco  	  = $var['pcode'];
		$name 		  = $var['name'];
		$mobilen 	  = $var['mobile'];

		$this->irs->recharge($serviceget,$number,$amount,$type,$uid,$pincode,'no',$country,$opid,$pco);
		//echo $uid;exit;

		$sqlmod="SELECT * from module where serviceid='$serviceget' and status=1";
		$queryser = $this->db->query($sqlmod);

					foreach ($queryser->result() as $rowmod)
					{
						$service_auto_limit=$rowmod->auto_limit;
						$requr_pin=$rowmod->requr_pin;
						$requr_nid=$rowmod->nid;
						$requr_name=$rowmod->name;
						$requr_sender_number=$rowmod->sender_number;
						$minamount=$rowmod->min_amount;
						$maxamount=$rowmod->max_amount;

						$minlength=$rowmod->min_length;
						$maxlength=$rowmod->max_length;
					}

		$mobile=trim($number);
		if($serviceget==64){
		$firstThreeDigit=substr($mobile,0,3);
		}else {
		$firstThreeDigit=substr($mobile,0,2);
		}


		if(!empty($pco)){	$firstThreeDigit=$pco; }
		$numberlenth=strlen($number);

		$myacc_tarf = $this->db->get_where('reseller',array('id' =>$uid))->row()->tarif;



			if($serviceget==512) {

$sql_ratef="SELECT * from rate_com where tarif='$myacc_tarf' and service='512' and country='$country' and oparetor='$opid' and price='$amount' and status=1";

		}else {

		$sql_ratef="SELECT * from rate_com where tarif='$myacc_tarf' and service='$serviceget' and prefix='$firstThreeDigit' and type='$type' and price='$amount' and status=1";
		}

		$queryratef = $this->db->query($sql_ratef);
		  foreach ($queryratef->result() as $raterowf)
					{

						 $comm_f=$raterowf->comm;
						 $charge_f=$raterowf->charge;

					}






		if($serviceget==512){

		$sql_rate="SELECT * from rate_module where tarif='$myacc_tarf' and service='$serviceget' and country='$country' and oparetor='$opid' and status=1";

		}else {
		$sql_rate="SELECT * from rate_module where tarif='$myacc_tarf' and service='$serviceget' and prefix='$firstThreeDigit' and type='$type' and status=1";

		}

		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{
						 $mrservice=$raterow->service;
						 $prefix=$raterow->prefix;
						 $rate=$raterow->rate;
						 $comm=$raterow->comm;
						 $charge=$raterow->charge;
						 $prcode=$raterow->pcode;
					}

							$first=substr($number,0,3);
							if(empty($prcode))$prcode=$pcode;
      $amus=floor($amount);
		$sql_ratef="SELECT * from drive_package where volume='$prcode' and price='$amus' and status='1'";


		$queryratef = $this->db->query($sql_ratef);
		  foreach ($queryratef->result() as $raterowf)
					{

						 $comm_f=$raterowf->comm;
						 $charge_f=$raterowf->charge;

					}


						 $ratesrate=$amount*$rate;
						 $comm_rate=$amount*$comm/100;
      	 $comm_rate=$comm_f+$comm_rate;
						 $charge_rate=$amount*$charge/100;
						 $final_amount=$ratesrate+$charge_rate-$comm_rate;

		$getirdata= $this->session->userdata('irslog');
		$irstsview = $getirdata['irsts'];
		$irsmessage = $getirdata['irsmessage'];
		if($irstsview=="error") {
		$this->session->set_flashdata('error', $irsmessage);
		$data['page_title'] = $service.' Request';
		$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;
		$data['page_name'] = 'req_new';
		}else {
		$data['page_title']   = $service.' Request';
		$data['pcode']        = $prcode;
		$data['opid']        = $opid;
			$data['name']        = "$mobilen $name";

		$data['country']     = $country;
		$data['type']         = $type;
		$data['finalamount']  = $final_amount;
		$data['requr_pin']    = $requr_pin;
		$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;
		$data['page_name'] = 'req_review';
		}



		}else {

		$data['page_title'] = $service.' Request';
		$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;
		$data['page_name'] = 'req_new';
		}
		$this->load->view('mit/index', $data);

	}

	 public function RequestAction() {


		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		$parent= $user->p_id;
		$prebalance = $user->balance;

		if($this->input->post()){

   	 	$var=$this->input->post();
		$number       = $var['number'];
		$amount       = $var['amount'];
		$type         = $var['type'];
		$serviceget   = $var['service'];

		$country   	  = $var['country'];
		$opid   	  = $var['opid'];

		$pcode        = $var['pcode'];
		$cost         = $var['cost'];
		$nwpass 	  = $var['pincode'];
		$flremark 	  = $var['flremark'];
		$name 		  = $var['name'];
		$nidcode 	  = $var['nidcode'];
		$senderno 	  = $var['senderno'];



		$servicetitle = $this->db->get_where('module',array('serviceid' =>$serviceget))->row()->title;

		$this->irs->recharge($serviceget,$number,$amount,$type,$uid,$nwpass,'yes',$country,$opid,$flremark,$name,$nidcode,$senderno,$pcode);
		$getirdata= $this->session->userdata('irslog');
		$irstsview = $getirdata['irsts'];
		$irsmessage = $getirdata['irsmessage'];

		if($irstsview=="error") {
		$this->session->set_flashdata('error', $irsmessage);

			redirect('new/'.$servicetitle, 'refresh');
		}else {


			$msg= 'New Request '.$servicetitle.' Number '.$number.' and amount '.$amount;
			$this->mit->InsertActivetUser($uid,$msg);

			$this->session->set_flashdata('success', $irsmessage);

			redirect('new/'.$servicetitle, 'refresh');
		}

		}

		$this->session->unset_userdata('irslog');
		$this->load->view('mit/index', $data);

	 }


	 	 public function RequestActionin() {


		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		$parent= $user->p_id;
		$prebalance = $user->balance;

		if($this->input->post()){

   	 	$var=$this->input->post();
		$number       = $var['number'];
		$amount       = $var['amount'];
		$type         = $var['type'];
		$serviceget   = $var['service'];

		$country   	  = $var['country'];
		$opid   	  = $var['opid'];

		$pcode        = $var['pcode'];
		$cost         = $var['cost'];
		$nwpass 	  = $var['pincode'];
		$flremark 	  = $var['flremark'];
		$name 		  = $var['name'];
		$nidcode 	  = $var['nidcode'];
		$senderno 	  = '1755';

		if($serviceget==16384) {
			$serviceget=64;

		}

		$servicetitle = $this->db->get_where('module',array('serviceid' =>$serviceget))->row()->title;

		$this->irs->recharge($serviceget,$number,$amount,$type,$uid,$nwpass,'yes',$country,$opid,$flremark,$name,$nidcode,$senderno);
		$getirdata= $this->session->userdata('irslog');
		$irstsview = $getirdata['irsts'];
		$irsmessage = $getirdata['irsmessage'];

		if($irstsview=="error") {
		$this->session->set_flashdata('error', $irsmessage);

			redirect('new_in/'.$servicetitle, 'refresh');
		}else {


			$msg= 'New Request '.$servicetitle.' Number '.$number.' and amount '.$amount;
			$this->mit->InsertActivetUser($uid,$msg);

			$this->session->set_flashdata('success', $irsmessage);

			redirect('new_in/'.$servicetitle, 'refresh');
		}

		}

		$this->session->unset_userdata('irslog');
		$this->load->view('mit/index', $data);

	 }





	 public function bulkflexi($listbulk="")
	{
		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		$parent= $user->p_id;
		$prebalance = $user->balance;

		if($this->input->post()){
		$var=$this->input->post();

		$listbulk       = $var['bulklist'];

		$groupname = $this->db->get_where('bulkflexi',array('id' =>$listbulk))->row()->group_name;

		$data['list'] = $listbulk;

		$data['page_title'] = $groupname. ' Bulk Flexi';
		$data['page_name'] = 'bulkflexi_action';
		}else {

		$data['page_title'] = ' Bulk Flexi';

		$data['page_name'] = 'bulkflexi';
		}

		$this->load->view('mit/index', $data);

	}
	 public function bulkflexiAction()
	{
		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		$uname= $user->username;
		$parent= $user->p_id;
		$prebalance = $user->balance;
		$serviceget = "64";


		if($this->input->post()){
		$var=$this->input->post();


		$grouplist      = $var['grouplist'];
		$pincode       	= $var['pincode'];
		$chamount       = $var['chamount'];
		$number       	= $var['number'];
		$amount      	= $var['amount'];
		$type       	= $var['type'];


		$sql7="SELECT * FROM `phonebook` WHERE gid='$grouplist' and status=1 and mobile!=''";
		$query89 = $this->db->query($sql7);
		foreach ($query89->result() as $row7)
		{
		if(!empty($chamount)) {
		$amount=$chamount;
		}else {
		$amount=$row7->amount;
		}
		$number=$row7->mobile;
		$type=$row7->type;

		$this->irs->recharge($serviceget,$number,$amount,$type,$uid,$pincode,'yes');
		}



		/*
		$N = count($number);
		for($i=0; $i < $N; $i++)
		{

		$this->irs->recharge($serviceget,$number[$i],$amount[$i],$type[$i],$uid,$pincode,'yes');
		}
		*/

		$getirdata= $this->session->userdata('irslog');
		$irstsview = $getirdata['irsts'];
		$irsmessage = $getirdata['irsmessage'];

		if($irstsview=="error") {
		$this->session->set_flashdata('error', $irsmessage);

		redirect('main/bulkflexi/', 'refresh');

		}else {

			$msg= 'New Request Bulkflexi '.$number.' and amount '.$amount;
			$this->mit->InsertActivetUser($uid,$msg);


			$this->session->set_flashdata('success', $irsmessage);

			redirect('main/bulkflexi/', 'refresh');
		}


		}
	}

	 public function bulk_add()
	{
		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		$parent= $user->p_id;
		$prebalance = $user->balance;

		if($this->input->post()){

   	 	$var=$this->input->post();
		$title       = $var['title'];
		$qty       	= $var['bulkqty'];


		$groupinsert = array(
					'user_id'=>$uid,
					'group_name'=>$title,
					'qty'=>$qty,
					'status'=>1,
					'date'=>$this->date_time()
					);

		$this->db->insert('bulkflexi',$groupinsert);



		$idlast = $this->db->insert_id();

		redirect('main/bulkflexi_view/'.$this->mdb->passwordChanger('encrypt', $idlast), 'refresh');


		}else {
		$data['page_title'] = ' Bulk Flexi Add';

		$data['page_name'] = 'bulk_add';
		}

		$this->load->view('mit/index', $data);
	}


	public function bulkflexi_view($bfid="")
	{
		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		$parent= $user->p_id;
		$prebalance = $user->balance;


		$bulkid =$this->mdb->passwordChanger('decrypt', $bfid);

		$groupname = $this->db->get_where('bulkflexi',array('id' =>$bulkid))->row()->group_name;


		if($this->input->post()){

   	 	$var=$this->input->post();
		$number       = $var['number'];
		$amount       	= $var['amount'];
		$status       	= $var['status'];
		$type       	= $var['type'];

		$N = count($number);
		for($i=0; $i < $N; $i++)
		{
		//if(!empty($number[$i]) && !empty($amount[$i]) ) {
		$phonebookinsert = array(
					'user_id'=>$uid,
					'mobile'=>$number[$i],
					'amount'=>$amount[$i],
					'type'=>$type[$i],
					'gid'=>$bulkid,
					'status'=>$status[$i],
					'group_name'=>$groupname,
					'date'=>$this->date_time()
					);

		$this->db->insert('phonebook',$phonebookinsert);
		//}
		}
		redirect('main/bulkflexi/', 'refresh');


		}else {

		$data['bulkid'] = $bulkid;
		$data['page_title'] = $groupname. ' Bulk Flexi Add';

		$data['page_name'] = 'bulkflexi_view';
		}

		$this->load->view('mit/index', $data);
	}

	public function bulkManag($para1="", $para2="")
	{
		$user=$this->session->userdata('member_session');
		$uid= $user->id;

		$bulkid =$this->mdb->passwordChanger('decrypt', $para2);
		$groupname = $this->db->get_where('bulkflexi',array('id' =>$bulkid))->row()->group_name;
		$var=$this->input->post();

		if($para1=="delete") {

			$this->db->where('id', $para2);
			$this->db->where('user_id', $uid);
		    $this->db->delete('bulkflexi');
			$this->session->set_flashdata('success', 'Delete Successful');

			redirect('main/bulkManag/', 'refresh');
		}
		else if($para1=="edit") {

		$data['bulkid'] = $bulkid;
		$data['page_title'] = $groupname. ' Bulk Flexi Group Edit';


		$data['page_name'] = 'bulk_group_edit';
		}else if($para1=="update") {

		$title       = $var['title'];
		$qty       	= $var['bulkqty'];

			$groupupdate = array(
					'group_name'=>$title,
					'qty'=>$qty,
					'status'=>1,
					'mod_date'=>$this->date_time()
					);

		$this->db->where('id',$bulkid);

		$this->db->update('bulkflexi',$groupupdate);

		redirect('main/bulkManag/', 'refresh');

		}else if($para1=="numedit") {
		$data['bulkid'] = $bulkid;
		$data['page_title'] = $groupname.' Bulk Flexi Phone Edit';
		$data['page_name'] = 'bulkflexi_phonebook_edit';

		}else if($para1=="numupdate") {
		$var=$this->input->post();
		$number       	= $var['number'];
		$amount       	= $var['amount'];
		$bulkstatus     = $var['bulkstatus'];
		$type       	= $var['type'];
		$phonebid       = $var['phonebid'];
		$pincode       = $var['pincode'];

		$utrye = 'user';
		$chking= $this->mit->otpchk($pincode,$utrye);
		if($chking) {

		$N = count($number);
		for($i=0; $i < $N; $i++)
		{
		//if(!empty($number[$i]) && !empty($amount[$i]) ) {
		$phonebookupdate = array(
					'mobile'=>$number[$i],
					'amount'=>$amount[$i],
					'type'=>$type[$i],
					'gid'=>$bulkid,
					'status'=>$bulkstatus[$i],
					'group_name'=>$groupname,
					'mod_date'=>$this->date_time()
					);

		$this->db->where('id',$phonebid[$i]);
		$this->db->update('phonebook',$phonebookupdate);
		}
		}else {
		$this->session->set_flashdata('error', 'PIN not valid');
		}
		redirect('main/bulkManag/', 'refresh');

		}else {
		$data['page_title'] = ' Bulk Flexi Manag';
		$data['page_name'] = 'bulkflexi_managList';
		}
		$this->load->view('mit/index', $data);
	}

	public function req($service="")
	{
		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		$mybalance= $user->balance;
		$balancelimit= $user->balance_limit;

		$idate = date('Y-m-d');

		if($this->input->post()){


   	 	$var=$this->input->post();
		$number       = $var['acnumber'];
		$amount       = $var['amount'];
		$type         = $var['type'];
		$serviceget   = $var['service'];

		$sqlmod="SELECT * from module where serviceid='$serviceget' and status=1";
		$queryser = $this->db->query($sqlmod);

					foreach ($queryser->result() as $rowmod)
					{
						$service_auto_limit=$rowmod->auto_limit;
						$requr_pin=$rowmod->requr_pin;
						$requr_nid=$rowmod->nid;
						$requr_name=$rowmod->name;
						$requr_sender_number=$rowmod->sender_number;
						$minamount=$rowmod->min_amount;
						$maxamount=$rowmod->max_amount;

						$minlength=$rowmod->min_length;
						$maxlength=$rowmod->max_length;
					}

		$this->form_validation->set_rules('acnumber', 'Number', 'trim|required|xss_clean|max_length[14]');
		$this->form_validation->set_rules('amount', 'Amount', 'trim|required|xss_clean|max_length[6]');

	if ($this->form_validation->run() == FALSE) {
		//$data = $this->data['form_validation'] = validation_errors();

		$data['page_title'] = $service.' Request';

		$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

		$data['page_name'] = 'req_new';

		//$this->load->view('member/index', $data);

	}else {



		$mobile=trim($number);
		if($serviceget==64){
		$firstThreeDigit=substr($mobile,0,3);
		}else {
		$firstThreeDigit=substr($mobile,0,2);
		}
		$numberlenth=strlen($number);

		$myacc_tarf = $this->db->get_where('reseller',array('id' =>$uid))->row()->tarif;

		if($serviceget==64){

$country_op_status=$this->db->get_where('country_op',array('oprefix' =>$firstThreeDigit))->row()->status;
if($country_op_status==0) {

$this->session->set_flashdata('error', 'Oparetor Off at this time');
$data['page_title'] = $service.' Request';
$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;
$data['page_name'] = 'req_new';

		}else {

		}
		}



		$sql_rate="SELECT * from rate_module where tarif='$myacc_tarf' and service='$serviceget' and prefix='$firstThreeDigit' and type='$type' and status=1";

		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{
						 $mrservice=$raterow->service;
						 $prefix=$raterow->prefix;
						 $rate=1;
						 $comm=$raterow->comm;
						 $charge=$raterow->charge;
						 $prcode=$raterow->pcode;
					}


						 if(empty($prcode))$prcode=$pcode;
      $amus=floor($amount);
		$sql_ratef="SELECT * from drive_package where volume='$prcode' and price='$amus' and status='1'";


		$queryratef = $this->db->query($sql_ratef);
		  foreach ($queryratef->result() as $raterowf)
					{

						 $comm_f=$raterowf->comm;
						 $charge_f=$raterowf->charge;

					}


						 $ratesrate=$amount*$rate;
						 $comm_rate=$amount*$comm/100;
      	 $comm_rate=$comm_f+$comm_rate;
						 $charge_rate=$amount*$charge/100;
						 $final_amount=$ratesrate+$charge_rate-$comm_rate;




			$intrvalt = $this->db->get_where('security_option',array('id' =>1))->row()->req_Interval*60;
			$bkash_auto = $this->db->get_where('security_option',array('id' =>1))->row()->bkash_auto;

			$reseller_dayli_limit = $this->db->get_where('security_option',array('id' =>1))->row()->reseller_dayli_limit;

			$min=$this->mit->checkMobile($number,$serviceget);



$sqllmt="select balance,idate from sendflexi where userid='$uid' and idate='$idate'";
$querylmt = $this->db->query($sqllmt);
		  foreach ($querylmt->result() as $row24)
					{

						$limitdate=$row24->userid;
						$limitbl=$row24->balance;
						$agrebal+=$limitbl;
					}



					$availablebal=$mybalance-$balancelimit;

				if($availablebal>=$final_amount && is_numeric($amount)){
				$topuser=$this->mit->countBalance($uid,$final_amount);

				if($topuser=="TRUE") {

				if($amount>=$minamount && $amount<=$maxamount) {

				if($numberlenth>=$minlength && $numberlenth<=$maxlength) {

				if($min>$intrvalt || $min==0){

				if($agrebal<$reseller_dayli_limit) {



$blanc_chk=$this->mit->reverse_treeBalanceChk($uid,$amount,$serviceget,$prcode);
if($blanc_chk=="TRUE") {




					/// request review

		$data['page_title']   = $service.' Request';
		$data['pcode']        = $prcode;
		$data['type']         = $type;
		$data['finalamount']  = $final_amount;
		$data['requr_pin']    = $requr_pin;
		$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;
		$data['page_name'] = 'req_review';

					/// request review finsih



				}else { /// number lenth min max
				$this->session->set_flashdata('error', 'Dear User Your Product Not Found');
				$data['page_title'] = $service.' Request';
				$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;
				$data['page_name'] = 'req_new';
				}

				}else { /// number lenth min max
				$this->session->set_flashdata('error', 'Dear User Your Daily Limit Over');
				$data['page_title'] = $service.' Request';
				$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;
				$data['page_name'] = 'req_new';
				}


				}else { /// intval time chk lenth min max

				$remain=$intrvalt-$min;

$this->session->set_flashdata('error', 'Please request this Mobile '.$number.' after '.$remain.' Seconds');
     $data['page_title'] = $service.' Request';

$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

				$data['page_name'] = 'req_new';

				}



				}else { /// number lenth min max

				$this->session->set_flashdata('error', 'Minimum Number: '.$minlength. ' And Maximum Number: '. $maxlength);
				$data['page_title'] = $service.' Request';

			$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

				$data['page_name'] = 'req_new';

				}
				}else { /// amount min max

				$this->session->set_flashdata('error', 'Minimum Amount:' .$minamount.'  And Maximum Amount: '.$maxamount);
				$data['page_title'] = $service.' Request';

			$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

				$data['page_name'] = 'req_new';

				}



				}else { /// uplevel balance chk

				$this->session->set_flashdata('error', 'Contact with your Parent');
				$data['page_title'] = $service.' Request';

			$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

				$data['page_name'] = 'req_new';

				}


				}else { /// salf balance chk


				$this->session->set_flashdata('error', 'Insufficient Balance in your Account');
				$data['page_title'] = $service.' Request';

			$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

				$data['page_name'] = 'req_new';

				}


		} //

		/// post finish
		}else {

		$data['page_title'] = $service.' Request';

		$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

		$data['page_name'] = 'req_new';
		}
		$this->load->view('mit/index', $data);
	}




		public function req_in($service="")
	{
		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		$mybalance= $user->balance;
		$balancelimit= $user->balance_limit;

		$idate = date('Y-m-d');

		if($this->input->post()){


   	 	$var=$this->input->post();
		$number       = $var['acnumber'];
		$amount       = $var['amount'];
		$type         = $var['type'];
		$serviceget   = $var['service'];

		$sqlmod="SELECT * from module where serviceid='$serviceget' and status=1";
		$queryser = $this->db->query($sqlmod);

					foreach ($queryser->result() as $rowmod)
					{
						$service_auto_limit=$rowmod->auto_limit;
						$requr_pin=$rowmod->requr_pin;
						$requr_nid=$rowmod->nid;
						$requr_name=$rowmod->name;
						$requr_sender_number=$rowmod->sender_number;
						$minamount=$rowmod->min_amount;
						$maxamount=$rowmod->max_amount;

						$minlength=$rowmod->min_length;
						$maxlength=$rowmod->max_length;
					}

		$this->form_validation->set_rules('acnumber', 'Number', 'trim|required|xss_clean|max_length[14]');
		$this->form_validation->set_rules('amount', 'Amount', 'trim|required|xss_clean|max_length[6]');

	if ($this->form_validation->run() == FALSE) {
		//$data = $this->data['form_validation'] = validation_errors();

		$data['page_title'] = $service.' Request';

		$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

		$data['page_name'] = 'req_international';

		//$this->load->view('member/index', $data);

	}else {



		$mobile=trim($number);
		if($serviceget==64){
		$firstThreeDigit=substr($mobile,0,3);
		}else {
		$firstThreeDigit=substr($mobile,0,2);
		}
		$numberlenth=strlen($number);

		$myacc_tarf = $this->db->get_where('reseller',array('id' =>$uid))->row()->tarif;

		if($serviceget==64){

$country_op_status=$this->db->get_where('country_op',array('oprefix' =>$firstThreeDigit))->row()->status;
if($country_op_status==0) {

$this->session->set_flashdata('error', 'Oparetor Off at this time');
$data['page_title'] = $service.' Request';
$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;
$data['page_name'] = 'req_international';

		}else {

		}
		}



		$sql_rate="SELECT * from rate_module where tarif='$myacc_tarf' and service='$serviceget' and prefix='$firstThreeDigit' and type='$type' and status=1";

		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{
						 $mrservice=$raterow->service;
						 $prefix=$raterow->prefix;
						 $rate=$raterow->rate;
						 $comm=$raterow->comm;
						 $charge=$raterow->charge;
						 $prcode=$raterow->pcode;
					}

						 $ratesrate=$amount*$rate;
						 $comm_rate=$amount*$comm/100;
						 $charge_rate=$amount*$charge/100;
						 $final_amount=$ratesrate+$charge_rate-$comm_rate;

			$intrvalt = $this->db->get_where('security_option',array('id' =>1))->row()->req_Interval*60;
			$bkash_auto = $this->db->get_where('security_option',array('id' =>1))->row()->bkash_auto;

			$reseller_dayli_limit = $this->db->get_where('security_option',array('id' =>1))->row()->reseller_dayli_limit;

			$min=$this->mit->checkMobile($number,$serviceget);



$sqllmt="select balance,idate from sendflexi where userid='$uid' and idate='$idate'";
$querylmt = $this->db->query($sqllmt);
		  foreach ($querylmt->result() as $row24)
					{

						$limitdate=$row24->userid;
						$limitbl=$row24->balance;
						$agrebal+=$limitbl;
					}



					$availablebal=$mybalance-$balancelimit;

				if($availablebal>=$final_amount && is_numeric($amount)){
				$topuser=$this->mit->countBalance($uid,$final_amount);

				if($topuser=="TRUE") {

				if($amount>=$minamount && $amount<=$maxamount) {

				if($numberlenth>=$minlength && $numberlenth<=$maxlength) {

				if($min>$intrvalt || $min==0){

				if($agrebal<$reseller_dayli_limit) {



$blanc_chk=$this->mit->reverse_treeBalanceChk($uid,$amount,$serviceget,$prcode);
if($blanc_chk=="TRUE") {




					/// request review

		$data['page_title']   = $service.' Request';
		$data['pcode']        = $prcode;
		$data['type']         = $type;
		$data['finalamount']  = $final_amount;
		$data['requr_pin']    = $requr_pin;
		$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;
		$data['page_name'] = 'req_review';

					/// request review finsih



				}else { /// number lenth min max
				$this->session->set_flashdata('error', 'Dear User Your Product Not Found');
				$data['page_title'] = $service.' Request';
				$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;
				$data['page_name'] = 'req_international';
				}

				}else { /// number lenth min max
				$this->session->set_flashdata('error', 'Dear User Your Daily Limit Over');
				$data['page_title'] = $service.' Request';
				$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;
				$data['page_name'] = 'req_international';
				}


				}else { /// intval time chk lenth min max

				$remain=$intrvalt-$min;

$this->session->set_flashdata('error', 'Please request this Mobile '.$number.' after '.$remain.' Seconds');
     $data['page_title'] = $service.' Request';

$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

				$data['page_name'] = 'req_international';

				}



				}else { /// number lenth min max

				$this->session->set_flashdata('error', 'Minimum Number: '.$minlength. ' And Maximum Number: '. $maxlength);
				$data['page_title'] = $service.' Request';

			$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

				$data['page_name'] = 'req_international';

				}
				}else { /// amount min max

				$this->session->set_flashdata('error', 'Minimum Amount:' .$minamount.'  And Maximum Amount: '.$maxamount);
				$data['page_title'] = $service.' Request';

			$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

				$data['page_name'] = 'req_international';

				}



				}else { /// uplevel balance chk

				$this->session->set_flashdata('error', 'Contact with your Parent');
				$data['page_title'] = $service.' Request';

			$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

				$data['page_name'] = 'req_international';

				}


				}else { /// salf balance chk


				$this->session->set_flashdata('error', 'Insufficient Balance in your Account');
				$data['page_title'] = $service.' Request';

			$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

				$data['page_name'] = 'req_international';

				}


		} //

		/// post finish
		}else {

		$data['page_title'] = $service.' Request';

		$data['servicesid'] = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

		$data['page_name'] = 'req_international';
		}
		$this->load->view('mit/index', $data);
	}




	 public function pending() {

		$member = $this->session->userdata('member_session');

		$uid= $member->id;

		$idate = date('Y-m-d');

		$data['page_title'] = 'Pending history';

		$serviceid = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

		$sql="select * from sendflexi where (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') order by id desc";

		$query2 = $this->db->query($sql);

		$data['pending_history'] = $query2->result();

		$data['page_name'] = 'pending_history';
		$this->load->view('mit/index', $data);
	 }



	public function history($servicename="",$page="", $from1="", $to1="", $resel="", $op="", $status="", $limit="")
	{


		$member = $this->session->userdata('member_session');

		$uid= $member->id;

		$idate = date('Y-m-d');

		$var=$this->input->post();
		$varget=$this->input->get();

		if($_POST){

		$resel=$var["resel"];
		$from1=$var["from1"];
		$to1=$var["to1"];
		$status=$var["status"];
		$op=$var["op"];
		$number=$var["number"];
		$limit=$var["limit"];
		}


		else {

		$resel=$varget["resel"];
		$from1=$varget["from1"];
		$to1=$varget["to1"];
		$status=$varget["status"];
		$op=$varget["op"];
		$number=$varget["number"];
		$limit=$varget["limit"];
	}




		if(!empty($op)) {
		$servicename = $this->db->get_where('module',array('serviceid' =>$op))->row()->title;
		}




		 if(empty($limit) ) {
		 $limit = 50;
		 }


		   $link="from1=$from1&to1=$to1&resel=$resel&op=$op&status=$status&number=$number&limit=$limit";


		$targetpage = "?";
		  //your file name  (the name of this file)
		  $pageselect=$varget['pageid'];

				 //how many items to show per page

		  $page = $varget['page'];
		  if($page)
			$start = ($page - 1) * $limit;       //first item to display on this page
		  else
			$start = 0;                //if no page var is given, set start to 0




		 if($from1!="" and $to1!="" and $resel=="none" and $op=="all" and $status=="all" and $number=="")
		  {

		//only date tot date //
		 $sql="SELECT * FROM sendflexi WHERE idate >= '$from1' AND idate <= '$to1' AND (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') ORDER BY id desc LIMIT $start, $limit";

		  $sqlcont="SELECT * FROM sendflexi WHERE idate >= '$from1' AND idate <= '$to1' AND (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') ORDER BY id desc";



		  }

		   // service and user and number
		  elseif($from1!="" and $to1!="" and $resel!="none" and $op!="all" and $status=="all" and $number!="")
		  {

		if($resel==$uid) {
		 $sql="select * from sendflexi where phone ='$number' and userid='$uid' and service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit";

		    $sqlcont="select * from sendflexi where phone ='$number' and userid='$uid' and service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc";



		  }else {

		  $sql="select * from sendflexi where phone ='$number' and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit";

		$sqlcont="select * from sendflexi where phone ='$number' and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc";


		   }

		  }

		   // service and user and number
		  elseif($from1!="" and $to1!="" and $resel=="none" and $op!="all" and $status!="all" and $number!="")
		  {


		  $sql="select * from sendflexi where phone ='$number' and status='$status' and service='$op' and idate >= '$from1' and idate <= '$to1' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') order by id desc  LIMIT $start, $limit";

		   $sqlcont="select * from sendflexi where phone ='$number' and status='$status' and service='$op' and idate >= '$from1' and idate <= '$to1' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') order by id desc";



		  }


		  elseif($from1!="" and $to1!="" and $resel=="none" and $op!="all" and $status=="all" and $number=="")
		  {

		// date and oparetor //
		  $sql="SELECT * FROM sendflexi WHERE idate >= '$from1' and idate <= '$to1' and service='$op' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid')  ORDER BY id desc LIMIT $start, $limit";

		  $sqlcont="SELECT * FROM sendflexi WHERE idate >= '$from1' and idate <= '$to1' and service='$op' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid')  ORDER BY id desc";



		  // date and reseller
		 }
		 elseif($from1!="" and $to1!="" and $resel!="none" and $op=="all" and $status=="all" and $number=="")
		  {

		if($resel==$uid) {

		 $sql="select * from sendflexi where userid='$uid' and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit";

		   $sqlcont="select * from sendflexi where userid='$uid' and idate >= '$from1' and idate <= '$to1' order by id desc ";



		  }else {
		  $sql="select * from sendflexi where (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit";

		   $sqlcont="select * from sendflexi where (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1' order by id desc";



		}

		  // date and service and reseller
		}elseif($from1!="" and $to1!="" and $resel!="none" and $op!="all" and $status=="all" and $number=="")
		  {

		if($resel==$uid) {

		 $sql="select * from sendflexi where userid='$uid' and service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit";

		  $sqlcont="select * from sendflexi where userid='$uid' and service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc";



		}else {
		  $sql="select * from sendflexi where userid='$uid' and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit";

		   $sqlcont="select * from sendflexi where userid='$uid' and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc";



		  }


		} elseif($from1!="" and $to1!="" and $resel!="none" and $op=="all" and $status!="all" and $number=="")
		  {

		if($resel==$uid) {

		  $sql="select * from sendflexi where userid='$uid' and status='$status' and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit";

		   $sqlcont="select * from sendflexi where userid='$uid' and status='$status' and idate >= '$from1' and idate <= '$to1' order by id desc";



		}else {
		  $sql="select * from sendflexi where (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and status='$status' and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit";

		   $sqlcont="select * from sendflexi where (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and status='$status' and idate >= '$from1' and idate <= '$to1' order by id desc";



		  }


		  }
		  //date, service, status,//
		  elseif($from1!="" and $to1!="" and $resel=="none" and $op!="all" and $status!="all" and $number=="")
		  {

		// date and oparetor
		  $sql="select * from sendflexi where status='$status' and service='$op' and idate >= '$from1' and idate <= '$to1' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') order by id desc  LIMIT $start, $limit";

		   $sqlcont="select * from sendflexi where status='$status' and service='$op' and idate >= '$from1' and idate <= '$to1' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') order by id desc";



		  }

		  //date and status
		   elseif($from1!="" and $to1!="" and $resel=="none" and $op=="all" and $status!="all" and $number=="")
		  {
		//date and status

		  $sql="select * from sendflexi where status='$status' and idate >= '$from1' and idate <= '$to1' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid') order by id desc  LIMIT $start, $limit";

		   $sqlcont="select * from sendflexi where status='$status' and idate >= '$from1' and idate <= '$to1' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid') order by id desc";



		  }


		  // service and status and userid/reseller

		   elseif($from1!="" and $to1!="" and $resel!="none" and $op!="all" and $status!="all" and $number=="")
		  {
		  // date

		  if($resel==$uid) {

		   $sql="SELECT * FROM sendflexi WHERE idate >= '$from1' and idate <= '$to1' and userid='$uid' and status='$status' and service='$op' ORDER BY id desc  LIMIT $start, $limit";

		   $sqlcont="SELECT * FROM sendflexi WHERE idate >= '$from1' and idate <= '$to1' and userid='$uid' and status='$status' and service='$op' ORDER BY id desc";



		  }else {

		  $sql="SELECT * FROM sendflexi WHERE idate>='$from1' and idate<='$to1' and status='$status' and service='$op' and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') ORDER BY id desc  LIMIT $start, $limit";

		    $sqlcont="SELECT * FROM sendflexi WHERE idate>='$from1' and idate<='$to1' and status='$status' and service='$op' and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') ORDER BY id desc";



		  }


		  }


			elseif($from1!="" and $to1!="" and $resel=="none" and $op=="all" and $status=="all" and $number=="")
		  {
		  // date
		  $sql="SELECT * FROM sendflexi WHERE idate >= '$from1' and idate <= '$to1' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') ORDER BY id desc  LIMIT $start, $limit";

		  $sqlcont="SELECT * FROM sendflexi WHERE idate >= '$from1' and idate <= '$to1' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') ORDER BY id desc";




		  }



		  //all query
		   elseif($from1!="" and $to1!="" and $resel!="none" and $op!="all" and $status!="all" and $number!="")
		  {

		   if($resel==$uid) {

		  $sql="select * from sendflexi where `phone` LIKE '%$number%' and userid='$uid' and idate >= '$from1' and idate <= '$to1' and service='$op' and status='$status' order by id desc LIMIT $start, $limit";

		    $sqlcont="select * from sendflexi where `phone` LIKE '%$number%' and userid='$uid' and idate >= '$from1' and idate <= '$to1' and service='$op' and status='$status' order by id desc";


		  }else {

		  $sql="select * from sendflexi where `phone` LIKE '%$number%' and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1' and service='$op' and status='$status' order by id desc LIMIT $start, $limit";

		   $sqlcont="select * from sendflexi where `phone` LIKE '%$number%' and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1' and service='$op' and status='$status' order by id desc";



		  }


		  }

			elseif($from1!="" and $to1!="" and $resel!="none" and $op=="all" and $status!="all" and $number!="")
		  {
		// date and reseller and status and number
		 if($resel==$uid) {
		  $sql="select * from sendflexi where `phone` LIKE '%$number%' and userid='$uid' and idate >= '$from1' and idate <= '$to1'  and status='$status' order by id desc LIMIT $start, $limit";

		    $sqlcont="select * from sendflexi where `phone` LIKE '%$number%' and userid='$uid' and idate >= '$from1' and idate <= '$to1'  and status='$status' order by id desc";



		 }else {
		  $sql="select * from sendflexi where `phone` LIKE '%$number%' and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1'  and status='$status' order by id desc LIMIT $start, $limit";

		   $sqlcont="select * from sendflexi where `phone` LIKE '%$number%' and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1'  and status='$status' order by id desc";



		  }


		  }

		   elseif($from1!="" and $to1!="" and $resel!="none" and $op=="all" and $status=="all" and $number!="")
		  {

		// date and reseller and number

		 if($resel==$uid) {
		  $sql="select * from sendflexi where `phone` LIKE '%$number%' and userid='$uid' and idate >= '$from1' and idate <= '$to1' order by id desc LIMIT $start, $limit";

		    $sqlcont="select * from sendflexi where `phone` LIKE '%$number%' and userid='$uid' and idate >= '$from1' and idate <= '$to1' order by id desc";



		  }else {

		  $sql="select * from sendflexi where `phone` LIKE '%$number%' and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1' order by id desc LIMIT $start, $limit";

		   $sqlcont="select * from sendflexi where `phone` LIKE '%$number%' and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1' order by id desc";



		  }


		  }

			elseif($from1!="" and $to1!="" and $resel=="none" and $op!="all" and $status=="all" and $number!="")
		  {
		  // date and oparetor and number

		  $sql="select * from sendflexi where `phone` LIKE '%$number%' and service='$op' and idate >= '$from1' and idate <= '$to1' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') order by id desc LIMIT $start, $limit";

		  $sqlcont="select * from sendflexi where `phone` LIKE '%$number%' and service='$op' and idate >= '$from1' and idate <= '$to1' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') order by id desc";



		  }
		//date and number

		   elseif($from1!="" and $to1!="" and  $resel=="none" and $op=="all" and $status=="all" and $number!=""){

			$sql="select * from sendflexi where `phone` LIKE '%$number%' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') order by id desc LIMIT $start,$limit";


		    $sqlcont="select * from sendflexi where `phone` LIKE '%$number%' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') order by id desc";



		  }

		  else{

		  $idate=date('Y-m-d');

		  $op = $this->db->get_where('module',array('title' =>$servicename))->row()->serviceid;

		  if($servicename=="all") {

		  $sql="SELECT * from sendflexi where idate='$idate' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid')  order by id desc LIMIT $start,$limit";


		 $sqlcont="SELECT * from sendflexi where idate='$idate' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid')  order by id desc";

		  }else {

		  $sql="SELECT * from sendflexi where idate='$idate' and service='$op' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid')  order by id desc LIMIT $start,$limit";

		 $sqlcont="SELECT * from sendflexi where idate='$idate' and service='$op' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid')  order by id desc";

		  }


		  }

		  $querycount = $this->db->query($sqlcont);
		  if($querycount->num_rows() > 0 ) {
		  $total_pages = $querycount->num_rows;
		  }


		$data['number'] = $number;
		$data['resel'] = $resel;
		$data['op'] = $op;

		$data['status'] = $status;
		$data['limit'] = $limit;
		$data['to1'] = $to1;
		$data['from1'] = $from1;

		$pagination = $this->mdb->pagelink($page,$total_pages,$limit,$link);


		$query2 = $this->db->query($sql);

		$data['page_title'] = $servicename.' history';
		$data['links'] = $pagination;
		$data['recharge_history'] = $query2->result();
		$data['page_name'] = 'history';
		$this->load->view('mit/index', $data);
	}

	public function trns($page="", $from1="", $to1="", $resel="", $notetr="", $limit=""){

	$member=$this->session->userdata('member_session');
	$uid= $member->id;

	$var=$this->input->post();
	$varget=$this->input->get();
	if($_POST){
	$resel=$var["resel"];

	$from1=$var["from1"];
	$to1=$var["to1"];

	$notetr=$var["notetr"];

	$limit=$var["limit"];
	}else {

	$resel=$varget["resel"];

	$from1=$varget["from1"];
	$to1=$varget["to1"];

	$notetr=$varget["notetr"];

	$limit=$varget["limit"];

	}

	if(empty($limit) ) {
		 $limit = 50;
		 }


	$link="from1=$from1&to1=$to1&limit=$limit";
	$targetpage = "?";
	 //your file name  (the name of this file)
	$pageselect=$varget['pageid'];
		 //how many items to show per page
	$page = $varget['page'];
	if($page)
	$start = ($page - 1) * $limit;       //first item to display on this page
	else
	$start = 0;                //if no page var is given, set start to 0

	// data query start here

	if($from1!="" and $to1!="")
	{

	$sql="SELECT * FROM trans WHERE  date >= '$from1' and date <= '$to1' and userid='$uid'  ORDER BY id desc LIMIT $start, $limit";

	$sqlcont="SELECT * from trans where date >= '$from1' and date <= '$to1' and userid='$uid' ";

	}else {

	$sql="select * from trans where userid='$uid' order by id desc LIMIT $start,$limit";

	 $sqlcont="SELECT * from trans where userid='$uid'";
	}


	  $querycount = $this->db->query($sqlcont);
		  if($querycount->num_rows() > 0 ) {
		  $total_pages = $querycount->num_rows;
	}

	$pagination = $this->mdb->pagelink($page,$total_pages,$limit,$link);

	$query2 = $this->db->query($sql);

	$data['limit'] = $limit;


	$data['to1'] = $to1;
	$data['from1'] = $from1;


	$data['links'] = $pagination;
	$data['trns_list'] = $query2->result();

	$data['page_title'] = 'Total Transaction Report ';
	$data['page_name'] = 'reseller_transaction';
	$this->load->view('mit/index', $data);


	}

	public function rate($uid="")
	{
		$get=$this->input->get();
		$var=$this->input->post();
		if($this->input->post() && !empty($cname))
		 {}else {

		$residhex=$this->mdb->passwordChanger('decrypt', $uid);
		$member=$this->session->userdata('member_session');
		$uid= $member->id;
		$username = $this->db->get_where('reseller',array('id' =>$residhex))->row()->username;
		$pid = $this->db->get_where('reseller',array('id' =>$residhex))->row()->p_id;
		$ctype = $this->db->get_where('reseller',array('id' =>$residhex))->row()->custype;

		 $parent2 = $this->db->get_where('reseller',array('id' =>$residhex))->row()->p_id;
		 $parent3 = $this->db->get_where('reseller',array('id' =>$parent2))->row()->p_id;
		 $parent4 = $this->db->get_where('reseller',array('id' =>$parent3))->row()->p_id;
		 $parent5 = $this->db->get_where('reseller',array('id' =>$parent4))->row()->p_id;

		if($pid==$uid or $parent2==$uid or $parent3==$uid or $parent4==$uid or $parent5==$uid) {


		$tarif = $this->db->get_where('reseller',array('id' =>$residhex))->row()->tarif;

		$username = $this->db->get_where('reseller',array('id' =>$residhex))->row()->username;

		 $prefix=$var['prefix'];

         $opname=$var['opname'];


		if($prefix!="" && $opname==""){
		$sql="select * from rate_module where `prefix` = '$prefix' and tarif='$tarif'";
		} else if($opname!="" && $prefix==""){
		$sql="select * from rate_module where `service` = '$opname' and tarif='$tarif'";

		} else if($opname!="" && $prefix!=""){
		$sql="select * from rate_module where service = '$opname' and prefix = '$prefix' and tarif='$tarif'";
		}else {
		$sql="select * from rate_module where  tarif='$tarif' order by id asc";
		}


		//$sql="select * from rate_module where tarif='$tarif' order by id asc";

		$query11 = $this->db->query($sql);
		$reselerrate = $query11->result() ;
		$data["tarifid"] = $tarif;
		$data["reseller_rate"] = $reselerrate;
		$data["links"] = $pagination;
		$data["username"] = $username;
		$data["resuserid"] = $residhex;
		$data['prefix']=$prefix;
        $data['opname']=$opname;
		$data['page_title'] = 'Reseller '. $username;
		$data['page_name'] = 'rate';
		$this->load->view('mit/index', $data);

	}else {

		redirect('main/resellers/', 'refresh');
	}

		 }
	}

	public function rateUpdate($uid="")
	{
		$var=$this->input->post();
		$tarpcode=$var['tarpcode'];
		$edit_com=$var['edit_com'];
		$ratestatus=$var['ratestatus'];
        $edit_charge=$var['edit_charge'];
		$tarifid=$var['tarifid'];
		$cnt=count($var['edit_com']);
		$userid=$var['userid'];

		for($i=0;$i<$cnt;$i++)
		{
		$del_id=$var['chkbox'];


		$queryrate="UPDATE  `rate_module` SET `comm` =  '$edit_com[$i]',
		`charge` =  '$edit_charge[$i]', `status` =  '$ratestatus[$i]' where id='$del_id[$i]'";

		$this->db->query($queryrate);




		}

		$user=$this->session->userdata('member_session');
		$uids= $user->id;
		$msg= 'Edit Rate Reseller: '.$userid;
		$this->mit->InsertActivetUser($uids,$msg);

		$this->session->set_flashdata('success', 'Update Successful');

		redirect('main/resellers/', 'refresh');



	}

	public function rateSync($uid="") {

		$create_date = date('Y-m-d H:i:s');

		$member = $this->session->userdata('member_session');

		$sessuid = $member->id;

		$residhex=$this->mdb->passwordChanger('decrypt', $uid);

$trif_id = $this->db->get_where('reseller',array('id' =>$residhex))->row()->tarif;

$userid = $this->db->get_where('reseller',array('id' =>$residhex))->row()->id;

		$sql353="SELECT * from rate_module where userid='$sessuid'";

		$query2 = $this->db->query($sql353);
		foreach ($query2->result() as $rowprice)
					{

		$opname=$rowprice->service;

		$country=$rowprice->country;

		$operator=$rowprice->oparetor;
		$phonecode=$rowprice->c_code;
		$opcode=$rowprice->opcode;

		$priceid=$rowprice->id;

		$prefix=$rowprice->prefix;
		$rate=$rowprice->rate;

		$pcode=$rowprice->pcode;

		$comm=$rowprice->comm;
		$charge=$rowprice->charge;
		$type=$rowprice->type;


		$query = $this->db->query("select * from rate_module where tarif='$trif_id' and pcode='$pcode' and service='$opname' and prefix='$prefix'");


		if($query->num_rows() == 0 ) {

		$sqlratein="INSERT INTO `rate_module` (`id`, `country`, `c_code`, `oparetor`, `opcode`, `tarif`, `userid`, `create_by`, `service`, `prefix`, `pcode`, `type`, `rate`, `comm`, `charge`, `price_id`, `status`, `date`) VALUES (NULL, '$country', '$phonecode', '$operator', '$opcode', '$trif_id', '$userid', '$sessuid', '$opname', '$prefix', '$pcode', '$type', '$rate', '$comm', '$charge', '$priceid', '1', '$create_date')";

		$this->db->query($sqlratein);
		}

					}


		redirect('main/resellers/', 'refresh');

	}

	public function resellers($lev="",$page="" ,$cname="", $limit="")
	{
			$idc = explode(".", $cname);

		$get=$this->input->get();
		$var=$this->input->post();

		if($_POST) {

		$cname = $var['cname'];
		$limit = $var['limit'];

		}else {

		$cname = $get['cname'];
		$limit = $get['limit'];

		}

		if(empty($limit) ) {
		 $limit = 50;
		 }

		$targetpage = "?";
		//your file name  (the name of this file)
		              //how many items to show per page
		//$page = $get['page'];
		if($page)
		$start = ($page - 1) * $limit;       //first item to display on this page
		else
		$start = 0;


		$member = $this->session->userdata('member_session');

		$sessuid = $member->id;
		$lr=$this->mit->leftRight($sessuid);
		$lft=$lr[0];
		$rgt=$lr[1];

		if($cname!="") {

			$sql="select * from reseller where `username` LIKE '%$cname%' and lft>'$lft' AND rgt<'$rgt' and status!=2 order by id desc LIMIT $start,$limit";

			$sqlcont="select * from reseller where `username` LIKE '%$cname%' and  lft>'$lft' AND rgt<'$rgt' and status!=2 order by id desc";

		}else {
		$idc = explode(".", $lev);
if($idc[0]==2){
	$member=$this->session->userdata('member_session');
		$uid= $member->id;

		 $parent2 = $this->db->get_where('reseller',array('id' =>$idc[1]))->row()->p_id;
		 $parent3 = $this->db->get_where('reseller',array('id' =>$parent2))->row()->p_id;
		 $parent4 = $this->db->get_where('reseller',array('id' =>$parent3))->row()->p_id;
		 $parent5 = $this->db->get_where('reseller',array('id' =>$parent4))->row()->p_id;

		if($pid==$uid or $parent2==$uid or $parent3==$uid or $parent4==$uid or $parent5==$uid) {
			$sql="select * from reseller where p_id='$idc[1]' and status!=2 order by id desc";

			$sqlcont="select * from reseller where p_id='$idc[1]' and status!=2 order by id desc ";
		}else{redirect('main/resellers/', 'refresh');}
}else{	$sql="select * from reseller where p_id='$sessuid' and status!=2 order by id desc";

			$sqlcont="select * from reseller where p_id='$sessuid' and status!=2 order by id desc ";}
		}



		$querycount = $this->db->query($sqlcont);
		  if($querycount->num_rows() > 0 ) {
		  $total_pages = $querycount->num_rows;
		  }

		$pagination = $this->mdb->pagelink($page,$total_pages,$limit,$link);



		$data['limit'] = $limit;
		$data['cname'] = $cname;
		if($idc[0]==2){
			$sql9="SELECT * from reseller where p_id='$idc[1]' and status!=2 order by id desc";
		}else{	$sql9="SELECT * from reseller where p_id='$sessuid' and status!=2 order by id desc";}
	$query9 = $this->db->query($sql9);

		$query11 = $this->db->query($sql);
		$reselerdata = $query11->result() ;


			$data['reseller_bl'] = $query9->result();
		$data["reseller_list"] = $reselerdata;
		$data["links"] = $pagination;
		$data['page_title'] = 'Reseller';
		$data['page_name'] = 'reseller';
		$this->load->view('mit/index', $data);



	}

	public function resellerEdit($uid){

		$decideid = $this->mdb->passwordChanger('decrypt', $uid);
		$dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
		$sucdate=$dt->format('j F Y g:i A');
		$idate=$dt->format('Y-m-d');
		$var=$this->input->post();
		$varget=$this->input->get();

		$member=$this->session->userdata('member_session');
		$uid= $member->id;



		$username = $this->db->get_where('reseller',array('id' =>$decideid))->row()->username;
		$pid = $this->db->get_where('reseller',array('id' =>$decideid))->row()->p_id;
		$ctype = $this->db->get_where('reseller',array('id' =>$decideid))->row()->custype;

		 $parent2 = $this->db->get_where('reseller',array('id' =>$decideid))->row()->p_id;
		 $parent3 = $this->db->get_where('reseller',array('id' =>$parent2))->row()->p_id;
		 $parent4 = $this->db->get_where('reseller',array('id' =>$parent3))->row()->p_id;
		 $parent5 = $this->db->get_where('reseller',array('id' =>$parent4))->row()->p_id;

		if($pid==$uid or $parent2==$uid or $parent3==$uid or $parent4==$uid or $parent5==$uid) {

		if($ctype=="subadmin"){$level="subadmin";}
		if($ctype=="reseller5"){$level="5";}
		if($ctype=="reseller4"){$level="4";}
		if($ctype=="reseller3"){$level="3";}
		if($ctype=="reseller2"){$level="2";}
		if($ctype=="reseller1"){$level="1";}

		if($_POST) {
			$nid=$var['nid'];
		$name=$var['name'];
		$mobile=$var['mobile'];
		$balance_limit=$var['balance_limit'];
		$active=$var['active'];
		$oversale=$var['oversale'];
		$api=$var['api'];
		$appsaccess=$var['appsaccess'];
		$webaccess=$var['webaccess'];

		$pin = $var['pincode'];

		$typ = 'user';

		$otpchking = $this->mit->otpchk($pin,$typ);

		if($otpchking) {

		$per = 0;

		if (!empty($var['client_type'])):
	    foreach ($var['client_type'] as $per):
	        $add_type_values+= $per;
	    endforeach;
		endif;


		$query="UPDATE `reseller` SET `type` = '$add_type_values',
		`name` = '$name',
		`mobile` = '$mobile',
		`nid` = '$nid',
		`email` = '$email',
		`balance_limit` = '$balance_limit',
		`oversale` = '$oversale',
		`status` = '$active',
		`webAccess` = '$webaccess',
		`appsAccess` = '$appsaccess',
		`api` = '$api' WHERE `reseller`.`id` ='$decideid'";

		$query2 = $this->db->query($query);

		$user=$this->session->userdata('member_session');
		$uids= $user->id;
		$msg= 'Edit Reseller '.$username;
		$this->mit->InsertActivetUser($uids,$msg);

		$this->session->set_flashdata('success', 'Update Successful');

		redirect('main/resellers/', 'refresh');

		}else {

		$this->session->set_flashdata('error', 'Salf PIN Wrong');

		redirect('main/resellers/', 'refresh');
		}

		}



		$query="select * from reseller where id='$decideid'";

		$query2 = $this->db->query($query);
		$data['edit_reseller'] = $query2->result();

		$data['page_title'] = $username.' Edit Reseller';

		$data['page_name'] = 'reseller_edit_user';
		$this->load->view('mit/index', $data);

	}else {

		redirect('main', 'refresh');
	}

	}


	 public function serviceReq() {


		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		$parent= $user->p_id;
		$prebalance = $user->balance;

		if($this->input->post()){




   	 	$var=$this->input->post();
		$number       = $var['number'];
		$amount       = $var['amount'];
		$type         = $var['type'];
		$serviceget   = $var['service'];
		$pcode        = $var['pcode'];
		$cost        = $var['cost'];


		 $sqlmod="SELECT * from module where serviceid='$serviceget' and status=1";
		$queryser = $this->db->query($sqlmod);

					foreach ($queryser->result() as $rowmod)
					{
						$service_auto_limit=$rowmod->auto_limit;
						$requr_pin=$rowmod->requr_pin;
						$requr_nid=$rowmod->nid;
						$requr_name=$rowmod->name;
						$requr_sender_number=$rowmod->sender_number;
						$minamount=$rowmod->min_amount;
						$maxamount=$rowmod->max_amount;

						$minlength=$rowmod->min_length;
						$maxlength=$rowmod->max_length;

						$servicetitle=$rowmod->title;
					}

		$chk_data=$this->mdb->getData('reseller',array('id'=>$uid));
				$chkpass=$chk_data[0]['pincode'];
				$nwpass = $var['pincode'];
				$utrye = 'user';
				$chking= $this->mit->otpchk($nwpass,$utrye);
				if($chking) {


		 $intrvalt = $this->db->get_where('security_option',array('id' =>1))->row()->req_Interval*60;
		 $bkash_auto = $this->db->get_where('security_option',array('id' =>1))->row()->bkash_auto;
		$myacc_tarf = $this->db->get_where('reseller',array('id' =>$uid))->row()->tarif;

		 // parent
		 $parent1 = $this->db->get_where('reseller',array('id' =>$parent))->row()->p_id;
		 $parent2 = $this->db->get_where('reseller',array('id' =>$parent1))->row()->p_id;
		 $parent3 = $this->db->get_where('reseller',array('id' =>$parent2))->row()->p_id;
		 $parent4 = $this->db->get_where('reseller',array('id' =>$parent3))->row()->p_id;
		 $parent5 = $this->db->get_where('reseller',array('id' =>$parent4))->row()->p_id;
		 $parent6 = $this->db->get_where('reseller',array('id' =>$parent5))->row()->p_id;
		 $parent7 = $this->db->get_where('reseller',array('id' =>$parent6))->row()->p_id;
		 // parent

		$mobile=trim($number);
		$firstThreeDigit=substr($mobile,0,3);

		if($firstThreeDigit=="017"){
		$operator="gp";
		}if($firstThreeDigit=="019"){
		$operator="blink";
		}if($firstThreeDigit=="018"){
		$operator="robi";
		}if($firstThreeDigit=="016"){
		$operator="airtel";
		}if($firstThreeDigit=="015"){
		$operator="teletalk";
		}if($firstThreeDigit=="011"){
		$operator="CC";
		}

		 $sql_route = "SELECT * FROM `routing` WHERE active='1' and status='0' and service='$service' and (pcode='$prcode' or pcode='all') order by priority asc";
		 $queryroute = $this->db->query($sql_route);
		 foreach ($queryroute->result() as $rowroute)
					{
						$route_id      =$rowroute->id;
						$route_pcode   =$rowroute->pcode;
						$priority      =$rowroute->priority;
						$route_service =$rowroute->service;
						$routes        =$rowroute->route;

					}


				$sql_api="SELECT * FROM `api_set` WHERE id='$routes' and status=1";

				$queryapifl = $this->db->query($sql_api);
				foreach ($queryapifl->result() as $row_api)
					{

				$api_id=$row_api->id;
				$provider=$row_api->provider;
				$flapi_userid=$row_api->userid;
				$flapi_key=$row_api->api_key;

				$api_url=$row_api->url;
				$apirespons=$row_api->response;
					}


				if($amount<=$bkash_auto) {
				if($routes=="manual") {
				$local="5";
				$statusup="5";
				$apidisable = "5";
				}else {
				$local="0";
				$statusup="0";
				}
				}else {
				$statusup="5";
				$local="5";
				$apidisable = "5";
				}
				if($amount<=$service_auto_limit) {
				$local="0";
				$statusup="0";
				}else {
				$statusup="5";
				$local="5";
				$apidisable = "5";
				}

				$rendomid = uniqid();

				$create_date=date('Y-m-d H:i:s');
				$idate=date('Y-m-d');
				$update_date=date('Y-m-d H:i:s');


				$sql="INSERT INTO `sendflexi` (`id`, `sid`, `route`, `route_id`, `userid`, `admin_id`, `lft`, `rgt`, `ip`, `p_id`, `phone`, `sender_no`, `name`, `nid`, `type`, `balance`, `cost`, `submitted_date`, `idate`, `service`, `remark`, `send_time_stamp`, `parent_cost`, `status`, `operator`, `pcode`, `prebalance`, `local`, `refund`, `result`, `tarif_id`, `level`, `level2`, `level3`, `level4`, `level5`) VALUES (NULL, '$rendomid', '$routes', '$route_id', '$uid', '$admin_id', '$lft', '$rgt', '$ip', '$parent', '$number', '$sender_no', '$name', '$nidcode', '$type', '$amount', '$cost', '$create_date', '$idate', '$serviceget', '$flremark', '$update_date', '$parent_cost', '$statusup', '$operator', '$prcode', '$prebalance', '$local', '', '$blancupdateLink', '$myacc_tarf', '$parent', '$parent1', '$parent2', '$parent3', '$parent4')";

				$this->db->query($sql);

				$idlast = $this->db->insert_id();

				$this->mit->reverse_treeBalanceUpdate($uid,$number,$amount,$serviceget,$rendomid,$pcode,$idlast,"minus");

				$this->session->set_flashdata('success', $servicetitle.' request was received successfully');


				}else {
		$this->session->set_flashdata('error', 'Your PIN Not Valid');
		}

				redirect('main/req/'.$servicetitle, 'refresh');



	 }
	 }

	 /// patment

	 public function payment($para="") {

		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		$parent= $user->p_id;

		$residhex=$this->mdb->passwordChanger('decrypt', $para);
		$query2 = $this->db->query("select * from reseller where id='$residhex'");
		$row = $query2->row();
		$memid = $row->username;

		$data['resid'] = $residhex;

		if($uid!=$residhex) {

		if($this->input->post()){

   	 	$var=$this->input->post();
		$description  = $var['description'];
		$amount       = $var['amount'];
		$type         = $var['type'];

		$this->form_validation->set_rules('amount', 'Amount Not Work', 'numeric|greater_than[0.99]|required|xss_clean');

		 if ($this->form_validation->run() == FALSE)
                {
		$this->session->set_flashdata('error', 'Amount Not Work');
		$data['page_title'] = $memid.' Payment';
		$data['page_name'] = 'payment';
				}else {

		$data['page_title'] = $memid.' Payment';
		$data['page_name'] = 'payment_review';

		}



		}else {


		$data['page_title'] = $memid.' Payment';
		$data['page_name'] = 'payment';
		}
		}else {

			redirect('main', 'refresh');

		}
		$this->load->view('mit/index', $data);

	 }


	  public function paymentAction() {

		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		$sendername = $user->username;
		$uparent = $user->p_id;

		$idate=date('Y-m-d');
		$create_date=date('j F Y g:i A');
		$ip = $_SERVER['REMOTE_ADDR'];


		if($this->input->post()){

   	 	$var=$this->input->post();
		$description  = $var['description'];
		$amount       = $var['amount'];
		$type         = $var['type'];
		$ucid         = $var['ucid'];

		$chk_data=$this->mdb->getData('reseller',array('id'=>$uid));
				$chkpass=$chk_data[0]['pincode'];
				$oversale=$chk_data[0]['oversale'];
				$cbalance=$chk_data[0]['balance'];
	$balancelimit=$chk_data[0]['balance_limit'];


		$availablebal=$cbalance-$balancelimit;



		$nwpass = $var['pincode'];
		$optuser = 'user';
		$chking= $this->mit->otpchk($nwpass,$optuser);
		if($chking) {

		if($availablebal>=$amount && is_numeric($amount)){

		if($type=="Transfer") {
				$myacc_tarf = $this->db->get_where('reseller',array('id' =>$uid))->row()->tarif;
				$sql_rate="SELECT * from rate_module where tarif='$myacc_tarf' and service='262144' and type='1' and status=1";

		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{
						 $mrservice=$raterow->service;
						 $prefix=$raterow->prefix;
						 $rate=$raterow->rate;
						 $comm=$raterow->comm;
						 $charge=$raterow->charge;
						 $prcode=$raterow->pcode;
					}

						 $ratesrate=$amount*$rate;
						 $comm_rate=$amount*$comm/100;
						 $charge_rate=$amount*$charge/100;
						 $final_amount=$ratesrate+$charge_rate-$comm_rate;
				if($queryrate->num_rows() <= 0 ) {
			    $final_amount=$amount;

			}
				if($availablebal>=$final_amount && is_numeric($amount)){

		$prebalance= $this->mit->accountBalance($ucid);
		$t="plus";
			$tm="minus";
		$this->mit->balanceUpdate($uid, $final_amount,$tm);

		$this->mit->balanceUpdate($ucid, $amount,$t);

		$bal=$this->mit->accountBalance($ucid);

			$resellri = $this->db->get_where('reseller',array('id' =>$ucid))->row()->username;
		$getuser = $this->db->get_where('reseller',array('id' =>$ucid))->row()->username;

		 $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `date`, `time`) VALUES (NULL, '$ucid', 'Deposit By $sendername', '$prebalance', '0', '$amount', '$bal', 'plus', '$idate', '$create_date');";

		$this->db->query($sql_tr);
		$balm=$this->mit->accountBalance($uid);
		$sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `date`, `time`) VALUES (NULL, '$uid', 'Transfer to $sendername', '$availablebal', '$amount', '0', '$balm', 'Minus', '$idate', '$create_date');";

		$this->db->query($sql_tr);


		$sql="INSERT INTO `pay_receive` (`id`, `userid`, `desc`, `remark`, `sender`, `credit`, `account`, `type`, `transfer_type`, `idate`, `date`) VALUES (NULL, '$ucid', '$description', 'By $sendername', '$uid', '$amount', '$bal', '$type', 'recharge', '$idate', '$create_date');";

		$this->db->query($sql);


		$actsql ="INSERT INTO `activity_log` (`id`, `userid`, `p_id`, `secret`, `session`, `time`, `msg`, `ip`, `browser`, `os`, `date`) VALUES (NULL, '$uid', '$uparent', '', '0', '$create_date', 'Payment Add by reseller $getuser', '$ip', '', '', '$idate')";

		$this->db->query($actsql);
			$robi=substr($resellri, 0, 2);
if($robi=='88'){
			$getuser = $this->db->get_where('reseller',array('id' =>$ucid))->row()->name;
			$getusers = $this->db->get_where('reseller',array('id' =>$uid))->row()->name;

		$sm="$getusers Dear $getuser Your balance update by $amount new Balance $bal";
		$re=sends("flexiload","SMS*$resellri*$sm*DD");
}
		$this->session->set_flashdata('success', 'Payment added successfuly');

				}else{$this->session->set_flashdata('error', 'Sorry Insufficient balance, Please Recharge First');}
		//redirect('member/reseller/', 'refresh');
		}







		}else {
		$this->session->set_flashdata('error', 'Sorry Insufficient balance, Please Recharge First');
		}

		}else {
		$this->session->set_flashdata('error', 'Your PIN Not Valid');
		}

				redirect('main/', 'refresh');





		}

	  }

	 public function resellerAdd() {

		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		$sendername = $user->username;
		$uparent = $user->p_id;
		$ctype = $user->custype;
	$ressoff = $user->reseller;
$chk_data=$this->mdb->getData('reseller',array('id'=>$uid));
				$cbalance=$chk_data[0]['balance'];
	$balancelimit=$chk_data[0]['balance_limit'];


		$availablebal=$cbalance-$balancelimit;
        	if($ctype=="subadmin"){$custype="reseller5";}
		if($ctype=="reseller5"){$custype="reseller4";}
		if($ctype=="reseller4"){$custype="reseller3";}
		if($ctype=="reseller3"){$custype="reseller2";}
		if($ctype=="reseller2"){$custype="reseller1";}

        $sql_rate="SELECT * from level_list where name='$custype'";
		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{
				$amount=$raterow->account;

}



		$myLeft = $user->lft;
		$myRight = $user->rgt;

		$idate=date('Y-m-d');
		$create_date=date('j F Y g:i A');
		$ip = $_SERVER['REMOTE_ADDR'];

		 // parent

		 $parent2 = $this->db->get_where('reseller',array('id' =>$uid))->row()->p_id;
		 $parent3 = $this->db->get_where('reseller',array('id' =>$parent2))->row()->p_id;
		 $parent4 = $this->db->get_where('reseller',array('id' =>$parent3))->row()->p_id;
		 $parent5 = $this->db->get_where('reseller',array('id' =>$parent4))->row()->p_id;

		 // parent





		if($this->input->post()){

	$this->form_validation->set_rules('username', 'Username', 'is_unique[reseller.username]|trim|required|xss_clean|min_length[5]');
		$this->form_validation->set_rules('password', 'Password', 'trim|required|xss_clean|min_length[6]');
	$this->form_validation->set_rules('pin', 'PIN', 'trim|required|xss_clean|min_length[4]');

	$this->form_validation->set_rules('pincode', 'Salf PIN', 'trim|required|xss_clean|min_length[4]');

	$this->form_validation->set_rules('client_types[]', 'Reseller Permission', 'trim|required');

		if ($this->form_validation->run() == FALSE) {

		//$data=$this->data['form_validation'] = validation_errors();
		//$this->session->set_flashdata('error', 'Please Right Entry.');

		$data['page_name'] = 'add_reseller';
		//$this->load->view('mit/index', $data);


		}else {


		if($ressoff=='1'){

		    	if($availablebal>=$amount && is_numeric($amount)){

		$var=$this->input->post();

		$username  = $var['username'];
		$password  = $var['password'];
		$pin  	   = $var['pin'];
		$mobile    = $var['mobile'];
		$email  	= $var['email'];
		$client_types  = $var['client_types'];
		$weballow       = $var['webaccess'];
		$appsallow       = $var['appsaccess'];

		$nwpass = $var['pincode'];
		$chtype = 'user';
		$chking= $this->mit->otpchk($nwpass,$chtype);
		if($chking) {

		if($ctype=="subadmin"){$custype="reseller5";}
		if($ctype=="reseller5"){$custype="reseller4";}
		if($ctype=="reseller4"){$custype="reseller3";}
		if($ctype=="reseller3"){$custype="reseller2";}
		if($ctype=="reseller2"){$custype="reseller1";}


		$client_types = 0;

		if (!empty($var['client_types'])):
    foreach ($var['client_types'] as $client_types):
        $add_type_values+= $client_types;
    endforeach;
endif;

		$userpl=strlen($username);
		$pass=strlen($password);

		$q2="UPDATE reseller SET rgt = rgt + 2 WHERE rgt > $myLeft";
		$this->db->query($q2);

		$q3="UPDATE reseller SET lft = lft + 2 WHERE lft > $myLeft";
		$this->db->query($q3);


		$left=$myLeft +1;
		$right=$myLeft + 2;
		$hash = $this->mdb->generate($password);
		$hashpin = $this->mdb->generate($pin);

		$rlastids = $this->mit->lastUserID();
		 $rlastid = $rlastids+1;

		$sqltarif="INSERT INTO `tarif` (`id`, `make_by`, `name`, `desc`, `userid`, `duble`, `date`, `time`) VALUES (NULL, '$uid', '$username Tarif', '$add_type_values', '$rlastid', '$duble', '$idate', '$create_date');";

		$this->db->query($sqltarif);

		$tarifidlast = $this->db->insert_id();
		$otp = 0;

		$sql12="INSERT INTO `reseller` (`id`, `username`, `password`, `pincode`, `type`, `name`, `mobile`, `email`, `note`, `custype`, `p_id`, `admin_id`, `lft`, `rgt`, `balance`, `balance_limit`, `create_date`, `ip`, `enbale_otp`, `tarif`, `oversale`, `status`, `level5`, `level4`, `level3`, `level2`, `level1`, `webAccess`, `appsAccess`,`new_account_price`) VALUES (NULL, '$username', '$hash', '$hashpin', '$add_type_values', '$name', '$mobile', '$email', '$note', '$custype', '$uid', '$admin_id', '$left','$right', '0', '0', '$create_date', '$ip', '$otp', '$tarifidlast', '0', '1', '$uid', '$parent2', '$parent3', '$parent4', '$parent5', '$weballow', '$appsallow','$amount');";

		$this->db->query($sql12);

		$idlast = $this->db->insert_id();

		$user=$this->session->userdata('member_session');
		$uids= $user->id;
		$msg= 'Add Reseller '.$username;
		$this->mit->InsertActivetUser($uids,$msg);


		if (!empty($var['client_types'])):
		foreach ($var['client_types'] as $per):

		$sql33="SELECT * from rate_module where userid='$uid' and service='$per'";
		$query33=$this->db->query($sql33);

		foreach ($query33->result() as $rowrat)
					{

		$opname=$rowrat->service;

		$country=$rowrat->country;

		$operator=$rowrat->oparetor;
		$phonecode=$rowrat->c_code;
		$opcode=$rowrat->opcode;

		$priceid=$rowrat->id;

		$prefix=$rowrat->prefix;
		$rate=$rowrat->rate;

		$pcode=$rowrat->pcode;
		 if($opname=='262144'){
		     $comm='0';
		 }else{
		     $comm=$rowrat->comm;
		}

		$charge=$rowrat->charge;
		$type=$rowrat->type;

		$sql_rate="INSERT INTO `rate_module` (`id`, `country`, `c_code`, `oparetor`, `opcode`, `tarif`, `userid`, `create_by`, `service`, `prefix`, `pcode`, `type`, `rate`, `comm`, `charge`, `price_id`, `status`, `date`) VALUES (NULL, '$country', '$phonecode', '$operator', '$opcode', '$tarifidlast', '$idlast', '$uid', '$opname', '$prefix', '$pcode', '$type', '$rate', '$comm', '$charge', '$priceid', '1', '$create_date')";
		$this->db->query($sql_rate);

					}
		endforeach;
		endif;
			$prebalance= $this->mit->accountBalance($uid);

	$tm="minus";
		$this->mit->balanceUpdate($uid, $amount,$tm);
		$bal= $this->mit->accountBalance($uid);

		  $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `date`, `time`) VALUES (NULL, '$uid', 'new account for $username', '$prebalance', '$amount', '', '$bal', 'minus', '$idate', '$create_date')";
		  $this->db->query($sql_tr);

		$sql="INSERT INTO `pay_receive` (`id`, `userid`, `desc`, `remark`, `sender`, `debit`, `account`, `type`, `transfer_type`, `idate`, `date`) VALUES (NULL, '$idlast', 'new account for $username', 'By $username', '$uid', '$amount', '$bal', 'new_account', 'recharge', '$idate', '$create_date')
		";
		$this->db->query($sql);

	redirect('main/rate/'.$this->mdb->passwordChanger('encrypt', $idlast), 'refresh');

		//redirect('main/resellers/', 'refresh');

		}else {

		$this->session->set_flashdata('error', 'Your PIN Is Not Valid');

		redirect('main/resellers/', 'refresh');

		}

}else{$this->session->set_flashdata('error', 'Need Balance '.$amount.' for new account');}

		    	}else{	$this->session->set_flashdata('error', 'Your have no permission');
			}

		}

		}else {
		$data['page_title'] = ' Add Reseller';
		$data['page_name'] = 'add_reseller';
		}
		$this->load->view('mit/index', $data);

	  }

	   public function payments($users="") {

		$member = $this->session->userdata('member_session');

		$uid= $member->id;

		$idate = date('Y-m-d');

		$data['page_title'] = 'Payment history';

		$serviceid = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

		$residhex=$this->mdb->passwordChanger('decrypt', $users);

		if(!empty($users)) {

		$sql="select * from pay_receive where userid='$residhex' and (sender='$uid' or p_id='$uid') ORDER BY id desc";

		}else {

		$sql="select * from pay_receive where (sender='$uid' or p_id='$uid') ORDER BY id desc";
		}

		$query2 = $this->db->query($sql);

		$data['payment_list'] = $query2->result();

		$data['page_name'] = 'payment_history';
		$this->load->view('mit/index', $data);
	 }

	  public function received() {

		$member = $this->session->userdata('member_session');

		$uid= $member->id;

		$idate = date('Y-m-d');

		$data['page_title'] = 'Received history';

		$serviceid = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

		$sql="select * from pay_receive where userid='$uid' ORDER BY id desc";

		$query2 = $this->db->query($sql);

		$data['receive_history'] = $query2->result();

		$data['page_name'] = 'receive_history';
		$this->load->view('mit/index', $data);
	 }

	   public function balance() {

		$member = $this->session->userdata('member_session');

		$uid= $member->id;

		$idate = date('Y-m-d');

		$data['page_title'] = 'Balance  Reports';

		$serviceid = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

		$sql="Select sum(credit) as tsuccess from pay_receive where sender='$uid' and type='Transfer'";

		$sql_treturn="Select sum(credit) as tsuccess from pay_receive where userid='$uid' and type='Transfer'";

		$query2 = $this->db->query($sql);
		foreach($query2->result() as $recv) {
			$traddbal=$recv->tsuccess;
		}

		$query3 = $this->db->query($sql_treturn);
		foreach($query3->result() as $treun) {


			$btrn=$treun->tsuccess;
		}

		$sql_treturn="Select sum(credit) as tsuccess from pay_receive where userid='$uid' and type='Transfer'";


		$data['btrn'] = $btrn;
		$data['traddbal'] = $traddbal;

		$data['page_name'] = 'balance_report';
		$this->load->view('mit/index', $data);
	 }


	  public function reports() {

		$member = $this->session->userdata('member_session');

		$uid= $member->id;

		$var=$this->input->post();
		$varget=$this->input->get();

		$idate=date('Y-m-d');

		if($_POST){
		$resel=$var["resel"];

		$from1=$var["from1"];
		$to1=$var["to1"];
		}



	  	$data['to1'] = $to1;
		$data['from1'] = $from1;
		$data['resel'] = $resel;

		$idate = date('Y-m-d');

		$data['page_title'] = 'Reports';

		$serviceid = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

		$data['page_name'] = 'reports';
		$this->load->view('mit/index', $data);
	 }

	  public function costprofit() {

		$member = $this->session->userdata('member_session');

		$uid= $member->id;

		$var=$this->input->post();
		$varget=$this->input->get();

		if($_POST){


		$from1=$var["from1"];
		$to1=$var["to1"];
		}

	  	$data['to1'] = $to1;
		$data['from1'] = $from1;


		$idate = date('Y-m-d');

		$data['page_title'] = 'Cost & Profit';

		$serviceid = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

		$data['page_name'] = 'profit';
		$this->load->view('mit/index', $data);
	 }

	  public function Myprofile() {

		 $member = $this->session->userdata('member_session');

		$uid= $member->id;

		if($this->input->post()){
		$var=$this->input->post();

		$name = $var['name'];
		$mobile = $var['mobile'];
		$email = $var['email'];
			$utrye = 'user';
		$chking= $this->mit->otpchk($pincode,$utrye);
		if($chking){

		$query="UPDATE `reseller` SET
		`name` =  '$name',
		`mobile` =  '$mobile',
		`email` =  '$email' WHERE `id` ='$uid'";

			$this->db->query($query);
}else{
    $this->session->set_flashdata('error', 'OTP/PIN Code Wrong');

}
		redirect('main/Myprofile', 'refresh');

		}else {

		$data['page_title'] = 'My Profile';
		$data['page_name'] = 'profile';
		}
		$this->load->view('mit/index', $data);

	  }

 public function m2otp() {

	  	$member = $this->session->userdata('member_session');

		$uid= $member->id;
	$member=$this->mdb->getData('reseller',array('id'=>$uid));
			$uname= $member[0]['username'];
	$uemail= $member[0]['email'];
		$umobile= $member[0]['mobile'];


	  	if($this->input->post()){
		$var=$this->input->post();
		$code = $var['code'];

			$utrye = 'user';
		$chking= $this->mit->otpchk($pincode,$utrye);
			  if (!empty($var['codes'])) {



			      $otp = mt_rand(100000, 999999);

		$chking= $this->mit->otpchk($var['pincode'],$utrye);
			      	if(!$chking)$err='Invaild pin';
			      	if(empty($err)){

			      	    if($var['type']=='off'){
			            $odata=array(
			                	'otp_choice' =>'0',
			                	'enbale_otp' =>0,
			  );

			  	$this->db->where('id',$uid);
		$this->db->update('reseller',$odata);

			      	$this->session->set_flashdata('success', "OTP Stop");
		      	redirect('main/m2otp', 'refresh');

			        }else{

			      if($var['type']=='email')$to=$uemail;
			       if($var['type']=='mobile')$to=$umobile;
			      $subject="$system_name OTP CODE $otp";
			      $msg="$system_name Dear $uname, Your OTP code is $otp from ip $_SERVER[REMOTE_ADDR]";


			  $code=$this->mit->mail_send($to, $subject, $msg);
			  if (!empty($code)) {

			      $_SESSION['type']=$var['type'];
			      	$hash = $this->mdb->generate($otp);
			       $odata=array(
			  'otp' =>$hash,
			  );



			  	$this->db->where('id',$uid);
		$this->db->update('reseller',$odata);
			       $cd=array(
			  'code' =>1,
			  );
			      	$data["ucode"] =json_encode($cd, true);


			      	$this->session->set_flashdata('success', "We sent a OTP code to $to $var[type]");



			  }
			      	}

			  }else{
			      	$this->session->set_flashdata('error', 'OTP/PIN Code Wrong');

			  }
			  }




			  if (!empty($var['code'])) {
			      $otp = mt_rand(100000, 999999);
			      if(!$this->mit->twootp($var['code'],$utrye))$err='Invaild OTP';
			      	if(!$this->mit->otpchk($var['pincode'],$utrye))$err='Invaild pin';
			      	if(empty($err)){

			      	    if($_SESSION['type']=='email')$type=3;
			       if($_SESSION['type']=='mobile')$type=4;

			 $odata=array(
			  'enbale_otp' =>'1',
			  	'otp_choice' =>$type
			  );


			  	$this->db->where('id',$uid);
		$this->db->update('reseller',$odata);

			     	 unset($_SESSION['type']);

			 	$this->session->set_flashdata('success', 'OTP Update Successfully');
		redirect('main/m2otp', 'refresh');


			      	}else{

			      	    	$this->session->set_flashdata('error', 'OTP Code Wrong');
			      	}


			  }





		}

			$data['page_title'] = '2 step Otp';

				$data['page_name'] = 'm2otop';


	$data["userin"] =json_encode($member, true);

		$this->load->view('mit/index', $data);


	  }


	  public function set2step() {

	  	$member = $this->session->userdata('member_session');

		$uid= $member->id;

		$user_info=$this->mdb->getData('reseller',array('id'=>$uid));
							$secret = $user_info[0]['gotp_key'];

							$enbale_otp = $user_info[0]['enbale_otp'];


	  	if($this->input->post()){
		$var=$this->input->post();
		$code = $var['code'];

		if($enbale_otp==0) {
			$activeotp='1';
		}else {
			$activeotp='0';
		}
		$otpup = array(
						'enbale_otp' =>$activeotp ,
						'otp_choice' =>'2'

					 );

		$checkResult = $this->googleauthenticator->verifyCode($secret, $code, 2);
		if ($checkResult) {
		 $this->mdb->update('device_list',array('remember'=>0),array('userid'=>$uid));
		$this->mdb->update('reseller',$otpup,array('id'=>$uid));
		$this->session->set_flashdata('success', 'OTP Update Successfully');
		redirect('main/set2step', 'refresh');
		}else {

		$this->session->set_flashdata('error', 'OTP Code Wrong');
		redirect('main/set2step', 'refresh');

		}
		}else {

			$data['page_title'] = 'Google Authenticator Otp';

				$data['page_name'] = 'set2step';
		}

		$this->load->view('mit/index', $data);


	  }

	  public function api($para1="", $para2=""){

	  	$member = $this->session->userdata('member_session');

		$uid= $member->id;
		$getUsername= $member->username;

		$create_date = date('Y-m-d H:i:s');

		if($para1=="reset") {

				$alphanum  = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

            $code1 = substr(str_shuffle($alphanum), 0, 50);

            $code2 = substr(str_shuffle($alphanum), 0, 50);

            $code3 = substr(str_shuffle($alphanum), 0, 50);

            $code4 = substr(str_shuffle($alphanum), 0, 50);

            $code5 = substr(str_shuffle($alphanum), 0, 50);

            $code6 = substr(str_shuffle($alphanum), 0, 50);

            $code7 = substr(str_shuffle($alphanum), 0, 50);



            $code_01 = $code1.$code2.$code3.$code4.$code5.$code6.$code7;

            $gen_code1 = substr(str_shuffle($code_01), 0, 50);

            $code_01 = $code1.$code2.$code3.$code4.$code5.$code6.$code7;

            $gen_code2 = substr(str_shuffle($code_02), 0, 50);



            $gen_code = $gen_code1.$gen_code2;

            $pin_gen_code = substr(str_shuffle($gen_code), 0, 60);

		$updatekey = "UPDATE  `api_key_user` SET  `apikey` =  '$pin_gen_code', `reset_update` =  '$create_date' WHERE  `api_key_user`.`apikey` ='$para2'";
 		$query = $this->db->query($updatekey);

		$updatekey = "UPDATE  `api_key_ip` SET  `api_key` =  '$pin_gen_code' WHERE  `api_key_ip`.`api_key` ='$para2'";
		 $query = $this->db->query($updatekey);

		$this->session->set_flashdata('success', ' API Key Reset Successfully');
				redirect('main/api', 'refresh');


		}

	  	if($_POST){


		$var=$this->input->post();
		$code = $var['pincode'];
		$utypes = 'user';
		$chking = $this->mit->otpchk($code,$utypes);

		if($chking) {
	  		$alphanum  = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

            $code1 = substr(str_shuffle($alphanum), 0, 50);

            $code2 = substr(str_shuffle($alphanum), 0, 50);

            $code3 = substr(str_shuffle($alphanum), 0, 50);

            $code4 = substr(str_shuffle($alphanum), 0, 50);

            $code5 = substr(str_shuffle($alphanum), 0, 50);

            $code6 = substr(str_shuffle($alphanum), 0, 50);

            $code7 = substr(str_shuffle($alphanum), 0, 50);



            $code_01 = $code1.$code2.$code3.$code4.$code5.$code6.$code7;

            $gen_code1 = substr(str_shuffle($code_01), 0, 50);

            $code_01 = $code1.$code2.$code3.$code4.$code5.$code6.$code7;

            $gen_code2 = substr(str_shuffle($code_02), 0, 50);



            $gen_code = $gen_code1.$gen_code2;

            $pin_gen_code = substr(str_shuffle($gen_code), 0, 60);

            $sql="INSERT INTO `api_key_user` (`id`, `userid`, `username`, `apikey`, `date`) VALUES (NULL, '$uid', '$getUsername', '$pin_gen_code', '$create_date')";

             $query = $this->db->query($sql);
			  redirect('main/api', 'refresh');
			}else {
				$this->session->set_flashdata('error', ' Code Wrong');
				redirect('main/api', 'refresh');
			}



	  	}else {
	  	$data['page_title'] = 'Api Setting';
		$data['page_name'] = 'apikey';
		$this->load->view('mit/index', $data);
	  	}




	  }

	  public function ipweb($para1="", $para2="")
	  {
	  	$member = $this->session->userdata('member_session');

		$uid= $member->id;
		$getUsername= $member->username;

		$create_date = date('Y-m-d H:i:s');

	  	if($_POST){

		$var=$this->input->post();
		$api_key = $var['api_key'];
		$webaddress = $var['ipadress'];
		$etross = $var['etross'];
		if(!empty($webaddress)) {



	if($etross==1) {

	$ipadd = array('userid'=>$uid,
					'username'=>$getUsername,
					'api_key'=>$api_key,
					'ip'=>$webaddress,
					'web'=>$webaddress,
					'date'=>$create_date
					);

	}else {

		$ipadress = gethostbyname($webaddress);

		$ipadd = array('userid'=>$uid,
					'username'=>$getUsername,
					'api_key'=>$api_key,
					'ip'=>$ipadress,
					'web'=>$webaddress,
					'date'=>$create_date
					);

	}

					$this->db->insert('api_key_ip',$ipadd);



 	 $this->session->set_flashdata('success', 'Successfully Insert');

				redirect('main/api', 'refresh');

			}else {
				redirect('main/api', 'refresh');
			}

		}
	}

	public function ipdel($para1) {



			$var=$this->input->get();
			$id = $var['id'];

			$ipdel = "DELETE FROM `api_key_ip` WHERE id='$para1'";
			$query = $this->db->query($ipdel);

			$this->session->set_flashdata('success', 'Successfully Delete');

			 redirect('main/api', 'refresh');


	}

	   public function myrate() {

		$member = $this->session->userdata('member_session');

		$uid= $member->id;

		$idate = date('Y-m-d');

		$data['page_title'] = 'My Rates';

		//$serviceid = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

		$data['page_name'] = 'myrates';
		$this->load->view('mit/index', $data);
	 }

	   public function accesslogs() {

		$member = $this->session->userdata('member_session');

		$uid= $member->id;

		$idate = date('Y-m-d');

		$data['page_title'] = 'My Accesslogs';

		//$serviceid = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

		$data['page_name'] = 'accesslogs';
		$this->load->view('mit/index', $data);
	 }

	  public function resdevlog($para="") {

		$member = $this->session->userdata('member_session');
		$uid= $member->id;

		$get=$this->input->get();
		$var=$this->input->post();

		if($_POST) {

		$resel = $var['resel'];


		}else {

		$resel = $get['resel'];


		}

		if(!empty($resel)) {

		$sql="select * from device_list where userid='$resel' order by id desc";

		}else {
		$sql="select * from device_list where userid='$uid' or p_id='$uid' order by id desc";

		}

		$data['resel'] = $resel;

		$query11 = $this->db->query($sql);
		$reselerdev = $query11->result() ;

		$data["devicelist"] = $reselerdev;

		$data['page_title'] = 'Device Logs ';
		$data['page_name'] = 'reseller_device_log';

		$this->load->view('mit/index', $data);


	  }

	  public function device_action($para1="", $para2="", $para3="") {

		$member = $this->session->userdata('member_session');
		$uid= $member->id;

		$devid=$this->mdb->passwordChanger('decrypt', $para2);

		if($para1=='allow') {$stsac=1;}
		if($para1=='disable') {$stsac=0;}

		$query="UPDATE  `device_list` SET  `status` = '$stsac' where id='$devid'";

		$this->db->query($query);

		if($para1=='delete') {

		$this->db->where('id', $devid);
		$this->db->delete('device_list');

		}

		$userid = $this->db->get_where('device_list',array('id' =>$devid))->row()->userid;
		$username = $this->db->get_where('reseller',array('id' =>$userid))->row()->username;


		$msg= ' Device Action '. $para1. ' User : '.$username;
		$this->mit->InsertActivetUser($uid,$msg);

		redirect('main/resdevlog', 'refresh');
	}


	  public function viewtickets() {

		$member = $this->session->userdata('member_session');

		$uid= $member->id;

		$idate = date('Y-m-d');

		$data['page_title'] = 'Complain';

		//$serviceid = $this->db->get_where('module',array('title' =>$service))->row()->serviceid;

		$data['page_name'] = 'tickets';
		$this->load->view('mit/index', $data);
	 }

	  public function openticket() {

		  $member = $this->session->userdata('member_session');

		$uid= $member->id;

		if($this->input->post()){

		 $var=$this->input->post();

		 $subject= $var['subject'];
		 $details= $var['details'];

		$idate=date('Y-m-d');
		$create_date=date('j F Y g:i A');
		$ip = $_SERVER['REMOTE_ADDR'];


$sql="INSERT INTO `complain` (`id`, `userid`, `subject`, `msg`, `status`, `ip`, `reply_msg`, `date`) VALUES (NULL, '$uid', '$subject', '$details', '0', '$ip', '', '$create_date')";



 $query3 = $this->db->query($sql);

 $this->session->set_flashdata('success', 'Successful Add tickets');


		redirect('main/viewtickets', 'refresh');

		}else {


		$data['page_title'] = 'New Complain';

		$data['page_name'] = 'tickets_add';
		}
		$this->load->view('mit/index', $data);

	  }





	public function pin()
	{

		if($this->input->post()){
		 $get=$this->input->post();
	 	$user=$this->session->userdata('member_session');
        $chk_data=$this->mdb->getData('reseller',array('id'=>$user->id));
		$chkpass=$chk_data[0]['pincode'];
		$nwpass = $get['oldpass'];
		$chk= $this->mdb->verify($nwpass,$chkpass);
		if($chk==true) {
		//$this->session->set_flashdata('passerror', 'Your Password Worng');

		if($get['newpass']==$get['rnewpass']){
		$this->mdb->update('reseller',array('pincode'=>$this->mdb->generate($get['newpass'])),array('id'=>$user->id));

		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		$msg= 'Salf PIN Change';
		$this->mit->InsertActivetUser($uid,$msg);


		$this->session->set_flashdata('success', 'Your PIN Change Update');
		}else {
		$this->session->set_flashdata('error', 'Your Confirm PIN Worng');
		}
		}else {
		$this->session->set_flashdata('error', 'Your Old PIN Worng');
		}

		redirect('main/pin', 'refresh');
		}else {
		$data['page_title'] = 'My PIN';
        $data['page_name'] = 'pin';
		$this->load->view('mit/index', $data);
		}



	}



	 public function password()
	{

		if($this->input->post()){
		 $get=$this->input->post();
	 	$user=$this->session->userdata('member_session');
        $chk_data=$this->mdb->getData('reseller',array('id'=>$user->id));
		$chkpass=$chk_data[0]['password'];
		$nwpass = $get['oldpass'];
		$chk= $this->mdb->verify($nwpass,$chkpass);
		if($chk==true) {
		//$this->session->set_flashdata('passerror', 'Your Password Worng');

		if($get['newpass']==$get['rnewpass']){
		$this->mdb->update('reseller',array('password'=>$this->mdb->generate($get['newpass'])),array('id'=>$user->id));

		$user=$this->session->userdata('member_session');
		$uid= $user->id;
		$msg= 'Salf Password Change';
		$this->mit->InsertActivetUser($uid,$msg);

		$this->session->set_flashdata('success', 'Your Password Change Update');
		}else {
		$this->session->set_flashdata('error', 'Your Confirm Password Worng');
		}
		}else {
		$this->session->set_flashdata('error', 'Your Old Password Worng');
		}

		redirect('main/password', 'refresh');
		}else {
		$data['page_title'] = 'My Password';
        $data['page_name'] = 'password';
		$this->load->view('mit/index', $data);
		}



	}


	public function changeSecuReseller($memid = NULL)
	{ exit;

	$user= $this->session->userdata('member_session');
	$uid = $user->id;

	$decideid = $this->mdb->passwordChanger('decrypt', $memid);

	if($this->input->post()){

	$username = $this->db->get_where('reseller',array('id' =>$decideid))->row()->username;
	$pid = $this->db->get_where('reseller',array('id' =>$decideid))->row()->p_id;
	$ctype = $this->db->get_where('reseller',array('id' =>$decideid))->row()->custype;

	$parent2 = $this->db->get_where('reseller',array('id' =>$decideid))->row()->p_id;
	$parent3 = $this->db->get_where('reseller',array('id' =>$parent2))->row()->p_id;
	$parent4 = $this->db->get_where('reseller',array('id' =>$parent3))->row()->p_id;
	$parent5 = $this->db->get_where('reseller',array('id' =>$parent4))->row()->p_id;

	if($pid==$uid or $parent2==$uid or $parent3==$uid or $parent4==$uid or $parent5==$uid) {

	$var=$this->input->post();
	$pin = $var['pincode'];
	$typ = 'user';

	$otpchking = $this->mit->otpchk($pin,$typ);

	if($otpchking) {
	$chnagetype = $var['chnagetype'];
	if($chnagetype=="password") {
	$change = array(
	"password" => $this->mdb->generate($var['change'])
	);
	}else {
	$change = array(
	"pincode" => $this->mdb->generate($var['change'])
	);
	}
    $this->mdb->update('reseller',$change,array('id'=>$decideid));
	print("<script>window.alert('Successfully change Sucure Update');</script>");
		print("<script>
	   	window.opener.location.reload();
	    window.close();
		</script>");

	}else {

		print("<script>window.alert('Admin PIN Wrong');</script>");
		print("<script>
	   	window.opener.location.reload();
	    window.close();
		</script>");

	}



	}else {

		redirect('main/resellers', 'refresh');
	}
	}else {
	$data['memedit']=$this->mdb->getData('reseller',array('id'=>$decideid));

	$this->load->view('admin/status/change_pass', $data);
	}




	}






	 public function logout() {


		$user= $this->session->userdata('member_session');
		$user = $user->id;




		$lastid = $this->db->order_by('id', 'desc');
		$lastid = $this->db->limit(1);
		$lastid =  $this->db->get_where('userlog',array('ucid' => $user, 'ctype' => 'user' ))->row()->userlog_id;

		$change = array(
						"logout_time" => $this->date_time()
					);

		$this->mdb->update('userlog',$change,array('id'=>$lastid));


        $this->session->unset_userdata('secretcode');
		$this->session->unset_userdata('member_login');
		$this->session->unset_userdata('member_session');
		$this->session->unset_userdata('user_requested_page');
       redirect('dashboard/login', 'refresh');
    }

}
