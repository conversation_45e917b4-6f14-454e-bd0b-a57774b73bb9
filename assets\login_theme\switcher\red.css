.spinner,
.tp-loader.spinner0,
#fancybox-loading div {
    border-left: 3px solid rgba(232,85,78,.15);
    border-right: 3px solid rgba(232,85,78,.15);
    border-bottom: 3px solid rgba(232,85,78,.15);
    border-top: 3px solid rgba(232,85,78,.8);
}
a {
    color: #e8554e;
}
.nocolor:hover {
    color: #e8554e
}
.post-title a:hover {
    color: #e8554e
}
.main-title.text-center:after {
    color: #e8554e;
}
ul.circled li:before {
    color: #e8554e;
}
.meta a:hover,
.more:hover {
    color: #e8554e
}
footer a:hover {
    color: #e8554e !important
}
.btn,
.vanilla-form label.custom-select span {
    background: #e8554e;
}
.btn:hover,
.btn:focus,
.btn:active,
.btn.active,
.pagination ul > li > a:hover,
.pagination ul > li > a:focus,
.pagination ul > .active > a,
.pagination ul > .active > span {
    background: #cf4c45;
}
.navbar .navbar-nav > li > a:focus,
.navbar .nav > li > a:hover,
.navbar .nav > li.current > a {
    color: #e8554e !important;
}
.navbar .dropdown-menu li a:hover,
.navbar .dropdown-menu li a.active {
    color: #e8554e !important;
}
.navbar .nav .open > a,
.navbar .nav .open > a:hover,
.navbar .nav .open > a:focus {
    color: #e8554e;
}
.navbar .dropdown-menu > li > a:hover,
.navbar .dropdown-menu > li > a:focus,
.navbar .dropdown-submenu:hover > a,
.navbar .dropdown-submenu:focus > a,
.navbar .dropdown-menu > .active > a,
.navbar .dropdown-menu > .active > a:hover,
.navbar .dropdown-menu > .active > a:focus {
    color: #e8554e
}
.navbar .top-bar a:hover {
	color: #e8554e;
}
.yamm .yamm-content a:hover {
    color: #e8554e
}
.steps .icon {
    color: #e8554e;
}
.steps .steps-item .number {
    background: #e8554e;
}
.steps .steps-item:hover {
    border-color: #e8554e
}
.feature .icon {
    color: #e8554e;
}
.icon-large {
    color: #e8554e;
}
.owl-item .item:hover .box {
    box-shadow: 0 2px 0 #e8554e
}
.post-types .date-wrapper {
    background: #e8554e;
}
.post-types .post .format-wrapper {
    background: #e8554e;
}
.sidebox a:hover {
    color: #e8554e
}
.widget .post-list h5 a:hover {
    color: #e8554e
}
.widget .post-list .meta em a:hover {
    color: #e8554e
}
footer ul.tag-list li a:hover {
    background: #e8554e !important
}
.testimonials2 .quote:hover {
    border: 2px solid #e8554e
}
.isotope-filter ul li a:hover,
.isotope-filter ul li a.is-checked {
    color: #e8554e
}
.isotope-filter ul li:after {
    color: #e8554e;
}
.price {
    color: #e8554e;
}
.progress-list li em {
    color: #e8554e;
}
.progress.plain .bar {
    background: #e8554e;
}
.bordered .progress.plain {
    border: 1px solid #e8554e;
}
.bordered .progress.plain .bar {
    background: #e8554e
}
.tabs-top.bordered .tab a {
    color: #e8554e;
    border: 2px solid #e8554e;
}
.tabs-top .tab a:hover,
.tabs-top .tab.active a {
    color: #e8554e;
    box-shadow: 0 2px 0 #e8554e;
}
.tabs-top.bordered .tab a:hover,
.tabs-top.bordered .tab.active a {
    border: 2px solid #e8554e;
    background: #e8554e;
}
.tabs-bottom .tab:hover .icon.box,
.tabs-bottom .tab.active .icon.box {
    box-shadow: 0 2px 0 #e8554e
}
.panel-group .panel-active a,
.panel-group .panel-title > a:hover {
    color: #e8554e
}
.panel-group .panel-heading .panel-title:hover,
.panel-group .panel-active .panel-heading .panel-title {
    box-shadow: 0 2px 0 #e8554e;
    color: #e8554e;
}
.bordered .panel-heading .panel-title {
    color: #e8554e;
    border: 2px solid #e8554e;
}
.bordered .panel-heading .panel-title:hover {
    background: #e8554e;
    border: 2px solid #e8554e;
}
.bordered .panel-title > a {
    color: #e8554e
}
.bordered .panel-title:hover,
.bordered .panel-active .panel-heading .panel-title,
.bordered .panel-active .panel-heading .panel-title:hover {
    border: 2px solid #e8554e;
    background: #e8554e;
}
.tooltip-inner {
    background-color: #e8554e;
}
.tooltip.top .tooltip-arrow,
.tooltip.top-left .tooltip-arrow,
.tooltip.top-right .tooltip-arrow {
    border-top-color: #e8554e
}
.tooltip.right .tooltip-arrow {
    border-right-color: #e8554e
}
.tooltip.left .tooltip-arrow {
    border-left-color: #e8554e
}
.tooltip.bottom .tooltip-arrow,
.tooltip.bottom-left .tooltip-arrow,
.tooltip.bottom-right .tooltip-arrow {
    border-bottom-color: #e8554e
}
.vanilla-form input[type="radio"]:focus + span,
.vanilla-form input[type="checkbox"]:focus + span,
.vanilla-form input[type="radio"]:active + span,
.vanilla-form input[type="checkbox"]:active + span {
    border-color: #e8554e
}
.vanilla-form input[type="radio"] + span::after {
    background-color: #e8554e;
    border-color: #e8554e;
}
.vanilla-form input[type="radio"]:checked + span,
.vanilla-form input[type="checkbox"]:checked + span {
    border: 2px solid #e8554e
}
.vanilla-form input[type="checkbox"] + span::after {
    border: 0 solid #e8554e;
}
#comments .info h2 a:hover {
    color: #e8554e
}
#comments a.reply-link:hover {
    color: #e8554e
}
#contact-info-button:hover,
#btn-close-modal:hover {
    color: #e8554e
}
@media (max-width: 991px) { 
	.navbar .navbar-nav .open .dropdown-menu > li > a:hover,
	.navbar .navbar-nav .open .dropdown-menu > li > a:focus {
	    color: #e8554e !important
	}
}
@media (max-width: 767px) { 
	.isotope-filter ul li a:hover,
	.isotope-filter ul li a.is-checked {
	    box-shadow: 0 2px 0 #e8554e !important
	}
}