<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Firebase Cloud Messaging (FCM) V1 API Integration
 *
 * This class provides methods to send notifications using Firebase Cloud Messaging V1 API
 */
class Firebase {

    private $project_id;
    private $credentials_file;
    private $access_token;
    private $token_expiry;
    private $CI;

    /**
     * Constructor
     */
    public function __construct() {
        $this->CI =& get_instance();
        $this->project_id = 'take-recharge-bd';
        $this->credentials_file = FCPATH . 'firebase-credentials.json';
        $this->access_token = null;
        $this->token_expiry = 0;
    }

    /**
     * Get OAuth2 access token for FCM API
     *
     * @return string Access token
     */
    private function get_access_token() {
        // Check if we already have a valid token
        if ($this->access_token && time() < $this->token_expiry) {
            return $this->access_token;
        }

        // Load the credentials file
        if (!file_exists($this->credentials_file)) {
            log_message('error', 'Firebase credentials file not found: ' . $this->credentials_file);
            return false;
        }

        $credentials = json_decode(file_get_contents($this->credentials_file), true);

        if (!isset($credentials['private_key']) || !isset($credentials['client_email'])) {
            log_message('error', 'Invalid Firebase credentials file');
            return false;
        }

        // Create JWT token
        $now = time();
        $jwt_header = base64_encode(json_encode([
            'alg' => 'RS256',
            'typ' => 'JWT'
        ]));

        $jwt_claim = base64_encode(json_encode([
            'iss' => $credentials['client_email'],
            'scope' => 'https://www.googleapis.com/auth/firebase.messaging',
            'aud' => 'https://oauth2.googleapis.com/token',
            'exp' => $now + 3600,
            'iat' => $now
        ]));

        $jwt_signature_input = $jwt_header . '.' . $jwt_claim;

        // Create signature
        $private_key = $credentials['private_key'];
        openssl_sign(
            $jwt_signature_input,
            $jwt_signature,
            $private_key,
            'SHA256'
        );

        $jwt_signature = base64_encode($jwt_signature);

        // Create JWT
        $jwt = $jwt_header . '.' . $jwt_claim . '.' . $jwt_signature;

        // Exchange JWT for access token
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://oauth2.googleapis.com/token');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
            'assertion' => $jwt
        ]));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);

        $response = curl_exec($ch);
        curl_close($ch);

        $token_data = json_decode($response, true);

        if (!isset($token_data['access_token'])) {
            log_message('error', 'Failed to get Firebase access token: ' . $response);
            return false;
        }

        $this->access_token = $token_data['access_token'];
        $this->token_expiry = time() + $token_data['expires_in'] - 300; // Expire 5 minutes early to be safe

        return $this->access_token;
    }

    /**
     * Send notification to a specific device token
     *
     * @param string $token Device token
     * @param string $title Notification title
     * @param string $body Notification body
     * @param array $data Additional data to send
     * @return array Response from FCM
     */
    public function send_to_device($token, $title, $body, $data = []) {
        $access_token = $this->get_access_token();

        if (!$access_token) {
            return [
                'success' => false,
                'error' => 'Failed to get access token'
            ];
        }

        // Flatten nested data - Firebase requires all data values to be strings
        $flat_data = [];
        foreach ($data as $key => $value) {
            if (is_array($value) || is_object($value)) {
                $flat_data[$key] = json_encode($value);
            } else {
                $flat_data[$key] = (string)$value;
            }
        }

        $message = [
            'message' => [
                'token' => $token,
                'notification' => [
                    'title' => $title,
                    'body' => $body
                ],
                'data' => $flat_data
            ]
        ];

        return $this->send_fcm_message($message);
    }

    /**
     * Send notification to a topic
     *
     * @param string $topic Topic name
     * @param string $title Notification title
     * @param string $body Notification body
     * @param array $data Additional data to send
     * @return array Response from FCM
     */
    public function send_to_topic($topic, $title, $body, $data = []) {
        $access_token = $this->get_access_token();

        if (!$access_token) {
            return [
                'success' => false,
                'error' => 'Failed to get access token'
            ];
        }

        // Remove '/topics/' prefix if it exists
        if (strpos($topic, '/topics/') === 0) {
            $topic = substr($topic, 8);
        }

        // Flatten nested data - Firebase requires all data values to be strings
        $flat_data = [];
        foreach ($data as $key => $value) {
            if (is_array($value) || is_object($value)) {
                $flat_data[$key] = json_encode($value);
            } else {
                $flat_data[$key] = (string)$value;
            }
        }

        $message = [
            'message' => [
                'topic' => $topic,
                'notification' => [
                    'title' => $title,
                    'body' => $body
                ],
                'data' => $flat_data
            ]
        ];

        return $this->send_fcm_message($message);
    }

    /**
     * Send FCM message using the V1 API
     *
     * @param array $message Message payload
     * @return array Response from FCM
     */
    private function send_fcm_message($message) {
        $access_token = $this->get_access_token();

        if (!$access_token) {
            return [
                'success' => false,
                'error' => 'Failed to get access token'
            ];
        }

        $url = "https://fcm.googleapis.com/v1/projects/{$this->project_id}/messages:send";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $access_token,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($message));

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        $response_data = json_decode($response, true);

        if ($http_code >= 200 && $http_code < 300) {
            return [
                'success' => true,
                'name' => isset($response_data['name']) ? $response_data['name'] : null
            ];
        } else {
            return [
                'success' => false,
                'error' => isset($response_data['error']['message']) ? $response_data['error']['message'] : 'Unknown error',
                'code' => $http_code,
                'response' => $response_data
            ];
        }
    }
}
