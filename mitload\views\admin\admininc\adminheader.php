<?php
if ($this->session->userdata('admin_session')) {
$distibutor=$this->session->userdata('admin_session');

$tokencode = $this->security->get_csrf_hash();
$ip_address = $_SERVER['REMOTE_ADDR']; 

//$distibutoracctype = $distibutor->user_type;

 $chkdistype=$this->mdb->getData('reseller',array('id'=>$distibutor->id));
		$distibutoracctype=$chkdistype[0]['custype'];
		$distibutorid=$chkdistype[0]['id'];
		
		
		$time=time();
		$time_check=$time-300;
		$session=session_id();

		$chkonline=$this->mdb->getData('user_online',array('session'=>$session));
		if(count($chkonline)>0 ){
			
			 $online_data=array(
			 'page'=>$page_title,
			 'ip_address'=>$ip_address,
			 'tokenid'=>$tokencode,
			 'date'=>date('Y-m-d H:i:s'),
			 'time'=>$time);
			 
			 $this->mdb->update('user_online',$online_data,array('session'=>$session, 'userid'=>$distibutor->id));
			
		}else {
			 $online_data=array(
			'userid'=>$distibutor->id,
			'session'=>$session,
			'ip_address'=>$ip_address,
			'tokenid'=>$tokencode,
			'page'=>$page_title,
			'date'=>date('Y-m-d H:i:s'),
			'time'=>$time
			);
			$this->mdb->insert('user_online', $online_data);
		
		}
		
		$this->db->where('time<', $time_check);
		$this->db->delete('user_online');

		

}


?>
<!DOCTYPE html>
<html>
<head>
	
	 <title><?php echo $page_title; ?> || <?php echo $system_title; ?></title>
   <meta name="viewport" content="width=device-width, initial-scale=1">
   
	<link href="<?php echo "".base_url()."$system_logo"; ?>" rel="icon" type="image/x-icon" id="page_favicon"/>
  
	 <!-- Bootstrap 3.3.7 -->
  <link rel="stylesheet" href="<?php echo base_url(); ?>bootstrap/css/bootstrap.min.css">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="<?php echo base_url(); ?>bootstrap/css/font-awesome.min.css">
  <!-- Ionicons -->
  <link rel="stylesheet" href="<?php echo base_url(); ?>bootstrap/css/ionicons.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="<?php echo base_url(); ?>dist/css/Admin.css?id=2">
  <!-- AdminLTE Skins. Choose a skin from the css/skins
       folder instead of downloading all of them to reduce the load. -->
  <link rel="stylesheet" href="<?php echo base_url(); ?>dist/css/skins/_all-skins.css?id=3">
  
  <!-- Morris chart -->
  <link rel="stylesheet" href="<?php echo base_url(); ?>plugins/morris/morris.css">
  <!-- jvectormap -->
  <link rel="stylesheet" href="<?php echo base_url(); ?>plugins/jvectormap/jquery-jvectormap-1.2.2.css">
  <!-- Date Picker -->
  <link rel="stylesheet" href="<?php echo base_url(); ?>plugins/datepicker/datepicker3.css">
  <!-- Daterange picker -->
  <link rel="stylesheet" href="<?php echo base_url(); ?>plugins/daterangepicker/daterangepicker.css">
  <!-- bootstrap wysihtml5 - text editor -->
  <link rel="stylesheet" href="<?php echo base_url(); ?>plugins/bootstrap-wysihtml5/bootstrap3-wysihtml5.min.css">



  <!-- Google Font -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700,300italic,400italic,600italic">

	     
   

    <!--<link href="<?php echo base_url(); ?>assets/fload/css/bootstrap.min.css" rel="stylesheet">-->
	
	<link href="<?php echo base_url(); ?>assets/fload/css/datepicker.css" rel="stylesheet">
    <link href="<?php echo base_url(); ?>assets/fload/css/chosen.css" rel="stylesheet">
<link href="<?php echo base_url(); ?>assets/fload/css/fload.css" rel="stylesheet">
	
	
	
   <SCRIPT language=Javascript>
      <!--
      function isNumberKey(evt)
      {
         var charCode = (evt.which) ? evt.which : event.keyCode
         if (charCode > 31 && (charCode < 48 || charCode > 57))
            return false;

         return true;
      }
      //-->
   </SCRIPT>
   
  
   
   
<style type="text/css">


/*CSS Digg style pagination*/
  div.pagination {
    padding: 3px;
    margin: 3px;
    text-align:center;
  }
  
  div.pagination a {
    padding: 2px 5px 2px 5px;
    margin: 2px;
    border: 1px solid #AAAADD;
    
    text-decoration: none; /* no underline */
    color: #000099;
  }
  div.pagination a:hover, div.digg a:active {
    border: 1px solid #000099;

    color: #000;
  }
  div.pagination span.current {
    padding: 2px 5px 2px 5px;
    margin: 2px;
    border: 1px solid #000099;
    
    font-weight: bold;
    background-color: #000099;
    color: #FFF;
  }
  div.pagination span.disabled {
    padding: 2px 5px 2px 5px;
    margin: 2px;
    border: 1px solid #EEE;
  
    color: #DDD;
  }


</style>

<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	 
	 
<script src="https://cdn.onesignal.com/sdks/web/v16/OneSignalSDK.page.js" defer></script>
<script>
  window.OneSignalDeferred = window.OneSignalDeferred || [];
  OneSignalDeferred.push(async function(OneSignal) {
    await OneSignal.init({
      appId: "************************************",
      safari_web_id: "web.onesignal.auto.************************************",
      notifyButton: {
        enable: true,
      },
    });
  });
</script>
</head>
<body class="skin-green-light sidebar-mini">
<div class="wrapper">


    <?php
    // Define $totalsim to prevent undefined variable error
    $totalsim = isset($distibutor->balance) ? $distibutor->balance : 0;
    include('adminnav.php');
    ?>




	  <?php include('adminleft_menu.php'); ?>

       