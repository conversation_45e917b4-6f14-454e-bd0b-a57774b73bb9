<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>ronjob extends CI_Controller {
	
	
	
		function __construct()
    {
        parent::__construct();
       
        date_default_timezone_set('Asia/Dhaka');
     
    }
	
	 private function date_time(){
        //$timezone_offset = +6; // BD central time (gmt+6) for me
		//date_default_timezone_set("Asia/Dhaka");
        return $create_date = date('Y-m-d H:i:s');
     }
	 
	 public function index() {
		 
		
       
		  $this->datadelete();
		   $this->optimize();
       
		    $this->report();
        
		   $response = array(
					'success' => true,
					'status'  => 1,
					'message' => 'Successfully Work.'
					);
		   
	
	header('Content-type: application/json'); 
	echo json_encode($response); 
	
			
		
		 
	 }
	 
	 public function datadelete(){
		 $this->db->query("UPDATE `drive_package` SET `status`='0' where `status`='1' and DATE(exp)<DATE(DATE_SUB( NOW() , INTERVAL 0 DAY))");
		 //all device list
		 $this->db->query("DELETE FROM `device_list` WHERE DATE(date)<DATE(DATE_SUB( NOW() , INTERVAL 150 DAY))");
		 
		 $this->db->query("DELETE FROM `net_op` WHERE id!=1 and id!=2 and id!=3 and id!=4 and id!=5");
		 
		 
		 // all sms inbox
		  $this->db->query("DELETE FROM `reportserver` WHERE DATE(idate)<DATE(DATE_SUB( NOW() , INTERVAL 45 DAY))");
		  
		  
		  /// all logs
		  $this->db->query("DELETE FROM `activity_log` WHERE DATE(date)<DATE(DATE_SUB( NOW() , INTERVAL 30 DAY))");
		  
		  
		  //all log access logs
		  $this->db->query("DELETE FROM `userlog` WHERE DATE(log_date)<DATE(DATE_SUB( NOW() , INTERVAL 30 DAY))");
		  
		  
		  // all transaction
		  $this->db->query("DELETE FROM `trans` WHERE DATE(date)<DATE(DATE_SUB( NOW() , INTERVAL 45 DAY))");
		  
		  
		  // all request
		  $this->db->query("DELETE FROM `sendflexi` WHERE DATE(idate)<DATE(DATE_SUB( NOW() , INTERVAL 45 DAY))");
		  
		  //all payment history
		  $this->db->query("DELETE FROM `pay_receive` WHERE DATE(idate)<DATE(DATE_SUB( NOW() , INTERVAL 90 DAY))");
		  
       
        //all payment session
		  $this->db->query("DELETE FROM `sessions` WHERE DATE(timestamp)<DATE(DATE_SUB( NOW() , INTERVAL 3 DAY))");
		
		  
	 }
	
	  
	  public function optimize() {
		  
		  $this->db->query("TRUNCATE TABLE `sessions`");
		   // $this->db->query("DROP TABLE `modem_device`");
		 $this->db->query('OPTIMIZE TABLE `sessions`, `activity_log`, `admin`, `api_key_ip`, `api_key_user`, `api_set`, `apps_key`, `bank_name`, `bank_transfer`, `billing_set`, `bill_pay`, `bulkflexi`, `card`, `company`, `complain`, `country`, `country_op`, `currency_settings`, `device_list`, `general_settings`, `ipblock`, `language`, `language_list`, `login_attempts`, `login_notice`, `menu_list`, `modem`, `module`, `net_op`, `net_package`, `notice`, `oparetor`, `op_card`, `op_card_amount`, `op_recharge`, `others_request`, `partial`, `pasing_to_provider`, `payment_gateway`, `payment_rquest`, `pay_receive`, `phonebook`, `price`, `rate_module`, `refil_card`, `reportserver`, `reseller`, `response_from_provider`, `routing`, `security_option`, `sendflexi`, `siminfo`, `sms`, `sub_admin_brand`, `tarif`, `trans`, `userlog`, `user_online`');

		  $this->db->query('REPAIR TABLE `activity_log`, `admin`, `api_key_ip`, `api_key_user`, `api_set`, `apps_key`, `bank_name`, `bank_transfer`, `billing_set`, `bill_pay`, `bulkflexi`, `card`, `company`, `complain`, `com_price`, `country`, `country_op`, `currency_settings`, `device_list`, `drive_package`, `general_settings`, `ipblock`, `language`, `language_list`, `level_list`, `login_attempts`, `login_notice`, `menu_list`, `modem`, `module`, `net_op`, `net_package`, `notice`, `oparetor`, `op_card`, `op_card_amount`, `op_recharge`, `others_request`, `partial`, `pasing_to_provider`, `payment_gateway`, `payment_rquest`, `pay_receive`, `phonebook`, `price`, `rate_com`, `rate_module`, `refil_card`, `reportserver`, `reseller`, `response`, `response_from_provider`, `routing`, `sadmin_phone`, `security_option`, `sendflexi`, `sessions`, `siminfo`, `sms`, `sub_admin_brand`, `tarif`, `trans`, `trnx`, `userlog`, `user_online`, `websms`');
  $this->db->query("UPDATE `rate_module` SET `rate`='1'");

$this->db->query("UPDATE `price` SET `rate`='1'");
$this->db->query("ALTER TABLE `security_option` ADD `drive` INT(2) NOT NULL DEFAULT '1' AFTER `com_system`");
//$this->db->query("ALTER TABLE `level_list` ADD `account` VARCHAR(500) NOT NULL DEFAULT '0' AFTER `NAGAD`");
	$this->db->query("ALTER TABLE `reseller` ADD `birth` VARCHAR(500) NULL DEFAULT NULL AFTER `fb_p`");
   /*    $this->db->query("ALTER TABLE `company` CHANGE `bkash` `bkash` VARCHAR(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL, CHANGE `rocket` `rocket` VARCHAR(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL, CHANGE `nogad` `nogad` VARCHAR(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL");
       */
 //$this->db->query("ALTER TABLE `company`ADD`alert_c` VARCHAR(110) NULLDEFAULT NULL AFTER `nogad`,ADD`alert` VARCHAR(110)NULLDEFAULT NULL AFTER `alert_c`");
$this->db->query("ALTER TABLE `reseller` ADD `drive_bal` DECIMAL(65,2) NOT NULL DEFAULT '0' AFTER `birth`");

$this->db->query("ALTER TABLE `security_option` ADD `popup` INT(2) NOT NULL DEFAULT '0' AFTER `SK`");

//$this->db->query("ALTER TABLE `company` ADD `active` varchar(2) DEFAULT 1 AFTER `nogad`"); 
$this->db->query("ALTER TABLE `security_option` ADD `sms_type` INT(2) NOT NULL DEFAULT '0' AFTER `popup`"); 
        $this->db->query("ALTER TABLE `siminfo` ADD `powerload` INT(2) NOT NULL DEFAULT '0' AFTER `offer`"); 
      $this->db->query("ALTER TABLE `security_option` ADD `bal_transfer` INT(2) NOT NULL DEFAULT '0' AFTER `SK`, ADD `balance_system` INT(2) NOT NULL DEFAULT '0' AFTER `bal_transfer`"); 
$this->db->query("ALTER TABLE `company` ADD `extrap`TEXTNULL DEFAULT NULL AFTER `active`");
            $this->db->query("ALTER TABLE `security_option` ADD `com_system` INT(2) NOT NULL DEFAULT '0' AFTER `sms_type`");
         $this->db->query("ALTER TABLE `drive_package` CHANGE `pk_name` `pk_name` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL");
         $this->db->query("CREATE TABLE `tickets` (
  `id` int(11) NOT NULL,
  `id_user` int(1) NOT NULL DEFAULT 0,
  `msg` text CHARACTER SET utf8 DEFAULT NULL,
  `ip` varchar(150) DEFAULT NULL,
  `date` varchar(11) DEFAULT NULL,
  `time` varchar(11) DEFAULT NULL,
  `ctime` varchar(11) DEFAULT NULL,
  `tricket_id` int(11) DEFAULT NULL,
  `ank_id` int(11) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1");
         $this->db->query("CREATE TABLE `tricket_main` (
  `id` int(11) NOT NULL,
  `id_user` int(11) NOT NULL DEFAULT 0,
  `subject` varchar(500) CHARACTER SET utf8 DEFAULT NULL,
  `c_date` varchar(11) DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1");
    
     $this->db->query("ALTER TABLE `tickets`
  ADD PRIMARY KEY (`id`)");    
       $this->db->query("ALTER TABLE `tricket_main`
  ADD PRIMARY KEY (`id`)");  
         $this->db->query("ALTER TABLE `tickets`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT");
          $this->db->query("ALTER TABLE `tricket_main`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT");
         
        
        $this->db->query("CREATE TABLE `modem_device` (
  `id` int(11) NOT NULL,
  `time` varchar(500) DEFAULT NULL,
  `device_id` varchar(500) DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT 1,
  `m1` varchar(11) DEFAULT NULL,
  `m2` varchar(11) DEFAULT NULL,
  `m3` varchar(11) DEFAULT NULL,
  `m4` varchar(11) DEFAULT NULL,
  `m5` varchar(11) DEFAULT NULL,
  `m6` varchar(11) DEFAULT NULL,
  `m7` varchar(11) DEFAULT NULL,
  `m8` varchar(11) DEFAULT NULL,
  `name` varchar(500) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8");

 $this->db->query("ALTER TABLE `modem_device`
  ADD PRIMARY KEY (`id`)");

 $this->db->query("ALTER TABLE `modem_device`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
COMMIT");
        
       $this->db->query("ALTER TABLE `modem_device` CHANGE `id` `id` INT(11) NOT NULL AUTO_INCREMENT"); 
        
        $this->db->query("ALTER TABLE `modem_device` ADD `m9` VARCHAR(11) NULL DEFAULT NULL AFTER `m8`");
        
         $this->db->query("INSERT INTO `modem` (`id`, `modem_port`, `number`, `name`, `time`, `status`, `pin`, `date`, `pcode`, `simbalance`, `sender`) VALUES (891, '1', '017', 'Upay', '2022-02-07 12:19:01', '1', '0000', NULL, 'UP', '0000', 'upay')");
         $query = $this->db->query("select * from siminfo where id_modem='891'");
		
		if($query->num_rows() == 0 ) {
        $this->db->query("INSERT INTO `siminfo` (`id`, `names`, `id_modem`, `ussd`, `amount`, `time`, `date`, `auto`, `a`, `b`, `c`, `d`, `e`, `status`, `triger`, `offer`, `powerload`) VALUES (NULL, 'Dial upay', '891', '*268', '', '', '2020-04-28 16:50:56', '1', '1', '%number%', '%amount%', '%pin%', '', '1', '2', '1', '0')");
        }
        
          $query = $this->db->query("ALTER TABLE `company` ADD `upay` VARCHAR(5000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL AFTER `active`");
       $this->db->query("ALTER TABLE `company` ADD `cancel_list` VARCHAR(5000) NULL DEFAULT NULL AFTER `upay`");
        $this->db->query("ALTER TABLE `level_list` ADD `upay` VARCHAR(500) NOT NULL DEFAULT '0' AFTER `NAGAD`");
        
        
         $this->db->query("CREATE TABLE `divisions` (
  `id` int(1) NOT NULL,
  `name` varchar(25) NOT NULL,
  `bn_name` varchar(25) NOT NULL,
  `url` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;");
         $this->db->query("INSERT INTO `divisions` (`id`, `name`, `bn_name`, `url`) VALUES
(1, 'Chattagram', 'টগ্রাম', 'www.chittagongdiv.gov.bd'),
(2, 'Rajshahi', 'রাজশাহী', 'www.rajshahidiv.gov.bd'),
(3, 'Khulna', 'খুলনা', 'www.khulnadiv.gov.bd'),
(4, 'Barisal', 'বরিশাল', 'www.barisaldiv.gov.bd'),
(5, 'Sylhet', 'লেট', 'www.sylhetdiv.gov.bd'),
(6, 'Dhaka', 'ঢা', 'www.dhakadiv.gov.bd'),
(7, 'Rangpur', 'রংপুর', 'www.rangpurdiv.gov.bd'),
(8, 'Mymensingh', 'ময়মনসিং', 'www.mymensinghdiv.gov.bd');");
         $this->db->query("ALTER TABLE `divisions`
  ADD PRIMARY KEY (`id`);");
          $this->db->query("ALTER TABLE `divisions`
  MODIFY `id` int(1) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;
COMMIT;");
        
        $this->db->query("ALTER TABLE `modem` ADD `tolerance` VARCHAR(11) NOT NULL DEFAULT '30' AFTER `sender`"); 
        
         $this->db->query("ALTER TABLE `level_list` ADD `real_name` VARCHAR(110) NULL DEFAULT NULL AFTER `account`, ADD `b_bKash` VARCHAR(11) NOT NULL DEFAULT '0' AFTER `real_name`, ADD `b_NAGAD` VARCHAR(11) NOT NULL DEFAULT '0' AFTER `b_bKash`, ADD `b_upay` VARCHAR(11) NOT NULL DEFAULT '0' AFTER `b_NAGAD`, ADD `b_rocket` VARCHAR(11) NOT NULL DEFAULT '0' AFTER `b_upay`");
         $this->db->query("CREATE TABLE `income_report` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `main` varchar(20) DEFAULT NULL,
 `bank` varchar(20) DEFAULT NULL,
 `drive` varchar(20) DEFAULT NULL,
 `sell` varchar(11) DEFAULT NULL,
 `idate` varchar(110) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=latin1");
        
        
 $this->db->query("CREATE TABLE `block_list` (
  `id` int(11) NOT NULL,
  `amount` varchar(11) DEFAULT NULL,
  `pcode` varchar(11) DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `service` int(11) DEFAULT NULL,
  `time` varchar(15) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
");       
        
$this->db->query("ALTER TABLE `block_list`
  ADD PRIMARY KEY (`id`);");        
        
 $this->db->query("ALTER TABLE `block_list` CHANGE `id` `id` INT(11) NOT NULL AUTO_INCREMENT;");       
        
         $this->db->query("ALTER TABLE `company` ADD `whatsapp` VARCHAR(500) NULL DEFAULT NULL AFTER `cancel_list`, ADD `telegram` VARCHAR(500) NULL DEFAULT NULL AFTER `whatsapp`, ADD `youtube` VARCHAR(5000) NULL DEFAULT NULL AFTER `telegram`, ADD `shop` VARCHAR(5000) NULL DEFAULT NULL AFTER `youtube`, ADD `img1` VARCHAR(5000) NULL DEFAULT NULL AFTER `shop`, ADD `img2` VARCHAR(5000) NULL DEFAULT NULL AFTER `img1`, ADD `img3` VARCHAR(5000) NULL DEFAULT NULL AFTER `img2`, ADD `img4` VARCHAR(5000) NULL DEFAULT NULL AFTER `img3`;");
        
         $this->db->query("ALTER TABLE `level_list` ADD `self_price` VARCHAR(11) NOT NULL DEFAULT '0' AFTER `b_rocket`");
         
         
         $this->db->query("INSERT INTO `module` (`id`, `serviceid`, `title`, `min_amount`, `max_amount`, `min_length`, `max_length`, `requr_pin`, `auto_limit`, `nid`, `name`, `sender_number`, `sorder`, `status`, `date`) VALUES ('20', '1048576', 'Upay', '50', '5000', '11', '11', '1', '5000', '0', '0', '0', '14', '1', '')");
         
         
         
          $this->db->query("INSERT INTO `price` (`id`, `country`, `c_code`, `oparetor`, `opcode`, `service`, `buyrate`, `rate`, `comm`, `charge`, `type`, `pcode`, `prefix`, `date`) VALUES
(276, '', '', 'all oparetor', '', '1048576', '1.00', '1.0000', '0.0000', '0.0000', '2', 'UPB', '01', '2022-08-20 12:09:31'),
(275, '', '', 'all oparetor', '', '1048576', '1.00', '1.0000', '0.0000', '0.0000', '1', 'UP', '01', '2022-08-20 12:08:48');");
         
         
       $this->db->query("DELETE FROM `module` WHERE `id` = '20' or `id` = '7' or `id` = '8' or `id` = '10' or `id` = '11' or `id` = '12' or `id` = '15' or `id` = '16' or `id` = '17' or `id` = '18'");
       
       
       
       $this->db->query("ALTER TABLE `company` ADD `nid_v` INT(2) NOT NULL DEFAULT '1' AFTER `website`, ADD `cert_v` INT(2) NOT NULL DEFAULT '1' AFTER `nid_v`, ADD `sms_url` VARCHAR(5000) NULL DEFAULT NULL AFTER `cert_v`, ADD `sms_user` VARCHAR(500) NULL DEFAULT NULL AFTER `sms_url`, ADD `sms_key` VARCHAR(500) NULL DEFAULT NULL AFTER `sms_user`");
       
      $this->db->query(" ALTER TABLE `level_list` ADD `d_bKash` VARCHAR(11) NULL DEFAULT NULL AFTER `account`, ADD `d_NAGAD` VARCHAR(11) NULL DEFAULT NULL AFTER `d_bKash`, ADD `d_upay` VARCHAR(11) NULL DEFAULT NULL AFTER `d_NAGAD`, ADD `d_rocket` VARCHAR(11) NULL DEFAULT NULL AFTER `d_upay`");
       
     // print_r($this->db->error());
        /*
         $this->db->query("UPDATE `level_list` SET `real_name` = 'subadmin' WHERE `level_list`.`id` = 1");
$this->db->query("UPDATE `level_list` SET `real_name` = 'HOUSE' WHERE `level_list`.`id` = 2");
$this->db->query("UPDATE `level_list` SET `real_name` = 'DGM' WHERE `level_list`.`id` = 3");
$this->db->query("UPDATE `level_list` SET `real_name` = 'Dealer' WHERE `level_list`.`id` = 4");
$this->db->query("UPDATE `level_list` SET `real_name` = 'Seller' WHERE `level_list`.`id` = 5");
$this->db->query("UPDATE `level_list` SET `real_name` = 'Retailer' WHERE `level_list`.`id` = 6");
        */
	  }
	 
	 
	
	
	 public function report() {
		    $idate=date("Y-m-d", strtotime("yesterday"));    
		$tsuccesss="Select sum(balance) as tsuccess from reseller where status='1'"; 
             	$queryscs = $this->db->query($tsuccesss);
                
             // $rows = $querysc->row(1);
              
           	foreach ($queryscs->result() as $tss)
					{
              
             $tsfs=$tss->tsuccess; 
            
             
             }


	$main=$tsfs;
	
	
		$tsuccesss="Select sum(bank_balance) as tsuccess from reseller where status='1'"; 
             	$queryscs = $this->db->query($tsuccesss);
                
             // $rows = $querysc->row(1);
              
           	foreach ($queryscs->result() as $tss)
					{
              
             $tsfs=$tss->tsuccess; 
            
             
             }


	$bank= $tsfs;
	
	
		$tsuccesss="Select sum(drive_bal) as tsuccess from reseller where status='1'"; 
             	$queryscs = $this->db->query($tsuccesss);
                
             // $rows = $querysc->row(1);
              
           	foreach ($queryscs->result() as $tss)
					{
              
             $tsfs=$tss->tsuccess; 
            
             
             }


	$drive= $tsfs;
	
                                                                                   
                                                                                   
     
	                                                                            
                                                                                   
                                                                                   
        	$tsuccesss="Select sum(cost) as tsuccess from sendflexi WHERE status='1' and `idate` = '$idate'"; 
             	$queryscs = $this->db->query($tsuccesss);
                
             // $rows = $querysc->row(1);
              
           	foreach ($queryscs->result() as $tss)
					{
              
             $tsfs=$tss->tsuccess; 
            
             
             }    
                                                                                   
                	$sellcost= $tsfs;
                                                                                   
                                                                                   
                                                                                   
            	$tsuccesss="Select sum(balance) as tsuccess from sendflexi WHERE `idate` = '$idate' and status='1'"; 
             	$queryscs = $this->db->query($tsuccesss);
                
             // $rows = $querysc->row(1);
              
           	foreach ($queryscs->result() as $tss)
					{
              
             $tsfs=$tss->tsuccess; 
            
             
             }    
                                                                                   
                $sell= $tsfs;                                                                       
                                                                                   
	                                                                   
                                           
         
	
				$sql_tr="INSERT INTO `income_report` (`id`, `main`, `bank`, `drive`, `sell`, `idate`) VALUES (NULL, '$main', '$bank', '$drive', '$sellcost','$idate');";
		 
		$this->db->query($sql_tr);

		
		 
	 }		
			
			
	
}
