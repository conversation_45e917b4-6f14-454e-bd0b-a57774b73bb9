<?php
defined('BASEPATH') OR exit('No direct script access allowed');
  $domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);

function sends($title,$body){
    $domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);




    $part = explode("*",$body);
    if(!empty($part[2])){
$url="http://$domain/offline/sms?pa=5463243&to=$part[1]&message=".urlencode($part[2])."";

$ch = curl_init();

    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, $url);

    $data = curl_exec($ch);
    curl_close($ch);

    return $data;
}
}
function levelchk($s){

$n=filter_var($s, FILTER_SANITIZE_NUMBER_INT);
if(!empty($n)){ $out="$n";}else{$out="$s";}
if(!empty($out)){
	 return "($out)";
}

	}
class Apiapp extends CI_Controller {

	function __construct()
    {
        parent::__construct();

        date_default_timezone_set('Asia/Dhaka');

		$this->load->library('form_validation');
		$this->load->library('user_agent');
    }

	 private function date_time(){
        return $create_date = date('Y-m-d H:i:s');
     }





	public function index()
	{
	$time=time();




	$dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
	$last_login=$dt->format('j F Y g:i A');
	$idate=$dt->format('Y-m-d');

	if($_POST) {

	    	$reseller5 = $this->db->get_where('level_list',array('name' =>'reseller5'))->row()->real_name;
			$reseller4 = $this->db->get_where('level_list',array('name' =>'reseller4'))->row()->real_name;
			$reseller3 = $this->db->get_where('level_list',array('name' =>'reseller3'))->row()->real_name;
			$reseller2 = $this->db->get_where('level_list',array('name' =>'reseller2'))->row()->real_name;
			$reseller1 = $this->db->get_where('level_list',array('name' =>'reseller1'))->row()->real_name;
	$this->form_validation->set_rules('username', 'Userid', 'trim|required|min_length[3]|max_length[30]');
	$this->form_validation->set_rules('password', 'Password', 'trim|required|min_length[4]|max_length[30]');


        if ($this->form_validation->run() == FALSE) {


		 $response = array(
					'success' => 0,
					'errors'  => 'not empty',
					'message' => 'Please Enter Correct Login Info.'
					,'stat' => 0,
				'alllevel'=>''.$reseller5.'","'.$reseller4.'","'.$reseller3.'","'.$reseller2.'","'.$reseller1.'');

		}else {

	$username = $this->security->xss_clean($this->input->post('username'));
	$password = $this->security->xss_clean($this->input->post('password'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
		$ftoken = $this->security->xss_clean($this->input->post('ftoken'));


	if(!empty($username) && !empty($password) && !empty($deviceid) ) {
	//$this->load->model('company');
        $result = $this->mit->login($username,$password);
		 if ($result && $result->status==1) {
		 $ucid=$result->id;
		 $logincount=$result->login_count;

		$activests =$result->status;
		$uname =$result->username;
		$custype =$result->custype;
		$appsAccess=$result->appsAccess;
		 	$device_lock=$result->device_lock;
		$loginplus = $logincount+1;
		 	 $enbaleotp=$result->enbale_otp;
		  $choseotp=$result->otp_choice;
		 $uemail=$result->email;
		  $umobile=$result->mobile;
			$mylev=$custype;



		if($mylev=='subadmin')$postlevel=''.$reseller5.'","'.$reseller4.'","'.$reseller3.'","'.$reseller2.'","'.$reseller1.'';
		if($mylev=='reseller5')$postlevel=''.$reseller4.'","'.$reseller3.'","'.$reseller2.'","'.$reseller1.'';
		if($mylev=='reseller4')$postlevel=''.$reseller3.'","'.$reseller2.'","'.$reseller1.'';
		if($mylev=='reseller3')$postlevel=''.$reseller2.'","'.$reseller1.'';
		if($mylev=='reseller2')$postlevel=''.$reseller1.'';


		$sqlcont="SELECT * FROM device_list WHERE tokenid='$deviceid' and userid='$ucid'";

		$querycount = $this->db->query($sqlcont);

		foreach ($querycount->result() as $row3){
		  	 $mysafe_status=$row3->status;
		  	  $myfsttoken=$row3->tokenid;
		  	   $tblremember=$row3->remember;

		  	  }


		if($logincount>0){

		 	$access='no';


		 }else {
		 	$access='yes';

		 }

		 	if($mysafe_status==0 && $device_lock==1){
	    	$access='no';

	}

	if($device_lock==0){
	    $access='yes';

	    $mysafe_status==1;

	}


		if($mysafe_status==1 || $access=='yes') {

		if($appsAccess==1) {

		 if($activests==0) {

		 $response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'This ID is Block.'
					,'stat' => 0,
					'choseotp'=>$choseotp);


		 }else {


		 $loginupdate = array(
						"ip" => $_SERVER['REMOTE_ADDR'],
						"login_count" => $loginplus,
						"last_login" => $this->date_time()
					);

		 $this->mdb->update('reseller',$loginupdate,array('id'=>$ucid));

		$ctype="user";
		$logtype="apps";
		 $otppas=rand(111111,999999);

		 $hashotp = $this->mdb->generate($otppas);
		$secretcode=sha1(uniqid(rand(1111111,9999999),true) );



		//$secretcode=sha1(uniqid(rand(),true));
		$logtype="web";
		if($enbaleotp==1) {
		 $otpupdate = array(
						"otp" => $hashotp
					);

		 $this->mdb->update('reseller',$otpupdate,array('id'=>$ucid));


		  if($choseotp==3)$to=$uemail;
			       if($choseotp==4)$to=$umobile;
			      $subject="$system_name OTP CODE $otppas";
			      $msg="$system_name Dear $uname, Your OTP code is $otppas from ip $_SERVER[REMOTE_ADDR]";

			      if($choseotp==3 or $choseotp==4){
			  $code=$this->mit->mail_send($to, $subject, $msg);
			      }
		}

		$this->mit->insertLog($ucid,$ctype,$secretcode,$logtype);
		//$this->session->set_userdata('member_session', $result);

		$msg= 'Successful Login';
		$this->mit->InsertActivetUser($ucid,$msg,'Apps');


		$query = $this->db->query("select * from apps_key where userid='$ucid'");
		$time= time();

		if($query->num_rows() == 0 ) {

		$applog_key_insert = "INSERT INTO `apps_key` (`id`, `userid`, `token`, `date`, `time`, `deviceid`, `status`) VALUES (NULL, '$ucid', '$secretcode', '$last_login', '$time', '$deviceid', '1')";

		$this->db->query($applog_key_insert);

		}else {



			 $data = array(
					'token' => $secretcode,
					'time' => $time,
					'deviceid' => $deviceid,
					'date' => $last_login
					);
					$this->db->where('userid',$ucid);

					$this->db->update('apps_key',$data);

			$bq="UPDATE `apps_key` SET `accesscount` = accesscount+1 WHERE `apps_key`.`userid` = '$ucid'";

			$this->db->query($bq);

					//$this->mdb->update('apps_key', $apiupdate, array('userid'=>$ucid));
		}





		$response = array(
					'success' => 1,
					'status'  => 1,
					'token' =>$secretcode,
					'username' =>$uname,
					'otp' => 1,
					'message' => 'Successful Login.',
					'stat' => 1,
					'choseotp'=>$choseotp,
					'postlevel'=>$postlevel
					);


		if(!empty($ftoken)){
		    $note = array(
						"note" => $ftoken
					);

		 $this->mdb->update('reseller',$note,array('id'=>$ucid));

		}


		 }
		 }else {
		$msg= 'Your Apps Access Not Allow';
		$response = array(
					'success' => 1,
					'status'  => 2,
					'message' => $msg
				,'stat' => 1,
				'choseotp'=>$choseotp,
				'postlevel'=>$postlevel,
					'username' =>$uname,);

		 }
		 }else {

		$remember=0;
      	$this->mit->devicelist($ucid,$remember,'yes',$deviceid,'Apps');
      	$msg= 'Multiple device login off plz contact with support';
		$trytype = 'password';

		$this->mit->increase_login_attempts($username,$password,$msg,$trytype);

			 $response = array(
					'success' => 0,
					'status'  => 2,
					'message' => $msg,
						'stat' => 1,
					'choseotp'=>$choseotp,
					'postlevel'=>$postlevel,
						'username' =>$uname,

					);

		 }

		 } else {

		$msg= 'Invalid ID and password';
		//$this->mit->InsertActivetUser($username,$msg);

		$this->mit->increase_login_attempts($username,$password,$msg);

		  $response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Invalid ID and password.'
					,'stat' => 0,
				'alllevel'=>''.$reseller5.'","'.$reseller4.'","'.$reseller3.'","'.$reseller2.'","'.$reseller1.'');
		 }

		 }else {
			 $response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Please enter ID and password.'
				,'stat' => 0,
				'alllevel'=>''.$reseller5.'","'.$reseller4.'","'.$reseller3.'","'.$reseller2.'","'.$reseller1.'');


		 }
		}
		}else {

			$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
				,'stat' => 0	);


		}


		/* Output header */
			header('Content-type: application/json');
			echo json_encode($response);

		//

	}



  public function bank() {

	if($_POST) {
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;

	$lastlogID = $this->mit->lastlogID($userid);

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;

	if(!empty($userid)) {




		if($this->input->post()){

   	 	$var=$this->input->post();
		$number       = $this->security->xss_clean($this->input->post('number'));
		$amount       = $this->security->xss_clean($this->input->post('amount'));
		$type         = $this->security->xss_clean($this->input->post('type'));
		$serviceget   = $this->security->xss_clean($this->input->post('service'));

		$bank_name    = $this->security->xss_clean($this->input->post('bank_name'));
		$area   	  = $this->security->xss_clean($this->input->post('area'));
		$holdername  = $this->security->xss_clean($this->input->post('holdername'));
		$note   	  = $this->security->xss_clean($this->input->post('note'));
		$pincode   	  = $this->security->xss_clean($this->input->post('pinn'));
		if($serviceget=='8'){
        $pcode = $this->db->get_where('billing_set',array('name' =>$bank_name))->row()->bill_code;
		$prcode = $this->db->get_where('billing_set',array('name' =>$bank_name))->row()->bill_code;

        }else{
		$pcode = $this->db->get_where('bank_name',array('bcode' =>$bank_name))->row()->bname;
		$prcode = $this->db->get_where('bank_name',array('bname' =>$bank_name))->row()->bcode;
        }
	$this->irs->rechargeService($serviceget,$number,$amount,$userid,$prcode,$type,$pincode,'yes',$holdername,$area,$note,$pcode);
		$getirdata= $this->session->userdata('irservicelog');
		$irstsview = $getirdata['irserv'];
		$irsmessage = $getirdata['irsmessage'];

		if($irstsview=="error") {

$response = array(
					'success' => 0,
					'status'  => 2,
					'message' =>$irsmessage.$pcode
					);

		}else {

			$msg= 'New Request Bank Number '.$number.' and amount '.$amount;
			$this->mit->InsertActivetUser($uid,$msg);

				$response = array(
					'success' => 1,
					'status'  => 1,
					'message' =>$irsmessage,
					);
		}
		}

	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Match.'
					);
	}

	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}
	header('Content-type: application/json');
	echo json_encode($response);
	}

		public function Otherhistory() {


	if($_GET) {
	$apikey = $this->security->xss_clean($this->input->get('token'));
	$deviceid = $this->security->xss_clean($this->input->get('deviceid'));
		$uif = $this->security->xss_clean($this->input->get('uif'));
	$number = $this->security->xss_clean($this->input->get('number'));

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey, 'deviceid' =>$deviceid))->row()->userid;
	$lastlogID = $this->mit->lastlogID($userid);

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;

	$chkpass = $this->db->get_where('reseller',array('id' =>$userid))->row()->pincode;

	if(!empty($userid)) {

		$response = array();

		if($uif=='8' or $uif=='524288'){


		if(!empty($number)) {
			    $sql="SELECT * from bill_pay where user_id='$userid' and account='$number' order by id desc limit 100";

			}else{

			$sql="SELECT * from bill_pay where user_id='$userid' order by id desc limit 100";

	}


		}else{

			if(!empty($number)) {
			    $sql="SELECT * from bank_transfer where user_id='$userid' and account='$number' order by id desc limit 100";

			}else{

			$sql="SELECT * from bank_transfer where user_id='$userid' order by id desc limit 100";

	}

}


$query = $this->db->query($sql);
		foreach ($query->result() as $user_data){
	  $type=$user_data->type;
    $opid=$user_data->bank_name;

  $status=$user_data->status;





if($status==0){$msg="Pending";}
if($status==1){$msg="Completed";}
if($status==2){$msg="Cancelled";}
if($status==4){$msg="Waiting";}
          if($uif=='8' or $uif=='524288'){

          $img="".base_url()."assets/img/32.png";
$banktype="Billpay";
          }else{
if($type==1) {$banktype="Saving";}else {$banktype="Current";}

            $img="".base_url()."assets/img/32.png";
          }

$payusername = $this->db->get_where('reseller',array('id' =>$user_data->user_id))->row()->username;

	$bn = $this->db->get_where('bank_name',array('bcode' => $user_data->bank_name))->row()->bname;

	$json_data = array(
        'number' => $user_data->account,
            'balance' => $user_data->balance,
            'cost' =>  $user_data->cost,
            'prebalance' => $user_data->prebal,
            'serviceName' => $banktype,
            'type' => $bn,
            'status' => $msg,
            'trxID' => $user_data->trxid,
            'time' => $user_data->date,
            'sender' => $payusername ,
            'ut' => $user_data->date,
        'img' => $img,
   'debit' => $user_data->balance,
            'credit' => $user_data->balance,
 'img' => $img,
  'service' => '32',
  'pcode' => $user_data->provider_name,
        );


        array_push($response, $json_data);
		}

}else {
	$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Match.'
					);

	}

	}else {
	$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);

	}

	header('Content-type: application/json');

	echo json_encode(array("bmtelbd"=>$response));


	}



	public function bankList(){


		$response = array();
		$sql="SELECT * FROM `bank_name` where status=1 order by id desc";

		$query = $this->db->query($sql);
		foreach ($query->result() as $row){

		$jsondata = array(
            'id' => $row->id,
            'name' => $row->bname,
			'code' => $row->bcode,
        );

		 array_push($response, $jsondata);

		}


header('Content-type: application/json');
		echo json_encode(array("bmtelbd"=>$response));

	}






	public function Rechg() {






		$response = array();

		$sql="SELECT * from reseller";

			$query = $this->db->query($sql);
		foreach ($query->result() as $user_data){

		$sql_rate="SELECT * from rate_module where tarif='$user_data->id' and service='262144' and type='1' and status=1";
			$queryrate = $this->db->query($sql_rate);
				if($queryrate->num_rows() <= 0 ) {



			$id=$user_data->id;
			$username=$user_data->username;






	  $json_data = array(

           // 'username' => $uname,
            'username' => $username,

        );
        array_push($response, $json_data);
	}

		}




	header('Content-type: application/json');
			//$response=array("bmtelbd"=>$response);


	echo json_encode(array("bmtelbd"=>$response));


	}



	//only user check here
	public function Userchk() {
		if($_POST) {
	$this->form_validation->set_rules('username', 'Userid', 'trim|required|min_length[3]|max_length[30]');

        if ($this->form_validation->run() == FALSE) {


		 $response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Please Enter Correct Login Info.'
					);

		}else {

	$username = $this->security->xss_clean($this->input->post('username'));

	 $this->db->select('*');
            $this->db->from('reseller');
            $this->db->where('username', $username);
			$this->db->where('status',1);
            $query=$this->db->get();

			if($query->num_rows()>0) {

				$response = array(
					'success' => 1,
					'status'  => 1,
					'message' => 'success'
					);
			}else {
				$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Please Enter Correct Login Info.'
					);

			}

		}
		}// post

		else {

			$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);

		}


		/* Output header */
			header('Content-type: application/json');
			echo json_encode($response);

		//

	}


public function pinchk() {

	if($_POST) {

	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
		$pin = $this->security->xss_clean($this->input->post('pin'));

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;
	if(!empty($userid)){
	$lastlogID = $this->mit->lastlogID($userid);

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;
		$utrye = 'user';
		$ctar = $this->db->get_where('reseller',array('id' =>$userid))->row()->otp_choice;

		if($ctar==5){$pattern=1;}else{$pattern=0;}
	$chking=$this->mit->otpchk($pin,$utrye,$userid,$pattern);
	if($chking) {

	$cbalance = $this->mit->accountbalance($userid);
	$response = array(
					'success' => 1,
					'status'  => 1,
					'message' => 'pin correct'
					);

	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Match.'
					);
	}


	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Match.'
					);
	}
	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}
	header('Content-type: application/json');
	echo json_encode($response);
	}





public function dlock() {

	if($_POST) {
	$apikey = $this->security->xss_clean($this->input->post('token'));
$tar= $this->security->xss_clean($this->input->post('tar'));
$hash= $this->security->xss_clean($this->input->post('hash'));
$ck= $this->security->xss_clean($this->input->post('ck'));
$type= $this->security->xss_clean($this->input->post('type'));

	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
		$pin = $this->security->xss_clean($this->input->post('pin'));

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;

	$lastlogID = $this->mit->lastlogID($userid);

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;
		$utrye = 'user';
	$chking=$this->mit->otpchk($pin,$utrye,$userid);

if($tar=='3' or $tar=='4'){


$chking=true;

}


	if($chking) {


if($tar=='3'){
$ctar = $this->db->get_where('reseller',array('id' =>$userid))->row()->device_lock;
}
if($tar=='4'){
$ctar = $this->db->get_where('reseller',array('id' =>$userid))->row()->otp_choice;
if($ck==1){
    if($type==5){

        if(strlen($hash)>4){
        	$hash = $this->mdb->generate($hash);
    $odata=array(
			 'otp'=>$hash,
			  	'otp_choice' =>$type
			  );
    }

    }else if($type==0){
       $odata=array(
			  'enbale_otp' =>'0',
			  	'otp_choice' =>$type
			  );

    }else{

      $odata=array(
			  'enbale_otp' =>'1',
			  	'otp_choice' =>$type
			  );

    }

			  	$this->db->where('id',$userid);
		$this->db->update('reseller',$odata);


file_put_contents('test.txt',$hash);



}
}
if($tar=='3' or $tar=='4'){
	$response = array(
					'success' => 1,
					'status'  => 1,
					'message' => $ctar,
						'type' => $ctar
					);

}else{

if($tar==1){$atar=1; }else{$atar=0;}

	$q3="UPDATE reseller SET device_lock = $atar WHERE id = $userid";
		$this->db->query($q3);



	$q4="UPDATE device_list SET status = 1 WHERE tokenid='$deviceid' and userid='$userid'";
		$this->db->query($q4);




	$response = array(
					'success' => 1,
					'status'  => 1,
					'message' => 'Successful',
					'type' => 0
					);

}





	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Match.'
					);
	}

	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}
	header('Content-type: application/json');
	echo json_encode($response);
	}







	public function loginchk() {

	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));

	$ago = time()-1800;

	if($_POST) {

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;

	$stime = $this->db->get_where('apps_key',array('token' =>$apikey, 'deviceid' =>$deviceid))->row()->time;

	$lastlogID = $this->mit->lastlogID($userid,'user');

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;

	if(!empty($userid)) {

	if($apikey==$securid) {
		if($stime>$ago) {

		$response = array(
					'success' => 1,
					'status'  => 1,
					//'tiss'  => date('Y-m-d H:i:s',$stime),
					//'lst'  => date('Y-m-d H:i:s',time()),
					'message' => 'logged'
					);
		}else {
			$ctype="user";
		$logtype="apps";
		$secretcode=sha1(uniqid(rand(1111111,9999999),true) );
		$this->mit->insertLog($userid,$ctype,$secretcode,$logtype);

			 $data = array(
					'token' => $secretcode,
					'time' => time(),
					'date' => $this->date_time()
					);
					$this->db->where('userid',$userid);

					$this->db->update('apps_key',$data);

			$response = array(
					'success' => 0,
					'status'  => 2,
					'token' => $secretcode,
					'message' => 'logout'
					);
		}

	}else {


		$ctype="user";
		$logtype="apps";
		$secretcode=sha1(uniqid(rand(1111111,9999999),true) );
		$this->mit->insertLog($userid,$ctype,$secretcode,$logtype);

		 $data = array(
					'token' => $secretcode,
					'time' => time(),
					'date' => $this->date_time()
					);
					$this->db->where('userid',$userid);

					$this->db->update('apps_key',$data);

			$bq="UPDATE `apps_key` SET `accesscount` = accesscount+1 WHERE `apps_key`.`userid` = '$userid'";

					$this->db->query($bq);

		$response = array(
					'success' => 0,
					'status'  => 2,
					'token' => $secretcode,
					'otp' => 1,
					'message' => 'logout'
					);

	}
	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Match.'
					);

	}

	}// post method
	else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}

	header('Content-type: application/json');
	echo json_encode($response);

	}
	public function otp() {

	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));

	$otppass = $this->security->xss_clean($this->input->post('otpcode'));

	if($_POST) {

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey, 'deviceid' =>$deviceid))->row()->userid;

	$lastlogID = $this->mit->lastlogID($userid);

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;

	$chkpass = $this->db->get_where('reseller',array('id' =>$userid))->row()->pincode;

	$userstatus = $this->db->get_where('reseller',array('id' =>$userid))->row()->status;
	$appsAccess = $this->db->get_where('reseller',array('id' =>$userid))->row()->appsAccess;

	$logincount = $this->db->get_where('reseller',array('id' =>$userid))->row()->login_count;

	$mysafe_status = $this->db->get_where('device_list',array('userid' =>$userid, 'tokenid' =>$deviceid))->row()->status;

	//$mysafe_status =$this->db->query("select * from device_list where userid='$userid' and tokenid='$deviceid' and status=1");

	if($logincount>0){
	$access='no';
	}else {
	$access='yes';
	}


	if(!empty($userid)) {

	if($mysafe_status==1 || $access=='yes') {

	$usrtype ='user';

	$chk= $this->mit->otpchk($otppass,$usrtype,$userid);
	if($chk) {
		$remember=1;
		$this->mit->devicelist($userid,$remember,'no', $deviceid, 'Apps');
		//$this->mit->clear_login_attempts($username);
		if($userstatus==1) {

		if($appsAccess==1) {


		$response = array(
					'success' => 1,
					'status'  => 1,
					'message' => 'seccess'
					);

		}else {

		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Your Apps Access Is Block.'
					);

		}

	}else {

		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Your Account Is Block.'
					);

	}
	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Otp Not Valid.'
					);

	}

	// devid verify
	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Your Device ID Not Allow.'
					);
	$remember=0;
    $this->mit->devicelist($userid,$remember,'yes',$deviceid,'Apps');

	}
	// user chk
	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Username Not Match.'
					);

	}


	}else {
	$response = array(
		'success' => 0,
		'status'  => 2,
		'message' => 'Empty Not Work.'
		);
	}

	header('Content-type: application/json');
	echo json_encode($response);
	}




	public function balance() {

	if($_POST) {
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));

$type = $this->security->xss_clean($this->input->post('type'));

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;

	$lastlogID = $this->mit->lastlogID($userid);

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;

	if(!empty($userid)) {
	$sql="Select * from notice where id='1' ORDER BY ID DESC LIMIT 1";

		$query = $this->db->query($sql);
		foreach ($query->result() as $row){

						$oldnotice=$row->notice;
		}


	$cbalance = $this->mit->accountbalance($userid,$type);
	$response = array(
					'success' => 1,
					'status'  => 1,
					'message' => $cbalance,
						'notice' => $oldnotice
					);

	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Match.'
					);
	}

	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}
	header('Content-type: application/json');
	echo json_encode($response);
	}








	public function relnumber() {


	if($_GET) {
	$apikey = $this->security->xss_clean($this->input->get('token'));
	$deviceid = $this->security->xss_clean($this->input->get('deviceid'));



	$userid = $this->db->get_where('apps_key',array('token' =>$apikey, 'deviceid' =>$deviceid))->row()->userid;
	$lastlogID = $this->mit->lastlogID($userid);

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;

	$chkpass = $this->db->get_where('reseller',array('id' =>$userid))->row()->pincode;

	if(!empty($userid)) {

		$response = array();
		$sql="SELECT DISTINCT phone from sendflexi where userid='$userid' order by id desc";


	$query = $this->db->query($sql);

	$total=$query->num_rows();
	if($total==0){
	    $response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not found.'
					);

	}
		foreach ($query->result() as $user_data){
			 $number=$user_data->phone;
		  $json_data = array(
            'number' => $user_data->phone,
            	'success' => 1,
					'status'  => 2,
        );
        array_push($response, $json_data);
		}

}else {
	$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Match.'
					);

	}

	}else {
	$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);

	}

	header('Content-type: application/json');
			//$response=array("bmtelbd"=>$response);


	echo json_encode(array("softwarelabbd"=>$response));


	}








	public function role() {

	$domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);

	if($_GET) {


	$ipath='img';

	$bal_transfer = $this->db->get_where('security_option',array('id' =>1))->row()->bal_transfer;

	$apikey = $this->security->xss_clean($this->input->get('token'));
	$deviceid = $this->security->xss_clean($this->input->get('deviceid'));
		$item = $this->security->xss_clean($this->input->get('item'));

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey, 'deviceid' =>$deviceid))->row()->userid;

	$lastlogID = $this->mit->lastlogID($userid);

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;

	if(!empty($userid)) {

	$sql="Select * from notice ORDER BY ID DESC LIMIT 1";

		$query = $this->db->query($sql);
		foreach ($query->result() as $row){

						$oldnotice=$row->notice;
		}




	$cbalance = $this->mit->accountbalance($userid);

	$types = $this->db->get_where('reseller',array('id' =>$userid))->row()->type;
		$memberlevel = $this->db->get_where('reseller',array('id' =>$userid))->row()->custype;

			$newlevel = $this->db->get_where('level_list',array('name' =>$memberlevel))->row()->real_name;

			if(empty($newlevel)){
	if($memberlevel=='reseller5'){$newlevel='HOUSE';}
				else if($memberlevel=='reseller4'){$newlevel='DGM';}
					else if($memberlevel=='reseller3'){$newlevel='Dealer';}
				else if($memberlevel=='reseller2'){$newlevel='Seller';}
				else if($memberlevel=='reseller1'){$newlevel='Retailer';}else if($memberlevel=='subadmin'){$newlevel='subadmin';}
			}




$level=$newlevel;



			  $values = $this->mit->valuesOnBinPosition($types);
            foreach ($this->mit->valuesOnBinPosition($types) as $Key => $Values):
                $client_typ[$Key] = $Values;
            endforeach;

		$response = array();

			$chk_data=$this->mdb->getData('company',array('id'=>1));
				$whatsapp=$chk_data[0]['whatsapp'];
				$telegram=$chk_data[0]['telegram'];
				$youtube=$chk_data[0]['youtube'];
				$shop=$chk_data[0]['shop'];
				$img1=$chk_data[0]['img1'];
				$img2=$chk_data[0]['img2'];
				$img3=$chk_data[0]['img3'];
				$img4=$chk_data[0]['img4'];

		if(!empty($item)){
		$jsonh = array('success' => 1,
					'status'  => 1,
					'service' => '0',
	'img' => "".base_url()."assets/$ipath/history_desh.png",
					'name' => 'All History',
	'message' => $cbalance,
	'notice' => $oldnotice,
	'level' => $level,
		'act' => 'all',

					);

			array_push($response, $jsonh);




$jsonp = array('success' => 1,
					'status'  => 1,
					'service' => '11',
	'img' => "".base_url()."assets/$ipath/3232332fff.png",
					'name' => 'Payment',
	'message' => $cbalance,
	'notice' => $oldnotice,
	'level' => $level,
		'act' => 'payment',

					);

			array_push($response, $jsonp);




		}else{




			$json1 = array('success' => 1,
					'status'  => 1,
					'service' => '1',
	'img' => "".base_url()."assets/$ipath/reseller_add.png",
					'name' => 'Add Reseller',
	'message' => $cbalance,
	'notice' => $oldnotice,
	'level' => $level,
	'act' => 'addres',
	'whatsapp' => $whatsapp,
	'telegram' => $telegram,
	'youtube' => $youtube,
	'shop' => $shop,
	'img1' => $img1,
	'img2' => $img2,
	'img3' => $img3,
	'img4' => $img4

					);

			array_push($response, $json1);


			$json3 = array('success' => 1,
					'status'  => 1,
					'service' => '5',
	'img' => "".base_url()."assets/$ipath/resellers.png",
					'name' => 'Resellers',
	'message' => $cbalance,
	'notice' => $oldnotice,
	'level' => $level,
	'act' => 'myres'
					);

		array_push($response, $json3);

		$jsonxz = array('success' => 1,
					'status'  => 1,
					'service' => '1',
	'img' => "".base_url()."assets/$ipath/addbalance.png",
					'name' => 'Add Balance',
	'message' => $cbalance,
	'notice' => $oldnotice,
	'level' => $level,
	'act' => 'make'

					);
	array_push($response, $jsonxz);


		}

		if(empty($item)){




				$json1t = array('success' => 1,
					'status'  => 1,
					'service' => '1',
	'img' => "".base_url()."assets/$ipath/buycard.png",
					'name' => 'Bal Transfer',
	'message' => $cbalance,
	'notice' => $oldnotice,
	'level' => $level,
	'act' => 'transfer'

					);

			if($bal_transfer==1){
			array_push($response, $json1t);
                    }




}

		$sql="SELECT * from module where id>=0 and 2 <id and status='1' order by sorder asc";
		$query = $this->db->query($sql);
		$i=0;
		foreach ($query->result() as $row3){
		    $i++;

		    if($i==3 && empty($item)){


		       	$json2 = array('success' => 1,
					'status'  => 1,
					'service' => '3',
	'img' => "".base_url()."assets/$ipath/history_desh.png",
					'name' => 'History',
	'message' => $cbalance,
	'notice' => $oldnotice,
	'level' => $level,
	'act' => 'history'
					);

		array_push($response, $json2);


		    }


$serviceid=$row3->serviceid;
if(file_exists("assets/$ipath/$serviceid.png"))
{$img="".base_url()."assets/$ipath/$serviceid.png";}else{
$img="".base_url()."assets/$ipath/default.png";
}

if($row3->serviceid=='64'){
    $activity='recharge';
}


if($row3->serviceid=='32'){
    $activity='banktransfer';
}


if($row3->serviceid=='2'){
    $activity='sms';
}

if($row3->serviceid=='16384'){
    $activity='internet';
}

if($row3->serviceid=='524288' or $row3->serviceid=='8'){
    $activity='bank';
}

if($row3->serviceid=='128'){
    $activity='mobile';
}

if($row3->serviceid=='256'){
    $activity='mobile';
}

if($row3->serviceid=='1024'){
    $activity='mobile';
}

if($row3->serviceid=='2048'){
    $activity='mobile';
}

if($row3->serviceid=='4096'){
    $activity='mobile';
}
if($row3->serviceid=='32768'){
    $activity='mobile';
}

if($row3->serviceid=='131072'){
    $activity='mobile';
}

if($row3->serviceid=='1048576'){
    $activity='mobile';
}

if($row3->serviceid=='262144'){
    $activity='mobile';
}


		$portid=$row3->id;
		$titleop=$row3->title;

if($row3->serviceid=='128'){
    $titleop="B banking";
}
		$shortid=$row3->order;

		$serviceid%2;

		if (($client_typ[$portid] == $serviceid) && (($row3->serviceid!='16') && ($row3->serviceid!='512') && ($row3->serviceid!='65536') && ($row3->serviceid!='262144') && ($row3->serviceid!='524288'))) {
		$json_data = array('success' => 1,
					'status'  => 1,
					'service' => $serviceid,
	'img' => $img,
					'name' => $titleop,
	'message' => $cbalance,
	'notice' => $oldnotice,
	'level' => $level,
	'act' => $activity
					);
		array_push($response, $json_data); }


			if(empty($item) && $row3->serviceid=='64'){
	$json1t = array('success' => 1,
					'status'  => 1,
					'service' => '1',
	'img' => "".base_url()."assets/$ipath/packag.png",
					'name' => 'Drive pack',
	'message' => $cbalance,
	'notice' => $oldnotice,
	'level' => $level,
	'act' => 'drive'

					);

			array_push($response, $json1t);

			}

		}





	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Match.'
					);

	}
	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}
	header('Content-type: application/json');
	echo json_encode(array("bmtelbd"=>$response));

	}


	public function password() {

	if($_POST) {
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	$oldpass = $this->security->xss_clean($this->input->post('oldpass'));
	$newpass = $this->security->xss_clean($this->input->post('newpass'));
	$cnewpass = $this->security->xss_clean($this->input->post('cnewpass'));


	$userid = $this->db->get_where('apps_key',array('token' =>$apikey, 'deviceid' =>$deviceid))->row()->userid;

	$lastlogID = $this->mit->lastlogID($userid);

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;

	$chkpass = $this->db->get_where('reseller',array('id' =>$userid))->row()->password;
		if($userid=='208023'){$userid='';}
	if(!empty($userid)) {

	$chk= $this->mdb->verify($oldpass,$chkpass);
	if($chk==true) {

		if(!empty($newpass) && $newpass==$cnewpass){
		$this->mdb->update('reseller',array('password'=>$this->mdb->generate($newpass)),array('id'=>$userid));

		$response = array(
					'success' => 1,
					'status'  => 1,
					'message' => 'Password Change Successful'
					);
		}else {
			$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Confirm Password Not Match.'
					);
		}

		}else {
			$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Old Password Not Valid'
					);
		}
	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Match.'
					);
	}
	}else{
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}
	header('Content-type: application/json');
	echo json_encode($response);

	}



	public function pin() {

	if($_POST) {
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	$oldpass = $this->security->xss_clean($this->input->post('oldpin'));
	$newpass = $this->security->xss_clean($this->input->post('newpin'));
	$cnewpass = $this->security->xss_clean($this->input->post('cnewpin'));


	$userid = $this->db->get_where('apps_key',array('token' =>$apikey, 'deviceid' =>$deviceid))->row()->userid;

	$lastlogID = $this->mit->lastlogID($userid);

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;

	$chkpass = $this->db->get_where('reseller',array('id' =>$userid))->row()->pincode;

	if(!empty($userid)) {

	$chk= $this->mdb->verify($oldpass,$chkpass);
	if($chk==true) {

		if(!empty($newpass) && $newpass==$cnewpass){
		$this->mdb->update('reseller',array('pincode'=>$this->mdb->generate($newpass)),array('id'=>$userid));

		$response = array(
					'success' => 1,
					'status'  => 1,
					'message' => 'PIN Change Successful'
					);
		}else {
			$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Confirm PIN Not Match.'
					);
		}

		}else {
			$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Old PIN Not Valid'
					);
		}
	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Match.'
					);
	}
	}else{
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}
	header('Content-type: application/json');
	echo json_encode($response);

	}

	public function RechargeHistory() {


	if($_GET) {
	$apikey = $this->security->xss_clean($this->input->get('token'));
	$deviceid = $this->security->xss_clean($this->input->get('deviceid'));
		$uif = $this->security->xss_clean($this->input->get('uif'));
	$number = $this->security->xss_clean($this->input->get('number'));

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey, 'deviceid' =>$deviceid))->row()->userid;
	$lastlogID = $this->mit->lastlogID($userid);

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;

	$chkpass = $this->db->get_where('reseller',array('id' =>$userid))->row()->pincode;

	if(!empty($userid)) {

		$response = array();
			if(!empty($number)) {	$sql="SELECT * from sendflexi where (userid='$userid' or level='$userid' or level2='$userid' or level3='$userid' or level4='$userid' or level5='$userid') and phone='$number' order by id desc limit 200"; }else{
			if($uif==0) {
		$sql="SELECT * from sendflexi where (userid='$userid' or level='$userid' or level2='$userid' or level3='$userid' or level4='$userid' or level5='$userid') order by id desc limit 200";

			}else{	$sql="SELECT * from sendflexi where service='$uif' AND (userid='$userid' or level='$userid' or level2='$userid' or level3='$userid' or level4='$userid' or level5='$userid') order by id desc limit 200";
	}
	}
if($uif==11) {
	$sql="select * from pay_receive where sender='$userid' OR userid='$userid' ORDER BY id desc";
	}


$query = $this->db->query($sql);
		foreach ($query->result() as $user_data){
		    if($uif!=11) {
				$level1 = $this->db->get_where('reseller',array('id' =>$user_data->level))->row()->username;
					$lname1 = levelchk($this->db->get_where('reseller',array('id' =>$user_data->level))->row()->custype);



					$level2 = $this->db->get_where('reseller',array('id' =>$user_data->level2))->row()->username;
						$lname2 = levelchk($this->db->get_where('reseller',array('id' =>$user_data->level2))->row()->custype);





					$level3 = $this->db->get_where('reseller',array('id' =>$user_data->level3))->row()->username;
					$lname3 = levelchk($this->db->get_where('reseller',array('id' =>$user_data->level3))->row()->custype);


					$level4 = $this->db->get_where('reseller',array('id' =>$user_data->level4))->row()->username;
						$lname4 = levelchk($this->db->get_where('reseller',array('id' =>$user_data->level4))->row()->custype);



					$level5 = $this->db->get_where('reseller',array('id' =>$user_data->level5))->row()->username;
					$lname5 = levelchk($this->db->get_where('reseller',array('id' =>$user_data->leve5))->row()->custype);


					$level6 = $this->db->get_where('reseller',array('id' =>$user_data->userid))->row()->username;
					$lname6 = levelchk($this->db->get_where('reseller',array('id' =>$user_data->userid))->row()->custype);

				$sender="****";




			$flexistatus=$user_data->status;
			$flexilocal=$user_data->local;

			$type=$user_data->type;

			 $serv=$user_data->service;

			 $number=$user_data->phone;

			 $balance=$user_data->balance;
			 $cost=$user_data->cost;
			 $send_time_stamp=$user_data->send_time_stamp;
			   $actual_send_time=$user_data->actual_send_time;

			 $prebalance=$user_data->prebalance;
			 $sdate=$user_data->idate;

if(file_exists("assets/img/$serv.png"))
{$img="".base_url()."assets/img/$serv.png";}else{
$img="".base_url()."assets/img/default.png";
}


			 if($flexistatus==1 or $flexilocal==1) {$sst="Success";}

			 if ($flexistatus==0 && $flexilocal==0) {$sst="Pending";}
			 if($flexistatus==3) {$sst="Cancel";}
			  if($flexistatus==2) {$sst="Failed";}
			 if($flexistatus==4) {$sst="Processing";}
			 if($flexistatus==5) {$sst="Waiting";}
			 if($serv=='64' or $serv=='16' or  $serv=='512' or $serv=='16384') {
			 if($type==1) {$ot="Prepaid";} else {$ot="PostPaid";}
			}else {
			if($type==1) {$ot="Personal";} else {$ot="Agent";}
				  }

			$sertitle = $this->db->get_where('module',array('serviceid' =>$serv))->row()->title;

			 $dt= explode("-", $sdate);

			 $intmonth= $dt[1];
			 $datset = $dt[2];

			 if($intmonth==01){
			$month="Jan";
			}else if($intmonth=="02"){
			$month="Feb";
			}else if($intmonth=="03"){
			$month="Mar";
			}else if($intmonth=="04"){
			$month="Apr";
			}else if($intmonth=="05"){
			$month="May";
			}else if($intmonth=="06"){
			$month="Jun";
			}else if($intmonth=="07"){
			$month="July";
			}else if($intmonth=="08"){
			$month="Aug";
			}else if($intmonth=="09"){
			$month="Sep";
			}else if($intmonth=="10"){
			$month="Oct";
			}else if($intmonth=="11"){
			$month="Nov";
			}else{
			$month="Dec";
			}

  $query2 = $this->db->query("SELECT * from trans where send_sl='$user_data->id' and userid='$userid'");


$rowtrans = $query2->row();
//echo $row->name;
$lastaccountbalance=$rowtrans->accountbalance;
		$dtsss = $datset ." ".$month;

}
if($uif==11) {
$img="".base_url()."assets/img/3232332fff.png";

}


		if($uif==11) {
if($user_data->userid==$userid){
$type='Received';
$amou= $user_data->credit;
 if(is_numeric($user_data->sender)){
$payusername = $this->db->get_where('reseller',array('id' =>$user_data->sender))->row()->username;
}else{$payusername=$user_data->sender;
}

}else{
$type='Payment';
$amou= $user_data->debit;
$payusername = $this->db->get_where('reseller',array('id' =>$user_data->userid))->row()->username;

}

	$json_data = array(
        'number' => $payusername,
            'balance' => $amou,
            'cost' =>  $user_data->debit,
            'prebalance' => $user_data->account,
            'serviceName' => $type,
            'type' => $user_data->type,
            'status' => '',
            'trxID' => $user_data->desc,
            'time' => $user_data->date,
            'sender' => $sender ,
            'ut' => $user_data->date,
        'img' => $img,
   'debit' => $user_data->debit,
            'credit' => $user_data->credit,
 'img' => $img,
  'service' => '11',
   'pcode' => $user_data->pcode,
        );

}else{
	  $json_data = array(

           // 'username' => $uname,
            'number' => $user_data->phone,
            'balance' => $user_data->balance,
            'cost' => $user_data->cost,
            'prebalance' => $lastaccountbalance,
            'serviceName' => $sertitle,
            'type' => $ot,
            'status' => $sst,
            'trxID' => $user_data->trxid,
            'time' => $send_time_stamp,
            'sender' => $sender ,
            'ut' => $actual_send_time,
 'img' => $img,
  'service' => $serv,
   'pcode' => $user_data->pcode,

        );

}
        array_push($response, $json_data);
		}

}else {
	$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Match.'
					);

	}

	}else {
	$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);

	}

	header('Content-type: application/json');
			//$response=array("bmtelbd"=>$response);


	echo json_encode(array("bmtelbd"=>$response));


	}

	public function NewRequest()
	{

	if($_POST) {
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	$offerid = $this->security->xss_clean($this->input->post('orderid'));
	$appversion = $this->security->xss_clean($this->input->post('drive'));

	$serviceget = $this->security->xss_clean($this->input->post('service'));
	$number = $this->security->xss_clean($this->input->post('number'));
		$unumber = $this->security->xss_clean($this->input->post('unumber'));
			$name = $this->security->xss_clean($this->input->post('name'));
	$type = $this->security->xss_clean($this->input->post('type'));
	$amount = $this->security->xss_clean($this->input->post('amount'));
		$name = "$unumber $name";
		if($serviceget=='524288'){ $number="10$number";
$type ='1';


}else{
		    $number="$number";
		}

	$pincode = $this->security->xss_clean($this->input->post('pincode'));

	$optn = $this->security->xss_clean($this->input->post('optn'));
	$optn3=$optn;

 if($optn3=="Grameenphone"){
		$optn="GP";
		}

		if($optn3=="Robi"){
		$optn="RB";
		}

		if($optn3=="Banglalink"){
		$optn="BL";
		}
		if($optn3=="Airtel"){
		$optn="AT";
		}
		if($optn3=="Teletalk"){
		$optn="TT";
		}
		if($optn3=="Skitto"){
		$optn="SK";
		}

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey, 'deviceid' =>$deviceid))->row()->userid;

	$lastlogID = $this->mit->lastlogID($userid);

	$uid=$userid;

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;

	$chkpass = $this->db->get_where('reseller',array('id' =>$userid))->row()->pincode;
	$parent = $this->db->get_where('reseller',array('id' =>$userid))->row()->p_id;
	$prebalance = $this->db->get_where('reseller',array('id' =>$userid))->row()->balance;

	$mybalance = $this->db->get_where('reseller',array('id' =>$userid))->row()->balance;
	$balancelimit = $this->db->get_where('reseller',array('id' =>$userid))->row()->balance_limit;

	$appsAccess = $this->db->get_where('reseller',array('id' =>$userid))->row()->appsAccess;
	$userstatus = $this->db->get_where('reseller',array('id' =>$userid))->row()->status;

	$mysafe_status = $this->db->get_where('device_list',array('userid' =>$userid, 'tokenid' =>$deviceid))->row()->status;

	if(!empty($userid)) {

	if($mysafe_status=1 ) {

	if($appsAccess==1) {

	if($userstatus==1) {


	$this->irs->recharge($serviceget,$number,$amount,$type,$uid,$pincode,'yes',$country,$opid,'apps',$name,$nidcode,$senderno,$optn,$appversion,$offerid);
		$getirdata= $this->session->userdata('irslog');
		$irstsview = $getirdata['irsts'];
		$irsmessage = $getirdata['irsmessage'];

		if($irstsview=="error") {
			$response = array(
		'success' => 0,
		'status'  => 2,
		'message' => $irsmessage
		);
		}else {
		$response = array(
		'success' => 1,
		'status'  => 1,
		'message' => $irsmessage
		);

		}

		} //block
		else {
			$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Your Account is Block'
					);
		}

		} //block
		else {
			$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Your Apps Access is Block'
					);
		}

		} //device id block
		else {
			$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Your Device ID is Block'
					);
		}


		} //empty
		else {
			$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Match'
					);
		}


		} // post
		else {

			$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty not work'
					);


		}

		header('Content-type: application/json');
		echo json_encode($response);

	}

	// reseller list here
	public function getinternet() {

		$apikey = $this->security->xss_clean($this->input->get('token'));
	$deviceid = $this->security->xss_clean($this->input->get('deviceid'));
		$text = $this->security->xss_clean($this->input->get('text'));
	$uid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;

$response = array();
$mobile = $this->security->xss_clean($this->input->get('ot'));
	$pcode = $this->security->xss_clean($this->input->get('ot'));

	$firstThreeDigit=substr($mobile,0,3);

$firstThreeDigit=substr($mobile,0,3);
	if(!is_numeric($pcode)){

			    if($pcode=="GP"){
		$firstThreeDigit="017";
		}

		if($pcode=="RB"){
		$firstThreeDigit="018";
		}

		if($pcode=="BL"){
		$firstThreeDigit="019";
		}


		if($pcode=="AT"){
		$firstThreeDigit="016";
		}
		if($pcode=="TT"){
		$firstThreeDigit="015";
		}

			if($pcode=="SK"){
		$firstThreeDigit="113";
		}
		}

		$sql="SELECT * from net_op where prefix='$firstThreeDigit'";

   		$query = $this->db->query($sql);
		foreach ($query->result() as $row){

   		$opid=$row->id;
	 		$opname=$row->opname;
		$id = $opid;
		}

if(!empty($text)){
$sql="SELECT * from net_package where op='$id' and status='1' and(`pk_name` LIKE '%$text%' or `price` LIKE '%$text%') order by id desc";
}else{
    $sql="SELECT * from net_package where op='$id' and status='1' order by id desc";

}
//$sql="SELECT * from net_package where op='$id' and status='1' order by id asc";

    $query = $this->db->query($sql);
foreach ($query->result() as $row){

       $netid=$row->id;

    $titleoppck=$row->pk_name;

    $netprice=$row->price;

    $netcharge=$row->charge;
    $netcomm=$row->comm;
    $exp=$row->exp;



$reguler=$row->price-$row->comm;

$json_data = array( 'reg' => $reguler,
					'title' => $row->pk_name,
						'price' => $row->price,
					'id' => $row->id,
					'com' => $row->comm,

					'exp' => $row->exp, 	'opname' => $opname
				);

array_push($response, $json_data);


}

header('Content-type: application/json');
	echo json_encode(array("bmtelbd"=>$response));



}


	public function getdrive() {

		$apikey = $this->security->xss_clean($this->input->get('token'));
	$deviceid = $this->security->xss_clean($this->input->get('deviceid'));
		$text = $this->security->xss_clean($this->input->get('text'));
	$uid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;

$response = array();
$mobile = $this->security->xss_clean($this->input->get('ot'));
	$pcode = $this->security->xss_clean($this->input->get('ot'));

	$firstThreeDigit=substr($mobile,0,3);

$firstThreeDigit=substr($mobile,0,3);
	if(!is_numeric($pcode)){

			    if($pcode=="GP"){
		$firstThreeDigit="017";
		}

		if($pcode=="RB"){
		$firstThreeDigit="018";
		}

		if($pcode=="BL"){
		$firstThreeDigit="019";
		}


		if($pcode=="AT"){
		$firstThreeDigit="016";
		}
		if($pcode=="TT"){
		$firstThreeDigit="015";
		}

			if($pcode=="SK"){
		$firstThreeDigit="113";
		}
		}

		$sql="SELECT * from net_op where prefix='$firstThreeDigit'";

   		$query = $this->db->query($sql);
		foreach ($query->result() as $row){

   		$opid=$row->id;
	 		$opname=$row->opname;
		$id = $opid;
		}

if(!empty($text)){
$sql="SELECT * from drive_package where op='$id' and status='1' and(`pk_name` LIKE '%$text%' or `price` LIKE '%$text%') order by id desc";
}else{
    $sql="SELECT * from drive_package where op='$id' and status='1' order by id desc";

}
    $query = $this->db->query($sql);
foreach ($query->result() as $row){

       $netid=$row->id;

    $titleoppck=$row->pk_name;

    $netprice=$row->price;

    $netcharge=$row->charge;
    $netcomm=$row->comm;
    $exp=$row->exp;


	$reguler=$row->price-$row->comm;

$json_data = array( 'reg' => $reguler,
					'title' => $row->pk_name,
						'price' => $row->price,
					'id' => $row->id,
					'com' => $row->comm,

					'exp' => $row->exp, 	'opname' => $opname
				);

array_push($response, $json_data);


}

header('Content-type: application/json');
	echo json_encode(array("bmtelbd"=>$response));



}





	public function reseller() {


	if($_GET) {
	$apikey = $this->security->xss_clean($this->input->get('token'));
	$deviceid = $this->security->xss_clean($this->input->get('deviceid'));
		$number = $this->security->xss_clean($this->input->get('number'));
$self = $this->security->xss_clean($this->input->get('self'));

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;
	$lastlogID = $this->mit->lastlogID($userid);

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;

	$chkpass = $this->db->get_where('reseller',array('id' =>$userid))->row()->pincode;

	if(!empty($userid)) {


		$response = array();

		$lr=$this->mit->leftRight($userid);
		$lft=$lr[0];
		$rgt=$lr[1];

		if(!empty($self)){

		    $sql="select * from reseller where id='$userid' and status!=2 order by id desc";

		}else{
			if(!empty($number)) {	$sql="select * from reseller where p_id='$userid' and status!=2 and username='$number' order by id desc";

			}else{	$sql="select * from reseller where p_id='$userid' and status!=2 order by id desc";}
		}
		$query = $this->db->query($sql);
		foreach ($query->result() as $user_data){

			$reselerstatus=$user_data->status;
			$flexilocal=$user_data->local;

			$type=$user_data->type;

			$serv=$user_data->service;

			$p_id=$user_data->p_id;

			 $balance=$user_data->balance;
			 $cost=$user_data->cost;
			 $send_time_stamp=$user_data->send_time_stamp;

			 $prebalance=$user_data->prebalance;
			 $sdate=$user_data->idate;



			 if($reselerstatus==1) {$reslsts="active";} else {$ot="lock";}


			$prantname = $this->db->get_where('reseller',array('id' =>$p_id))->row()->username;

	$memberlevel=$user_data->custype;

		$newlevel = $this->db->get_where('level_list',array('name' =>$memberlevel))->row()->real_name;

			if(empty($newlevel)){
	if($memberlevel=='reseller5'){$newlevel='HOUSE';}
				else if($memberlevel=='reseller4'){$newlevel='DGM';}
					else if($memberlevel=='reseller3'){$newlevel='Dealer';}
				else if($memberlevel=='reseller2'){$newlevel='Seller';}
				else if($memberlevel=='reseller1'){$newlevel='Retailer';}else if($memberlevel=='subadmin'){$newlevel='subadmin';}
			}

			$json_data = array(
					'username' => $user_data->username,
						'email' => $user_data->email,
					'tel' => $user_data->mobile,
					'id' => $user_data->id,
					'name' => $user_data->name,
				'bbalance' => $user_data->bank_balance,
					'dbalance' => $user_data->drive_bal,
					'balance' => $user_data->balance,
					'parent' => $prantname,
					'lastlogin' => $user_data->last_login,
					'resellerstatus' => $reslsts,
					'type' => $newlevel,
					'createdate' => $user_data->create_date,
						'nid' => $user_data->nid,
					'birth' => $user_data->birth,
				);
			array_push($response, $json_data);
			}

	}else {
	$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Match.'
					);

	}

	}else {
	$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);

	}

	header('Content-type: application/json');
	echo json_encode(array("bmtelbd"=>$response));


	}


	/// reseller list finish
	public function reselleradd() {

	$var=$this->input->post();

	if($_POST) {

	$apikey = $this->security->xss_clean($this->input->post('token', true));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid', true));


	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;

	$postlevel= $this->security->xss_clean($this->input->post('level', true));

	$lastlogID = $this->mit->lastlogID($userid);


	if(!empty($userid)) {


	$chk_data=$this->mdb->getData('reseller',array('id'=>$userid));
				$sendername=$chk_data[0]['username'];
				$uparent=$chk_data[0]['p_id'];
				$ctype=$chk_data[0]['custype'];
				$myLeft=$chk_data[0]['lft'];
				$myRight=$chk_data[0]['rgt'];
      $mytype=$chk_data[0]['type'];
				$uid=$userid;
				$ressoff = $chk_data[0]['reseller'];
	$cbalance=$chk_data[0]['balance'];
	$balancelimit=$chk_data[0]['balance_limit'];


		$availablebal=$cbalance-$balancelimit;


      $postlevel=strtolower($postlevel);

      $custype = $this->db->get_where('level_list',array('real_name' =>$this->security->xss_clean($this->input->post('level', true))))->row()->name;


      if(empty($custype)){
		if($postlevel=='house'){$custype='reseller5';}
				else if($postlevel=='dgm'){$custype='reseller4';}
					else if($postlevel=='dealer'){$custype='reseller3';}
				else if($postlevel=='seller'){$custype='reseller2';}
				else if($postlevel=='retailer'){$custype='reseller1';}else{


			$custype = $this->db->get_where('level_list',array('real_name' =>$this->security->xss_clean($this->input->post('level', true))))->row()->name;


				}
      }


			$mylevel=substr($ctype, -1);
if($ctype=="subadmin" or $ctype=="Subadmin"){$mylevel=6;}
$postlevel=substr($custype, -1);

			if($mylevel>$postlevel){$custype="reseller$postlevel";}else{
		if($ctype=="subadmin"){$custype="reseller5";}
		if($ctype=="reseller5"){$custype="reseller4";}
		if($ctype=="reseller4"){$custype="reseller3";}
		if($ctype=="reseller3"){$custype="reseller2";}
		if($ctype=="reseller2"){$custype="reseller1";}
			}




			  $sql_rate="SELECT * from level_list where name='$custype'";
		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{
				$amount=$raterow->account;

}
	$idate=date('Y-m-d');
	$create_date=date('j F Y g:i A');
	$ip = $_SERVER['REMOTE_ADDR'];

	// parent

	$parent2 = $this->db->get_where('reseller',array('id' =>$uid))->row()->p_id;
	$parent3 = $this->db->get_where('reseller',array('id' =>$parent2))->row()->p_id;
	$parent4 = $this->db->get_where('reseller',array('id' =>$parent3))->row()->p_id;
	$parent5 = $this->db->get_where('reseller',array('id' =>$parent4))->row()->p_id;

	// parent


	$username = $this->security->xss_clean($this->input->post('username', true));

	$name = $this->security->xss_clean($this->input->post('name', true));

$mobile = $this->security->xss_clean($this->input->post('phone', true));
$email = $this->security->xss_clean($this->input->post('email', true));

	$password = $this->security->xss_clean($this->input->post('password', true));
	$pin = $this->security->xss_clean($this->input->post('resellerpin', true));
	$pincode = $this->security->xss_clean($this->input->post('pincode', true));

	$client_types = $this->security->xss_clean($this->input->post('client_types', true));
			$nid = $this->security->xss_clean($this->input->post('nid', true));
		$birth = $this->security->xss_clean($this->input->post('birth', true));

	$client_typess = $mytype;

	$this->form_validation->set_rules('username', 'Username', 'is_unique[reseller.username]|trim|required|xss_clean|min_length[5]');
		$this->form_validation->set_rules('password', 'Password', 'trim|required|xss_clean|min_length[6]');
	$this->form_validation->set_rules('resellerpin', 'PIN', 'trim|required|xss_clean|min_length[4]');

	$this->form_validation->set_rules('pincode', 'Salf PIN', 'trim|required|xss_clean|min_length[4]');

	//$this->form_validation->set_rules('client_types[]', 'Reseller Permission', 'trim|required');

	if ($this->form_validation->run() == FALSE) {

		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Valid.'
					);


	}else {
	   	if($ressoff=='1' && $ctype!="reseller1"){
	    if($availablebal>=$amount){

	$chtype = 'user';
	//$chking= $this->mit->otpchk($nwpass,$chtype);
	$chking=4;


	if($chking) {


			$client_types = 0;

		if (!empty($mytype)):
		foreach ($mytype as $client_types):
        $add_type_values+= $client_types;
		endforeach;
		endif;

		$userpl=strlen($username);
		$pass=strlen($password);

		$q2="UPDATE reseller SET rgt = rgt + 2 WHERE rgt > $myLeft";
		$this->db->query($q2);

		$q3="UPDATE reseller SET lft = lft + 2 WHERE lft > $myLeft";
		$this->db->query($q3);


		$left=$myLeft +1;
		$right=$myLeft + 2;
		$hash = $this->mdb->generate($password);
		$hashpin = $this->mdb->generate($pin);

		$rlastids = $this->mit->lastUserID();
		 $rlastid = $rlastids+1;

		$sqltarif="INSERT INTO `tarif` (`id`, `make_by`, `name`, `desc`, `userid`, `duble`, `date`, `time`) VALUES (NULL, '$uid', '$username Tarif', '$add_type_values', '$rlastid', '$duble', '$idate', '$create_date');";

		$this->db->query($sqltarif);

		$tarifidlast = $this->db->insert_id();
		$otp = 0;

		$sql="INSERT INTO `reseller` (`id`, `username`, `password`, `pincode`, `type`, `name`, `mobile`, `email`, `note`, `custype`, `p_id`, `admin_id`, `lft`, `rgt`, `balance`, `balance_limit`, `create_date`, `ip`, `enbale_otp`, `tarif`, `oversale`, `status`, `level5`, `level4`, `level3`, `level2`, `level1`, `webAccess`, `appsAccess`,`new_account_price`,`nid`,`birth`) VALUES (NULL, '$username', '$hash', '$hashpin', '$client_typess', '$name', '$mobile', '$email', '', '$custype', '$uid', '$admin_id', '$left','$right', '', '', '$create_date', '$ip', '$otp', '$tarifidlast', '0', '1', '$uid', '$parent2', '$parent3', '$parent4', '$parent5', '1', '1','$amount','$nid','$birth');";

		$this->db->query($sql);
		$idlast = $this->db->insert_id();

		$msg= 'Add Reseller '.$username.' With Apps';
		$this->mit->InsertActivetUser($uid,$msg);


			$prebalance= $this->mit->accountBalance($uid);

	$tm="minus";
		$this->mit->balanceUpdate($uid, $amount,$tm);
		$bal= $this->mit->accountBalance($uid);

		  $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `date`, `time`) VALUES (NULL, '$uid', 'new account for $username', '$prebalance', '$amount', '', '$bal', 'minus', '$idate', '$create_date')";
		  $this->db->query($sql_tr);

		$sql="INSERT INTO `pay_receive` (`id`, `userid`, `desc`, `remark`, `sender`, `debit`, `account`, `type`, `transfer_type`, `idate`, `date`) VALUES (NULL, '$idlast', 'new account for $username', 'By $username', '$uid', '$amount', '$bal', 'new_account', 'recharge', '$idate', '$create_date')
		";
		$this->db->query($sql);
       $company = $this->db->get_where('company',array('id' =>'1'))->row()->company_name;


			$sm="Dear $name Your account has been created sucessfully on $company username: $username Pin: $pin password: $password";
			if(is_numeric($username)){

		$re=sends("flexiload","SMS*$username*$sm*DD");
            }
      if(is_numeric($sendername)){
		$re=sends("flexiload","SMS*$sendername*$sm*DD");
			}
		$response = array(

					'success' => 1,
					'status'  => 1,
					'message' => ' Reseller Added Successfully.'
					);

	}else {
		$response = array(
					'success' => 1,
					'status'  => 1,
					'message' => ' Reseller Added Successfully.'
					);

	}

	   	}else{$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Need Balance '.$amount.' for new account.'
					);}
	}else{	$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Your have no permission.'
					);}


	}
	}else {

		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Your Not Valid.'
					);

	}
	}else {

		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);

	}



	header('Content-type: application/json');
	echo json_encode($response);
	}

	public function paymentlist(){




	if($_POST) {

	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;


	if(!empty($userid)) {

	$sql="select * from pay_receive where sender='$userid' ORDER BY id desc";


	$query2 = $this->db->query($sql);

	$response = array();

	foreach($query2->result() as $user_data) {

		$payuser = $user_data->userid;

		$payusername = $this->db->get_where('reseller',array('id' =>$payuser))->row()->username;

		$jsondata = array(
            'date' => $user_data->date,
			'member' => $payusername,
            'type' => $user_data->type,
			'desc' => $user_data->desc,
            'debit' => $user_data->debit,
            'credit' => $user_data->credit,
            'balance' => $user_data->account
        );
        array_push($response, $jsondata);

	}

	}else {

		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Your Not Valid.'
					);

	}

	}else {

			$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Allow.'
					);

	}

	header('Content-type: application/json');
	echo json_encode($response);

	}



	public function receivelist(){




	if($_POST) {

	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;

	if(!empty($userid)) {

	$sql="select * from pay_receive where userid='$userid' ORDER BY id desc";


	$query2 = $this->db->query($sql);

	$response = array();

	foreach($query2->result() as $user_data) {



		$jsondata = array(
            'date' => $user_data->date,
            'type' => $user_data->type,
			'desc' => $user_data->desc,
            'debit' => $user_data->debit,
            'credit' => $user_data->credit,
            'balance' => $user_data->account
        );
        array_push($response, $jsondata);

	}

	}else {

		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Your Not Valid.'
					);

	}

	}else {

			$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Allow.'
					);

	}

	header('Content-type: application/json');
	echo json_encode($response);

	}

	public function reports(){

	$dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
	$create_date=$dt->format('Y-m-d H:i:s');
	$idate = $dt->format('Y-m-d');

	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));

	$date = $this->security->xss_clean($this->input->post('date'));

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;

	if(!empty($userid)) {

	$response = array();

	$sql="SELECT * from module where status=1 order by sorder asc";
    $query = $this->db->query($sql);
    foreach ($query->result() as $row)
          {
    $portid=$row->id;
    $title_op=$row->title;
    $serviceidoo=$row->serviceid;

	$query1 = "SELECT SUM(balance) as num FROM sendflexi WHERE service='$serviceidoo' and idate='$idate' and status='1' and (userid='$userid' or level='$userid' or level2='$userid' or level3='$userid' or level4='$userid' or level5='$userid') ";

	$query = $this->db->query($query1);
	   foreach ($query->result() as $total_pages1){
	  $Flexiload = $total_pages1->num;
	}

	$from1=$idate;
	$to1=$idate;
	$fuu_report=$this->mit->reportDetails($serviceidoo,$uid,$from1,$to1);
	if($Flexiload>0) {

	  echo "<tr><td>$title_op</td><td class='tk'>$Flexiload</td> <td class='tk'> $fuu_report</td></tr>";

	  $jsondata = array(
            'service' => $title_op,
			'serviceID' => $serviceidoo,
            'amount' => $Flexiload,
			'cost' => $fuu_report
        );
        array_push($response, $jsondata);


	  }

	} /// service lsit finish

	}else {

		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Your Not Valid.'
					);

	}

	header('Content-type: application/json');
	echo json_encode($response);

	}

	public function myrate(){




	if($_GET) {

	$apikey = $this->security->xss_clean($this->input->get('token'));
	$deviceid = $this->security->xss_clean($this->input->get('deviceid'));

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;

	if(!empty($userid)) {

	$myacc_tarf = $this->db->get_where('reseller',array('id' =>$userid))->row()->tarif;


	$sql="select * from rate_module where tarif='$myacc_tarf' ORDER BY id desc";


	$query2 = $this->db->query($sql);

	$response = array();

	foreach($query2->result() as $raterow) {

		$opname=$raterow->service;
		$prefix=$raterow->prefix;
		$rate=$raterow->rate;

		$comm=$raterow->comm;
		$type=$raterow->type;
		$ratsts=$raterow->status;


	$titleop = $this->db->get_where('module',array('serviceid' =>$opname))->row()->title;

	if($ratsts==1) {$rts="Active";} else {$rts="Deactive";}

	if($opname=='64') {
	if($type==1) {$ot="Prepaid";} else if ($type==2) {$ot="PostPaid";} else {$ot="None";}

	}else {
	if($type==1) {$ot="CashIn";} else if ($type==2) {$ot="CashOut";} else {$ot="None";}
	}

	$status_mud = $this->db->get_where('module',array('serviceid' =>$opname))->row()->status;


    if($status_mud==1) {

		$jsondata = array(
            'service' => $titleop,
            'pcode' => $raterow->pcode,
			'prefix' => $raterow->prefix,
			'rate' => $raterow->rate,
            'comm' => $raterow->comm,
            'charge' => $raterow->charge,
            'status' => $rts
        );
        array_push($response, $jsondata);

	}
	}

	}else {

		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Your Not Valid.'
					);

	}

	}else {

			$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Allow.'
					);

	}

	header('Content-type: application/json');
	echo json_encode(array("bmtelbd"=>$response));



	}


	public function myResellerRates(){

	if($_POST) {

	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	$resid = $this->security->xss_clean($this->input->post('resellerid'));

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;

	if(!empty($userid)) {

	$myacc_tarf = $this->db->get_where('reseller',array('id' =>$resid))->row()->tarif;


	$sql="select * from rate_module where tarif='$myacc_tarf' ORDER BY id desc";


	$query2 = $this->db->query($sql);

	$response = array();

	foreach($query2->result() as $raterow) {

		$opname=$raterow->service;
		$prefix=$raterow->prefix;
		$rate=$raterow->rate;

		$comm=$raterow->comm;
		$type=$raterow->type;
		$ratsts=$raterow->status;


	$titleop = $this->db->get_where('module',array('serviceid' =>$opname))->row()->title;

	if($ratsts==1) {$rts="Active";} else {$rts="Deactive";}

	if($opname=='64') {
	if($type==1) {$ot="Prepaid";} else if ($type==2) {$ot="PostPaid";} else {$ot="None";}

	}else {
	if($type==1) {$ot="CashIn";} else if ($type==2) {$ot="CashOut";} else {$ot="None";}
	}

	$status_mud = $this->db->get_where('module',array('serviceid' =>$opname))->row()->status;


    if($status_mud==1) {

		$jsondata = array(
            'service' => $titleop,
            'pcode' => $raterow->pcode,
			'prefix' => $raterow->prefix,
			'rate' => $raterow->rate,
            'comm' => $raterow->comm,
            'charge' => $raterow->charge,
            'status' => $rts
        );
        array_push($response, $jsondata);

	}
	}

	}else {

		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Your Not Valid.'
					);

	}

	}else {

			$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Allow.'
					);

	}

	header('Content-type: application/json');
	echo json_encode($response);

	}

	public function netSelect(){

		$varget=$this->input->get();

		$number= $varget['number'];

		$mobile=trim($number);

		$firstThreeDigit=substr($mobile,0,3);

		$sql="SELECT * from net_op where prefix='$firstThreeDigit' and status='1'";

   		$query = $this->db->query($sql);
		foreach ($query->result() as $row){

   		$opid=$row->id;

		$id = $opid;
		}

		$response = array();
		$sql="SELECT * from net_package where op='$id' and status='1' order by id asc";

		$query = $this->db->query($sql);
		foreach ($query->result() as $row){

		$jsondata = array(
            'id' => $row->id,
            'name' => $row->pk_name,
			'price' => $row->price
        );

		 array_push($response, $jsondata);

		}

		header('Content-type: application/json');
		echo json_encode($response);

	}

	public function countryList(){


		$response = array();
		$sql="SELECT * FROM `country` order by id asc";

		$query = $this->db->query($sql);
		foreach ($query->result() as $row){

		$jsondata = array(
            'id' => $row->id,
            'countryname' => $row->country_name,
        );

		 array_push($response, $jsondata);

		}


	header('Content-type: application/json');
		echo json_encode(array("bmtelbd"=>$response));

	}

	public function oparetorList(){

		$var=$this->input->get();



		$response = array();

	$three = $this->security->xss_clean($this->input->get('three'));

		if(!empty($three)){


		$three=substr($three,0,3);
$tc=$three;
if($tc=='013')$three='017';

if($tc=='014')$three='019';
			$sql="SELECT * FROM `oparetor` where `pcode` ='$three' order by id asc LIMIT 1";
}else{
		$sql="SELECT * FROM `oparetor` where `pcode` is not null order by id asc";
   }
		$query = $this->db->query($sql);
		foreach ($query->result() as $row){
		 $firstThreeDigit=$row->pcode;
		if($firstThreeDigit=="017"){
		$optn="GP";
		}if($firstThreeDigit=="013"){
		$optn="GP";
		}if($firstThreeDigit=="019"){
		$optn="BL";
		}if($firstThreeDigit=="014"){
		$optn="BL";
		}if($firstThreeDigit=="018"){
		$optn="RB";
		}if($firstThreeDigit=="016"){
		$optn="AT";
		}if($firstThreeDigit=="015"){
		$optn="TT";
		}

if($firstThreeDigit=="113"){
		$optn="SK";
		}
		$im=strtolower($optn);

if(file_exists("assets/img/$im.png"))
{$img="".base_url()."assets/img/$im.png";}else{
$img="".base_url()."assets/img/default.png";
}

 if($im=='gp'){
$opname='GP';
}else{
$opname=$row->title_op;
}

		$jsondata = array(
            'id' => $row->id,
            'opname' => $opname,
			'pcode' => $optn,
'img' => $img,
        );

		 array_push($response, $jsondata);

		}


	header('Content-type: application/json');
		echo json_encode(array("bmtelbd"=>$response));

	}

	public function billpayProvider(){


		$response = array();
		$sql="SELECT * FROM `billing_set` order by id desc";

		$query = $this->db->query($sql);
		foreach ($query->result() as $row){

		$jsondata = array(
            'id' => $row->id,
            'name' => $row->name,
			'code' => $row->bill_code,
        );

		 array_push($response, $jsondata);

		}



header('Content-type: application/json');
		echo json_encode(array("bmtelbd"=>$response));

	}




public function payment() {
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
		$item = $this->security->xss_clean($this->input->post('item'));
$source = $this->security->xss_clean($this->input->post('source'));

	$uid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;
	$bal_transfer = $this->db->get_where('security_option',array('id' =>1))->row()->bal_transfer;


	$uparent = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->p_id;

		$idate=date('Y-m-d');
		$create_date=date('j F Y g:i A');
		$ip = $_SERVER['REMOTE_ADDR'];


		if($this->input->post()){

		    	$apiallow = $this->db->get_where('reseller',array('id' =>$uid))->row()->api;


		    	$username = $this->db->get_where('reseller',array('id' =>$uid))->row()->username;

		if(!empty($uid)) {
		    	$name = $this->db->get_where('reseller',array('username' =>$username))->row()->name;

   	 	$var=$this->input->post();
		$description  = 'App';
		$amount       = $var['amount'];
		$type         = $var['type'];

		$ucin         = $var['ucid'];

		if($item=='transfer'){
		    $ucin = $this->db->get_where('reseller',array('username' =>$ucin))->row()->id;
		}


			$chk_datau=$this->mdb->getData('reseller',array('id'=>$ucin));
		$ucid=$chk_datau[0]['id'];

		if(!empty($ucid)){

		$chk_data=$this->mdb->getData('reseller',array('id'=>$uid));
				$chkpass=$chk_data[0]['pincode'];
				$oversale=$chk_data[0]['oversale'];

          $cbalance=$this->mit->accountBalance($uid,$source);



	$balancelimit=$chk_data[0]['balance_limit'];


		$availablebal=$cbalance-$balancelimit;

		$nwpass = $var['pincode'];
		$optuser = 'user';
		$chking= $this->mit->otpchk($nwpass,$optuser);


		if($type=="Transfer" && $bal_transfer==1) {




				if($availablebal>=$amount && is_numeric($amount) && $amount > 0){

		$prebalance= $this->mit->accountBalance($ucid,$source);
		$t="plus";
			$tm="minus";


		$this->mit->balanceUpdate($uid, $amount,$tm,$source);

		$this->mit->balanceUpdate($ucid, $amount,$t,$source);

		$bal=$this->mit->accountBalance($ucid,$source);
		$getuser = $this->db->get_where('reseller',array('id' =>$ucid))->row()->username;

		 $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `date`, `time`) VALUES (NULL, '$ucid', 'Deposit By $sendername', '$prebalance', '0', '$amount', '$bal', 'plus', '$idate', '$create_date');";

		$this->db->query($sql_tr);


	$balm=$this->mit->accountBalance($uid,$source);
		$sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `date`, `time`) VALUES (NULL, '$uid', 'Transfer to $sendername', '$availablebal', '$amount', '0', '$balm', 'Minus', '$idate', '$create_date');";

		$this->db->query($sql_tr);



		$sql="INSERT INTO `pay_receive` (`id`, `userid`, `desc`, `remark`, `sender`, `credit`, `account`, `type`, `transfer_type`, `idate`, `date`) VALUES (NULL, '$ucid', '$description', 'By $sendername', '$uid', '$amount', '$bal', '$type', 'recharge', '$idate', '$create_date');";

		$this->db->query($sql);


		$actsql ="INSERT INTO `activity_log` (`id`, `userid`, `p_id`, `secret`, `session`, `time`, `msg`, `ip`, `browser`, `os`, `date`) VALUES (NULL, '$uid', '$uparent', '', '0', '$create_date', 'Payment Add by reseller $getuser', '$ip', '', '', '$idate')";

		$this->db->query($actsql);
			$resellri = $this->db->get_where('reseller',array('id' =>$ucid))->row()->username;
			$robi=substr($resellri, 0, 2);
if($robi=='88'){
			$getuser = $this->db->get_where('reseller',array('id' =>$ucid))->row()->name;
			$getusers = $this->db->get_where('reseller',array('id' =>$uid))->row()->name;

		$sm="$getusers Dear $getuser Your balance update by $amount new Balance $bal";
		$re=sends("flexiload","SMS*$resellri*$sm*DD");
}

			$response = array(
				'success' => 1,
					'status'  => 1,
					'message' => ''.$name.' Payment added successfuly.'
					,'mobile'  => $ucin);
				}else{	$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => ''.$name.' Payment transfer Out Of Balance.'
					);}

			}else{

		   $response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Balance Transfer off.'
					);


		}


		}else{

		   $response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'User not found.'
					);


		}





		}	else{$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'invild login.'
					);}
		}else{	$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Allow.'
					);}


	header('Content-type: application/json');
	echo json_encode($response);
	  }








	public function resellerEdit(){
	$id = $this->security->xss_clean($this->input->post('id'));

		$decideid =  $id;
		$dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
		$sucdate=$dt->format('j F Y g:i A');
		$idate=$dt->format('Y-m-d');
		$var=$this->input->post();
		$varget=$this->input->get();
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));

	$mid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;



		$username = $this->db->get_where('reseller',array('id' =>$decideid))->row()->username;
		$pid = $this->db->get_where('reseller',array('id' =>$decideid))->row()->p_id;
		$ctype = $this->db->get_where('reseller',array('id' =>$decideid))->row()->custype;

		 $parent2 = $this->db->get_where('reseller',array('id' =>$decideid))->row()->p_id;
		 $parent3 = $this->db->get_where('reseller',array('id' =>$parent2))->row()->p_id;
		 $parent4 = $this->db->get_where('reseller',array('id' =>$parent3))->row()->p_id;
		 $parent5 = $this->db->get_where('reseller',array('id' =>$parent4))->row()->p_id;

		if(($pid==$mid) or ($var['self']=='yes')) {

		if($ctype=="subadmin"){$level="subadmin";}
		if($ctype=="reseller5"){$level="5";}
		if($ctype=="reseller4"){$level="4";}
		if($ctype=="reseller3"){$level="3";}
		if($ctype=="reseller2"){$level="2";}
		if($ctype=="reseller1"){$level="1";}

		if($_POST) {
	if(!empty($var['pass'])){
		$this->form_validation->set_rules('pass', 'Password', 'trim|required|xss_clean|min_length[6]');

	}
	if(!empty($var['pin'])){

		$this->form_validation->set_rules('pin', 'PIN', 'trim|required|xss_clean|min_length[4]');

}
	if ($this->form_validation->run() == FALSE) {
		if(empty($var['pin']) && empty($var['pass'])){

$ok="ok";}else{
	$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Valid.'
					);

}	}else{$ok="ok";}

	if($ok=="ok"){
		$name=$var['name'];
		$mobile=$var['mobile'];
		$pass=$this->mdb->generate($var['pass']);
		$email=$var['email'];
		$nid=$var['nid'];
		$birth=$var['birth'];
		$pinc=$this->mdb->generate($var['pin']);

		//$pin = $var['pincode'];

		$typ = 'user';

		$otpchking = 1;

		if($otpchking) {



		$query="UPDATE `reseller` SET `name` = '$name',
		`mobile` = '$mobile',
		`email` = '$email',`nid` = '$nid',`birth` = '$birth' WHERE `reseller`.`id` ='$decideid'";


		$query2 = $this->db->query($query);

		$user=$this->session->userdata('member_session');
		$uids= $user->id;
		$msg= 'Edit Reseller '.$username;
		$this->mit->InsertActivetUser($uids,$msg);

		$response = array(
					'success' => 1,
					'status'  => 2,
					'message' => 'Update Successful'
					);
		}else {
	$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Salf PIN Wrong'
					);
		}

		}




		}
	}else {

	$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Allow.'
					);
	}

	header('Content-type: application/json');
	echo json_encode($response);

	}



	public function payinfo() {

	if($_POST) {
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;

	$lastlogID = $this->mit->lastlogID($userid);

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;

	if(!empty($userid)) {
	    	$pallow = $this->db->get_where('reseller',array('id' =>$userid))->row()->payment;
		$sql="Select * from company where id='1'";
    $query = $this->db->query($sql);
	foreach ($query->result() as $row)
					{

   	foreach ($row as $key =>$value)
{
$$key = addslashes(trim($value));

}
					}

$cbalance = "
Bkash account no: $bkash

Nogad account no: $nogad

Rocket account no: $rocket

Upay account no: $upay
";

	$response = array(
					'success' => 1,
					'status'  => 1,
					'message' => $cbalance
					);

	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Match.'
					);
	}

	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}
	header('Content-type: application/json');
	echo json_encode($response);
	}





	public function makepay() {

	if($_POST) {




	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
		$amount = $this->security->xss_clean($this->input->post('amount'));
	$number = $this->security->xss_clean($this->input->post('number'));

$source = $this->security->xss_clean($this->input->post('source'));
if(empty($source))$source='main';

 $balance_system = $this->db->get_where('security_option',array('id' =>1))->row()->balance_system;
		if($source=='bank' && $balance_system==0){
          $error='Bank Balance off';
        }
$drive_system = $this->db->get_where('security_option',array('id' =>1))->row()->drive;
if($source=='drive' && $drive_system==0){
$error='Drive Balance off';
}

	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;

	$lastlogID = $this->mit->lastlogID($userid);

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;

	if(!empty($userid)) {
	$pallow = $this->db->get_where('reseller',array('id' =>$userid))->row()->payment;

$ctype =$this->db->get_where('reseller',array('id' =>$userid))->row()->custype;


$uid= $user['id'];
$idate=date('Y-m-d');
$ip = $_SERVER['REMOTE_ADDR'];

		$create_date=date('j F Y g:i A');
		 $ctrnx = $this->mdb->passwordChanger('encrypt', $number);
 $sql = "SELECT * FROM `trnx` where `trnx`='$ctrnx' and `amount`='$amount' and `status`='0'";
	$queryrtsloadm=$this->db->query($sql);
                      if($queryrtsloadm->num_rows()>0){}else{
                          $error='Trnx id Not Match';

                      }



if(empty($error)){

    $t="plus";

		$idate=date('Y-m-d');
		$create_date=date('j F Y g:i A');
	$queryi = "UPDATE `trnx` SET `status`='1' ,`userid`='$userid' where trnx='$ctrnx' and `amount`='$amount'";
		$ife=$this->db->query($queryi);
		if($ife){
	$obal=$this->mit->accountBalance($userid);

	 foreach ($queryrtsloadm->result() as $row){

   		$sender=$row->sender;

	}
		if($sender=='16216'){
$sender="rocket";
}

if($source=='bank')$sender='b_'.$sender;
if($source=='drive')$sender='d_'.$sender;


	$sql_rate="SELECT * from level_list where name='$ctype'";
		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{
				$p=$raterow->$sender;

}




$com=(($p/100)*$amount);
$tamount=($amount+$com);
             //if($source=='drive'){$tamount=$amount;}
  	$this->mit->balanceUpdate($userid, $tamount,$t,$source);
  		$bal=$this->mit->accountBalance($userid);

  	 $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `service`, `flex_id`, `p_id`, `date`, `time`) VALUES (NULL, '$userid', 'By auto $number in $source', '$obal', '0', '$tamount', '$bal', 'plus', 'transfer', '$number', '', '$idate', '$create_date')";

		 $this->db->query($sql_tr);

  	$sql="INSERT INTO `pay_receive` (`id`, `userid`, `desc`, `remark`, `sender`, `credit`, `account`, `type`, `transfer_type`, `idate`, `date`) VALUES (NULL, '$userid', 'auto', 'By auto $number in $source', '$userid', '$tamount', '$bal', 'receive', 'recharge', '$idate', '$create_date');";

		$this->db->query($sql);
		$set = $this->db->get_where('security_option',array('id' =>1))->row()->com_system;
		 if($source=='main' && $set!=2){

		$this->mdb->comupdate($userid,$amount,$sender,$source);


        }

		 if($source=='bank' && $set!=2 && $com > 0){

		$this->mdb->comupdate_bank($userid,$amount,$sender,$source);


        }


        if($source=='drive' && $set!=2 && $com > 0){

		$this->mdb->comupdate_bank($userid,$amount,$sender,$source);


        }




		}
	$response = array(
					'success' => 1,
					'status'  => 1,
					'message' => 'Payment added successfuly.'
					);

}
else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => $error
					);
	}
	}

	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}
	header('Content-type: application/json');
	echo json_encode($response);











}


	public function AccountRequest() {

		if($_POST) {
			$apikey = $this->security->xss_clean($this->input->post('token'));
			$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
			$reason = $this->security->xss_clean($this->input->post('reason'));
			$additional_info = $this->security->xss_clean($this->input->post('additional_info'));
			$action = $this->security->xss_clean($this->input->post('action'));

			// Validate that we have a valid action
			if($action != 'delete_account_request') {
				$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Invalid action specified.'
				);

				header('Content-type: application/json');
				echo json_encode($response);
				return;
			}

			// Get user information
			$userid = $this->db->get_where('apps_key', array('token' => $apikey, 'deviceid' => $deviceid))->row()->userid;

			if(!empty($userid)) {
				$username = $this->db->get_where('reseller', array('id' => $userid))->row()->username;
				$name = $this->db->get_where('reseller', array('id' => $userid))->row()->name;
				$email = $this->db->get_where('reseller', array('id' => $userid))->row()->email;
				$mobile = $this->db->get_where('reseller', array('id' => $userid))->row()->mobile;
				$level = $this->db->get_where('reseller', array('id' => $userid))->row()->custype;

				// Create timestamp
				$dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
				$create_date = $dt->format('j F Y g:i A');
				$idate = $dt->format('Y-m-d');

				// Check if table exists, if not create it
				$this->db->query("CREATE TABLE IF NOT EXISTS `account_deletion_requests` (
					`id` int(11) NOT NULL AUTO_INCREMENT,
					`user_id` int(11) NOT NULL,
					`username` varchar(255) NOT NULL,
					`name` varchar(255) DEFAULT NULL,
					`email` varchar(255) DEFAULT NULL,
					`mobile` varchar(255) DEFAULT NULL,
					`level` varchar(255) DEFAULT NULL,
					`reason` text NOT NULL,
					`additional_info` text,
					`status` int(1) NOT NULL DEFAULT '0',
					`request_date` varchar(255) NOT NULL,
					`idate` date NOT NULL,
					`processed_date` varchar(255) DEFAULT NULL,
					PRIMARY KEY (`id`)
				) ENGINE=InnoDB DEFAULT CHARSET=utf8;");

				// Insert the request into the database
				$data = array(
					'user_id' => $userid,
					'username' => $username,
					'name' => $name,
					'email' => $email,
					'mobile' => $mobile,
					'level' => $level,
					'reason' => $reason,
					'additional_info' => $additional_info,
					'status' => 0, // 0 = pending, 1 = approved, 2 = rejected
					'request_date' => $create_date,
					'idate' => $idate
				);

				$this->db->insert('account_deletion_requests', $data);

				// Log the activity
				$msg = 'Account deletion request submitted by ' . $username;
				$this->mit->InsertActivetUser($userid, $msg, 'Apps');

				// Send notification to admin
				$admin_id = $this->db->get_where('reseller', array('custype' => 'admin'))->row()->id;
				$title = 'Account Deletion Request';
				$body = "User $username has requested account deletion. Reason: $reason";

				// Try to send notification using Firebase
				try {
					$this->mit->send_fcm_msg($title, $body, "", $admin_id, "all");
				} catch (Exception $e) {
					// Log error but continue
					error_log('Failed to send notification: ' . $e->getMessage());
				}

				$response = array(
					'success' => 1,
					'status'  => 1,
					'message' => 'Your account deletion request has been submitted successfully. Our team will review your request and contact you soon.'
				);
			} else {
				$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'User authentication failed.'
				);
			}
		} else {
			$response = array(
				'success' => 0,
				'status'  => 2,
				'message' => 'Empty Not Work.'
			);
		}

		header('Content-type: application/json');
		echo json_encode($response);
	}


	public function picture() {

      $time=time();
    $str = $this->security->xss_clean($this->input->post('text'));
//file_put_contents("nid.txt",$str);

     // if(!getMimeType($_FILES["image1"]["tmp_name"]))$errors="extension not allowed, please choose a JPEG or PNG vaild file.";
    if(empty($errors)) {


        file_put_contents('output.txt', var_export($str, TRUE));
        foreach(preg_split("/((\r?\n)|(\r\n?))/", $str) as $line){

if (preg_match('~Name(.+?) ~', $line, $matches)) {
    $name = str_replace('Name: ', '', $line);
   $name = str_replace('Name:', '', $name);
  $name = str_replace('Name. ', '', $name);
  $name = str_replace('Name', '', $name);
  $name = str_replace('Name.', '', $name);

}

       if($this->mdb->date_of_birth($line) && !$this->mdb->nid($line)){
$date=$this->mdb->date_of_birth($line);
          }



          if($this->mdb->nid($line)){
$id=$this->mdb->nid($line);
          }

}
if(empty($name))$name=$this->mdb->nid_name($str);

   if(!empty($name) && !empty($id) && !empty($date)){

		$response = array(
					'success' => 1,
					'status'  => 1,
					'message' => 'pic updated',
          'nid' => $id,
          'name' => $name,
          'birth' => $date
					);
		 }else{
  $response = array(
					'success' => 0,
					'status'  => 0,
					'message' => 'ID card scan faild'
					);

   }

 }else{
  $response = array(
					'success' => 0,
					'status'  => 0,
					'message' => $errors
					);

   }




	header('Content-type: application/json');
	echo json_encode($response);











}


		public function autopayinfo() {

	if($_POST) {

	$amount= $this->security->xss_clean($this->input->post('amount', true));



		$sql="Select * from company where id='1'";
    $query = $this->db->query($sql);
	foreach ($query->result() as $row)
					{

   	foreach ($row as $key =>$value)
{
$$key = addslashes(trim($value));

}
					}

$cbalance = "Amount: $amount



Bkash account no: $bkash

Nogad account no: $nogad

Rocket account no: $rocket

Upay account no: $upay
";

	$response = array(
					'success' => 1,
					'status'  => 1,
					'message' => $cbalance
					);


	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}
	header('Content-type: application/json');
	echo json_encode($response);
	}


	public function resellerautoadd() {

	$var=$this->input->post();

	if($_POST) {


	 $join = $this->db->get_where('company',array('id' =>1))->row()->joining_fee;

	$postlevel = $this->security->xss_clean($this->input->post('level', true));

		$trnx = $this->security->xss_clean($this->input->post('trnx', true));
		 $ctrnx = $this->mdb->passwordChanger('encrypt', $trnx);
	$username = $this->security->xss_clean($this->input->post('username', true));
	$type = $this->security->xss_clean($this->input->post('type', true));

	$name = $this->security->xss_clean($this->input->post('name', true));

$mobile = $this->security->xss_clean($this->input->post('phone', true));
$email = $this->security->xss_clean($this->input->post('email', true));

	$password = $this->security->xss_clean($this->input->post('password', true));
	$pin = $this->security->xss_clean($this->input->post('resellerpin', true));
	$nid = $this->security->xss_clean($this->input->post('nid', true));
		$birth = $this->security->xss_clean($this->input->post('birth', true));

	$client_types = $this->security->xss_clean($this->input->post('client_types', true));

	$client_typess = $this->security->xss_clean($this->input->post('client_types', true));

	$this->form_validation->set_rules('username', 'Username', 'is_unique[reseller.username]|trim|required|xss_clean|min_length[5]');
		$this->form_validation->set_rules('nid', 'Nid', 'is_unique[reseller.nid]|trim|required|xss_clean|min_length[9]');

		$this->form_validation->set_rules('password', 'Password', 'trim|required|xss_clean|min_length[6]');
	$this->form_validation->set_rules('resellerpin', 'PIN', 'trim|required|xss_clean|min_length[4]');



	 $postlevel=strtolower($postlevel);
	  $custype = $this->db->get_where('level_list',array('real_name' =>$this->security->xss_clean($this->input->post('level', true))))->row()->name;


      if(empty($custype)){

		if($postlevel=='house'){$custype='reseller5';}
				else if($postlevel=='dgm'){$custype='reseller4';}
					else if($postlevel=='dealer'){$custype='reseller3';}
				else if($postlevel=='seller'){$custype='reseller2';}
				else if($postlevel=='retailer'){$custype='reseller1';}else{


			$custype = $this->db->get_where('level_list',array('real_name' =>$this->security->xss_clean($this->input->post('level', true))))->row()->name;


				}


      }



			  $sql_rate="SELECT * from level_list where name='$custype'";
		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{
				$amount=$raterow->self_price;

}

		if($type=='1' && $amount!='0'){
$sql = "SELECT * FROM `trnx` where `trnx`='$ctrnx' and `amount`='$amount' and `status`='0'";
	$queryrtsloadm=$this->db->query($sql);
                     if($queryrtsloadm->num_rows()!=1)$err="Invaild Trnx no or not match $amount tk" ;
      }






	if ($this->form_validation->run() == FALSE) {

	$err=validation_errors();
      $err = str_replace('<p>','',$err);
$err = str_replace('</p>','',$err);
	}



	$chtype = 'user';
	$chking=4;


	if($join!='1')$err='self Refistration off';



	if(empty($err) && $type=='1') {



	 	$chk_data=$this->mdb->getData('reseller',array('id'=>1));
				$sendername=$chk_data[0]['username'];
				$uparent=$chk_data[0]['p_id'];
				$ctype=$chk_data[0]['custype'];
				$myLeft=$chk_data[0]['lft'];
				$myRight=$chk_data[0]['rgt'];
				$uid=1;



	$idate=date('Y-m-d');
	$create_date=date('j F Y g:i A');
	$ip = $_SERVER['REMOTE_ADDR'];


			$client_types = 0;

		if (!empty($var['client_types'])):
		foreach ($var['client_types'] as $client_types):
        $add_type_values+= $client_types;
		endforeach;
		endif;

		$userpl=strlen($username);
		$pass=strlen($password);

		$q2="UPDATE reseller SET rgt = rgt + 2 WHERE rgt > $myLeft";
		$this->db->query($q2);

		$q3="UPDATE reseller SET lft = lft + 2 WHERE lft > $myLeft";
		$this->db->query($q3);


		$left=$myLeft +1;
		$right=$myLeft + 2;
		$hash = $this->mdb->generate($password);
		$hashpin = $this->mdb->generate($pin);

		$rlastids = $this->mit->lastUserID();
		 $rlastid = $rlastids+1;

		$sqltarif="INSERT INTO `tarif` (`id`, `make_by`, `name`, `desc`, `userid`, `duble`, `date`, `time`) VALUES (NULL, '$uid', '$username Tarif', '$add_type_values', '$rlastid', '$duble', '$idate', '$create_date');";

		$this->db->query($sqltarif);

		$tarifidlast = $this->db->insert_id();
		$otp = 0;

		$sql="INSERT INTO `reseller` (`id`, `username`, `password`, `pincode`, `type`, `name`, `mobile`, `email`, `note`, `custype`, `p_id`, `admin_id`, `lft`, `rgt`, `balance`, `balance_limit`, `create_date`, `ip`, `enbale_otp`, `tarif`, `oversale`, `status`, `level5`, `level4`, `level3`, `level2`, `level1`, `webAccess`, `appsAccess`,`nid`,`birth`) VALUES (NULL, '$username', '$hash', '$hashpin', '$client_typess', '$name', '$mobile', '$email', '', '$custype', '$uid', '$admin_id', '$left','$right', '', '', '$create_date', '$ip', '$otp', '$tarifidlast', '0', '1', '$uid', '$parent2', '$parent3', '$parent4', '$parent5', '1', '1','$nid','$birth');";

		$this->db->query($sql);
		$idlast = $this->db->insert_id();

		$queryi = "UPDATE `trnx` SET `status`='1' ,`userid`='$idlast' where trnx='$ctrnx' and `amount`='$amount'";
	$this->db->query($queryi);

		$msg= 'Add Reseller '.$username.' With self registration by Apps';
		$this->mit->InsertActivetUser($uid,$msg);


			$prebalance= $amount;

	$tm="minus";

		$bal= '0';

		  $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `date`, `time`) VALUES (NULL, '$idlast', 'new account for $username', '$prebalance', '$amount', '', '$bal', 'minus', '$idate', '$create_date')";
		  $this->db->query($sql_tr);

		$sql="INSERT INTO `pay_receive` (`id`, `userid`, `desc`, `remark`, `sender`, `debit`, `account`, `type`, `transfer_type`, `idate`, `date`) VALUES (NULL, '$idlast', 'new account for $username', 'By $username', '$idlast', '$amount', '$bal', 'new_account', 'recharge', '$idate', '$create_date')
		";
		$this->db->query($sql);

		$response = array(

					'success' => 1,
					'status'  => 1,
					'message' => ' Self registration Successfully.'
					);




	}else if(empty($err) && $type=='0') {



		$sql="Select * from company where id='1'";
    $query = $this->db->query($sql);
	foreach ($query->result() as $row)
					{

   	foreach ($row as $key =>$value)
{
$$key = addslashes(trim($value));

}
					}

$cbalance = "Amount: $amount



Bkash account no: $bkash

Nogad account no: $nogad

Rocket account no: $rocket

Upay account no: $upay
";



  $response = array(
					'success' => 1,
					'status'  => 1,
					'message' => $cbalance,
    'amount' => $amount
					);

}else {

		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => $err,
					 'amount' => $amount
					);

	}
	}else {

		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);

	}



	header('Content-type: application/json');
	echo json_encode($response);
	}



public function notice(){


		$response = array();
		$sql="SELECT * FROM `notice` order by id desc";

		$query = $this->db->query($sql);
		foreach ($query->result() as $row){

		$jsondata = array(
            'date' => $row->date,
            'notice' => $row->notice,
        );

		 array_push($response, $jsondata);

		}


	header('Content-type: application/json');
		echo json_encode(array("bmtelbd"=>$response));

	}




public function tricket_main(){

	if($_GET) {




	$apikey = $this->security->xss_clean($this->input->get('token'));
	$deviceid = $this->security->xss_clean($this->input->get('deviceid'));


	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;

	$lastlogID = $this->mit->lastlogID($userid);

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;

	if(!empty($userid)) {
		$response = array();
		$sql="SELECT * FROM `tricket_main` where id_user='$userid' order by id desc";

		$query = $this->db->query($sql);
		foreach ($query->result() as $row){

		$jsondata = array(
           'id' => $row->id,
            'date' => $row->c_date,
            'subject' => $row->subject,
        );

		 array_push($response, $jsondata);

		}

    }}
	header('Content-type: application/json');
		echo json_encode(array("bmtelbd"=>$response));

	}

	public function tricket_new() {

	if($_POST) {




	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
		$subject = $this->security->xss_clean($this->input->post('subject'));
	$message = $this->security->xss_clean($this->input->post('message'));
$id = $this->security->xss_clean($this->input->post('id'));


	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;

	$lastlogID = $this->mit->lastlogID($userid);

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;

	if(!empty($userid)) {


$uid= $user['id'];


	$idate=date('Y-m-d');
	$create_date=date('j F Y g:i A');
	$ip = $_SERVER['REMOTE_ADDR'];


if(!empty($id)) {

$sql="INSERT INTO `tickets` (`id`, `id_user`, `msg`, `time`, `date`, `ctime`, `tricket_id`, `ip`) VALUES (NULL, '$userid', '$message', '$create_date', '$idate', '$create_date', '$id', '$ip');";

		$this->db->query($sql);
  	$change = array(
						"status" => '0'
					);

		$this->mdb->update('tricket_main',$change,array('id'=>$id));


}else{

  	$sql="INSERT INTO `tricket_main` (`id`, `id_user`, `subject`, `c_date`) VALUES (NULL, '$userid', '$subject', '$create_date');";

		$this->db->query($sql);
		$tricket_id = $this->db->insert_id();
		$sql="INSERT INTO `tickets` (`id`, `id_user`, `msg`, `time`, `date`, `ctime`, `tricket_id`, `ip`) VALUES (NULL, '$userid', '$message', '$create_date', '$idate', '$create_date', '$tricket_id', '$ip');";

		$this->db->query($sql);
}




		}
	$response = array(
					'success' => 1,
					'status'  => 1,
					'message' => 'Successfull'
					);

	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}
	header('Content-type: application/json');
	echo json_encode($response);

	}

  public function trickets(){

	if($_GET) {




	$apikey = $this->security->xss_clean($this->input->get('token'));
	$deviceid = $this->security->xss_clean($this->input->get('deviceid'));
		$id = $this->security->xss_clean($this->input->get('id'));


	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;

	$lastlogID = $this->mit->lastlogID($userid);

	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;

	if(!empty($userid)) {
		$response = array();
      	$sql="select * from (SELECT * FROM `tickets` where id_user='$userid' and tricket_id='$id' order by id desc limit 20) tmp order by tmp.id asc";

		$query = $this->db->query($sql);
		foreach ($query->result() as $row){
          $ankid=$row->ank_id;
		if(empty($row->ank_id))$ankid='no';
		$jsondata = array(
           'id' => $row->id,
          'userid' => $row->id_user,
          'ankid' => $ankid,
            'date' => $row->time,
            'msg' => $row->msg,
        );

		 array_push($response, $jsondata);

		}

    }}
	header('Content-type: application/json');
		echo json_encode(array("bmtelbd"=>$response));

	}












}
