<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard extends CI_Controller {
	
	
	
		function __construct(){
        parent::__construct();
        
        date_default_timezone_set('Asia/Dhaka');
     
        //$this->load->model('admin');
		$this->load->library('form_validation');
		$this->load->library('user_agent');
			$this->load->helper('cookie');
			$this->load->library('encryption');
		
    }
	 
 
 
	 private function date_time(){
        //$timezone_offset = +6; // BD central time (gmt+6) for me
		//date_default_timezone_set("Asia/Dhaka");
        return $create_date = date('Y-m-d H:i:s');
     }
	 

	protected function rules() {
		$rules = array(
				 array(
					'field' => 'username',
					'label' => "Username",
					'rules' => 'trim|required|min_length[5]|max_length[40]|xss_clean'
				),
				array(
					'field' => 'password',
					'label' => "Password",
					'rules' => 'trim|required|max_length[40]|xss_clean'
				)
			);
		return $rules;
	}
	
	
	public function index()
	{ 
	
		if ($this->session->userdata('member_session')) {

        	redirect('main', 'refresh');
        }
		
		if($_POST) {
		    
		 
		    
		    
	$this->form_validation->set_rules('username', 'Userid', 'trim|required|min_length[3]|max_length[30]');
	$this->form_validation->set_rules('password', 'Password', 'trim|required|min_length[4]|max_length[30]');
   
     
        if ($this->form_validation->run() == FALSE) {
		$this->session->set_flashdata('error', 'Please Enter Correct Login Info');
		$data['page_name'] = 'login';
		$this->load->view('login21');
		  //redirect('website', 'refresh');
		}else {
			
	$username = $this->security->xss_clean($this->input->post('username'));
	$password = $this->security->xss_clean($this->input->post('password'));

	$tokencode = $this->security->get_csrf_hash();

	
	if(!empty($username) && !empty($password) ) {
	
	//$this->load->model('company');
        $result = $this->mit->login($username,$password);
		 if ($result) {

		 $ucidtype=$result->custype;
		 $ucid=$result->id;
		 $logincount=$result->login_count;
		 $enbaleotp=$result->enbale_otp;
		 $choseotp=$result->otp_choice;
		 $uemail=$result->email;
		  $umobile=$result->mobile;
		 $webaccessalow=$result->webAccess;
		 
		 $activests =$result->status;

		 if($ucidtype!="admin"){
		 
		 $loginplus = $logincount+1;
		 if($logincount>0){

		 	$access='no';


		 }else {
		 	$access='yes';

		 }



		 $sqlcont="SELECT * FROM device_list WHERE tokenid='$tokencode' and userid='$ucid'";

		$querycount = $this->db->query($sqlcont);
		
		foreach ($querycount->result() as $row3){
		  	 $mysafe_status=$row3->status;
		  	  $myfsttoken=$row3->tokenid;
		  	   $tblremember=$row3->remember;
		  	  
		  	  }
		  
		 if($mysafe_status==1 || $access=='yes') {
			 
		if($webaccessalow==1) {
		
		 $otppas=rand(111111,999999); 
		 
		 $hashotp = $this->mdb->generate($otppas); 
		
		 if($activests==0) { 
		 $this->session->set_flashdata('error', 'This Id is Block Please Contract Admin.');
		 $data['page_name'] = 'login';
		 $this->load->view('login21');
		 }else if($activests==2) { 
		 
		 $this->session->set_flashdata('error', 'This Id is Delete Please Contract Your Admin.');
		 $data['page_name'] = 'login';
		 $this->load->view('login21');
		 
		 }else {
			 	 
		
		
		$this->session->set_userdata('member_login', 'yes');
	


		 $loginupdate = array(
						"ip" => $_SERVER['REMOTE_ADDR'],
						"login_count" => $loginplus,
						"last_login" => $this->date_time()
					);
		 
		 $this->mdb->update('reseller',$loginupdate,array('id'=>$ucid));
		 	
		 
		$ctype="user";
		$secretcode=sha1(uniqid(rand(),true)); 
		$logtype="web";
		if($enbaleotp==1) {	
		 $otpupdate = array(
						"otp" => $hashotp
					);
		 
		 $this->mdb->update('reseller',$otpupdate,array('id'=>$ucid));
		 
		 
		  if($choseotp==3)$to=$uemail;
			       if($choseotp==4)$to=$umobile;
			      $subject="$system_name OTP CODE $otppas";
			      $msg="$system_name Dear $uname, Your OTP code is $otppas from ip $_SERVER[REMOTE_ADDR]";
			     
			      if($choseotp==3 or $choseotp==4){
			  $code=$this->mit->mail_send($to, $subject, $msg);
			      }
		}

		$this->mit->insertLog($ucid,$ctype,$secretcode,$logtype);
		
		
		
		$this->session->set_userdata('member_session', $result);
		
		 $sess_data = array(
               'secretcode' => $secretcode,
               'uid'     => $ucid
               
             );
   $this->session->set_userdata('securlog',$sess_data);
		
		
			$ps=$this->encryption->encrypt($password);
			 
			 setcookie("login_id", "$username");
			 setcookie("login_ses", "$ps");
		$msg= 'Successful Login';
		$this->mit->InsertActivetUser($ucid,$msg);

		$this->mit->clear_login_attempts($username);



		if($querycount->num_rows() == 0 ) {
		
		$referrer = $this->session->userdata('user_requested_page') ? $this->session->userdata('user_requested_page') : 'secure2step';

	}else if($tblremember==0){
		$referrer = $this->session->userdata('user_requested_page') ? $this->session->userdata('user_requested_page') : 'secure2step';

	}else {

		$referrer = $this->session->userdata('user_requested_page') ? $this->session->userdata('user_requested_page') : 'main';


	}
          
          redirect($referrer);

		// $data['page_name'] = 'dashboard';
		//$this->load->view('index', $data);
		 // redirect('home/dashboard', 'refresh');

      }
	  
	}else {
		
	$this->session->set_flashdata('error', 'Your Web Access Not Allow.');
    redirect('login', 'refresh');
			 
	}

      }else {

      	 $remember=0;

      	$this->mit->devicelist($ucid,$remember,'yes');
      	$msg= 'Access Denai';
		$trytype = 'password';
		
		$this->mit->increase_login_attempts($username,$password,$msg,$trytype);
      	 $this->session->set_flashdata('error', 'Please contact With admin for access');
      	 redirect('dashboard/login', 'refresh');

      }
      
      }// only reseller login
		
		} else {
		
		//$this->mit->InsertActivetUser($username,$msg);
		$msg= 'Invalid ID and password';
		$trytype = 'password';
		
		$this->mit->increase_login_attempts($username,$password,$msg,$trytype);
		
		 $this->session->set_flashdata('error', 'Invalid ID and password.');
		 $data['page_name'] = 'login';
		$this->load->view('login21');
		 }
		 
		 }else { // empty user and pass chaking
		  $this->session->set_flashdata('error', 'Please enter ID and password.');
		  redirect('login', 'refresh');
		 }
		}
		}else {
			$this->load->view('login21');
		}
			
	}
	
	
		public function login(){
				
		 if ($this->session->userdata('member_session')) {
				redirect('main', 'refresh');
		}else {
		$this->load->view('login21');
		}
				
		}

		public function loginNew(){
				
		 if ($this->session->userdata('member_session')) {
				redirect('main', 'refresh');
		}else {
		$this->load->view('login21');
		}
				
		}
		
		public function newlogin(){
				
		 if ($this->session->userdata('member_session')) {
				redirect('main', 'refresh');
		}else {
		$this->load->view('login21');
		}
				
		}
			
		
			
	public function notice() {
	
		$this->load->view('mit/notice');
	 }
	
	
}
