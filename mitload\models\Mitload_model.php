<?php Class Mitload_model extends CI_Model
{
   //ALTER TABLE `userlog` ADD `tokenid` VARCHAR(255) NULL AFTER `secretcode`;
     public $tables = array();

			 private function date_time(){
					$timezone_offset = +6; // BD central time (gmt+6) for me
					return $create_date = gmdate('Y-m-d H:i:s', time()+$timezone_offset*60*60);
				 }

public function send_fcm_msg($title,$body,$token,$id,$all=""){
    // Load the Firebase library
    $this->load->library('Firebase');

    // Prepare the data payload - keep it simple with string values only
    $data = [
        'id' => $id,
        'title' => $title,
        'body' => $body,
        'sound' => 'default',
        'click_action' => '.Retive'
    ];

    // Check if we're sending to a topic or a specific device
    if(!empty($all)){
        // Send to both domain topic and the standard topic "55555"
        $domain = $_SERVER['HTTP_HOST'];
        $domain = str_replace(array('www.'), array(''), $domain);

        // Send to domain topic
        $result1 = $this->firebase->send_to_topic($domain, $title, $body, $data);

        // Also send to the standard topic "55555" that the app subscribes to
        $result2 = $this->firebase->send_to_topic("55555", $title, $body, $data);

        // Combine results
        $result = [
            'domain_topic' => $result1,
            'standard_topic' => $result2,
            'success' => ($result1['success'] || $result2['success'])
        ];
    } else {
        // Send to specific device
        $result = $this->firebase->send_to_device($token, $title, $body, $data);
    }

    return json_encode($result);
}

		public function valuesOnBinPosition($dividend) {
					$position = 0;
					$remains = 0;
					$result = 0;
					$checkval = array();
					while ($dividend > 0):
						$remains = $dividend % 2;
						$dividend = floor($dividend / 2);
						$result = $remains * pow(2, $position);
						$checkval[$position] = $result;
						$position++;
					endwhile;
					return $checkval;
				}

		public function serviceMneuper($serviceid) {

		$sql_serper="SELECT * from module where serviceid='$serviceid'";

		$query = $this->db->query($sql_serper);

         foreach ($query->result() as $row_serper)
		{
		$status_serper=$row_serper->status;
		}
		if($status_serper==1) {
			return 1;
			}else {
			return 0;
			}


		}

		public function fetch_reseller($limit, $start) {

			$member = $this->session->userdata('member_session');

		$sessuid = $member->id;
		$lr=$this->mit->leftRight($sessuid);
		$lft=$lr[0];
		$rgt=$lr[1];


        $this->db->limit($limit, $start);
        $this->db->order_by("id", "desc");
		$this->db->where("lft>", $lft);
		$this->db->where("rgt<", $rgt);
		$this->db->where("status!=", 2);

        $query = $this->db->get("reseller");


        if ($query->num_rows() > 0) {
            foreach ($query->result() as $row) {
                $data[] = $row;
            }
            return $data;
        }
        return false;
   }


       public function mail_send($to, $subject, $msg)
{
if (filter_var($to, FILTER_VALIDATE_EMAIL)) {



      $json= $this->mail_send_direct($to, $subject, $msg);


}else{

   $json= $this->sms_send_direct($to, $subject, $msg);


        }




 return $json;

}

public function sms_send_direct($to, $subject, $msg){

$domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);

$url="http://$domain/offline/sms?pa=5463243&to=$to&message=".urlencode($msg)."";
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, $url);

    $data = curl_exec($ch);
    curl_close($ch);





return true;
		}

 public function mail_send_direct($to, $subject, $msg){

$domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);

$from="info@$domain";
		$headers = 'From: '.$from.'' . "\r\n" .
					'Reply-To: '.$from.'' . "\r\n" .
					'X-Mailer: PHP/' . phpversion();
		$mail = mail($to, $subject, $msg, $headers);
		if($mail) {
			return true;
		}

}


   	public function passchk($scode,$utype,$appsuser="") {

			 if($utype=="admin") {
			 $member = $this->session->userdata('admin_session');
			 }else {
			 $member = $this->session->userdata('member_session');
			 }
			 if(!empty($appsuser)) {
			 	$sessuid = $appsuser;
			 }else {
			 	$sessuid = $member->id;
			 }

			 $user_info=$this->mdb->getData('reseller',array('id'=>$sessuid));
							$secret = $user_info[0]['gotp_key'];

							$enotp = $user_info[0]['enbale_otp'];
							$otp_choice = $user_info[0]['otp_choice'];

							$tsecret = 'BIIOMONV4GKEPQAC';


					if($enotp=="1")
					{
					$otpcodsame=$user_info[0]['otp'];
					}else {
					$otpcodsame=$user_info[0]['password'];
					}
					if($enotp==1 && $otp_choice==2) {
					$tolerance = 2;
					$checkResult = $this->googleauthenticator->verifyCode($secret, $scode, 2);
					}else {
					$checkResult=$this->mdb->verify($scode,$otpcodsame);
					}

					if ($checkResult) {
						return true;
					}else {
						return false;
					}

		}





   // otp and pincode check here
   	public function twootp($scode,$utype,$appsuser="") {

			 if($utype=="admin") {
			 $member = $this->session->userdata('admin_session');
			 }else {
			 $member = $this->session->userdata('member_session');
			 }
			 if(!empty($appsuser)) {
			 	$sessuid = $appsuser;
			 }else {
			 	$sessuid = $member->id;
			 }

			 $user_info=$this->mdb->getData('reseller',array('id'=>$sessuid));
							$secret = $user_info[0]['gotp_key'];

							$enotp = $user_info[0]['enbale_otp'];
							$otp_choice = $user_info[0]['otp_choice'];

							$tsecret = 'BIIOMONV4GKEPQAC';



					$otpcodsame=$user_info[0]['otp'];


					$checkResult=$this->mdb->verify($scode,$otpcodsame);


					if ($checkResult) {
						return true;
					}else {
						return false;
					}

		}




	public function otpchk($scode,$utype,$appsuser="",$pattern="") {

			 if($utype=="admin") {
			 $member = $this->session->userdata('admin_session');
			 }else {
			 $member = $this->session->userdata('member_session');
			 }
			 if(!empty($appsuser)) {
			 	$sessuid = $appsuser;
			 }else {
			 	$sessuid = $member->id;
			 }

			 $user_info=$this->mdb->getData('reseller',array('id'=>$sessuid));
							$secret = $user_info[0]['gotp_key'];

							$enotp = $user_info[0]['enbale_otp'];
							$otp_choice = $user_info[0]['otp_choice'];

							$tsecret = 'BIIOMONV4GKEPQAC';


					if($enotp=="1")
					{
					$otpcodsame=$user_info[0]['otp'];
					}else {
					$otpcodsame=$user_info[0]['pincode'];
					}


					if($pattern==1 && $otp_choice==5){

					 	$otpcodsame=$user_info[0]['otp'];

					}


					if($enotp==1 && $otp_choice==2) {
					$tolerance = 2;
					$checkResult = $this->googleauthenticator->verifyCode($secret, $scode, 2);
					}else {
					$checkResult=$this->mdb->verify($scode,$otpcodsame);
					}

					if ($checkResult) {
						return true;
					}else {
						return false;
					}

		}

		public function leftRight($ucid){


		$sql3="SELECT lft,rgt from reseller where id='$ucid'";
		$query = $this->db->query($sql3);

					foreach ($query->result() as $row3)
					{
				$left=$row3->lft;
				$right=$row3->rgt;
					}


		return array($left,$right);

		}


   public function countBalance($ucid, $amiunt ){
     $parent =$ucid;
    for ($i=0; $i<200; $i++){

		$this->db->select('*');
		$this->db->from('reseller');
        $this->db->where('id', $parent);
		$this->db->where('status', 1);
        $query=$this->db->get();
        $row=$query->row();
		$parent=$row->p_id;


		$plimtbal = $row->balance - $row->balance_limit;

			if( $plimtbal < $amiunt ){
            $i=2000;
            return FALSE;
			}

        if($row->p_id==1){
            $i=2000;
			}
		}

            return TRUE;

	}


		public function checkMobile($phone,$service){


		$q="select max(id)as id, count(*)as cnt from sendflexi where phone='$phone' and service='$service'";

		$query = $this->db->query($q);

					foreach ($query->result() as $row)
					{
						$id=$row->id;
						$cnt=$row->cnt;
					}




		if($cnt>0){

		$create_date = $this->db->get_where('sendflexi',array('id' =>$id))->row()->submitted_date;

		$dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
		$request_date=$dt->format('Y-m-d H:i:s');
		$idate = $dt->format('Y-m-d');

		  $a= strtotime($request_date);
		  $b= strtotime($create_date);
		  $time=$a-$b;

		   $dateDiff    = $a-$b ;
		   $fullDays    = floor($dateDiff/(60*60*24));
		   $fullHours   = floor(($dateDiff-($fullDays*60*60*24))/(60*60));
		   $fullMinutes = floor(($dateDiff-($fullDays*60*60*24)-($fullHours*60*60))/60);
		   $fullSec     = $time;



		   return $fullSec;
		   }else{
		   return 0;
		   }

		}


			/// flexiload bkash dbbl etc service cost


	public function reverse_treeBalanceChk($account_no,$amount,$service,$pcode){

		$ucid = $account_no;

		   for ($i=1; $i<=8; $i++){

			 $sql = "SELECT * from reseller where id='$ucid' and custype!='admin'";

				$query = $this->db->query($sql);

					foreach ($query->result() as $row)
					{
						$ucid = $row->p_id;
					}


				if($ucid!=1){

				if(!empty($ucid)) {


		$myacc_tarf_up = $this->db->get_where('reseller',array('id' =>$ucid))->row()->tarif;
		$myup = $this->db->get_where('reseller',array('id' =>$ucid))->row()->p_id;

		//$myacc_tarf=$row_myacc['tarif'];
		//$myup=$row_myacc['p_id'];



		$sql_rate="SELECT * from price where service='$service' and pcode='$pcode'";
		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{

		 $servicecode=$raterow->service;
		 $prefix=$raterow->prefix;
		 $rate=$raterow->rate;
		 $ratecode=$raterow->pcode;

		 $comm=$raterow->comm;
		 $charge=$raterow->charge;
					}

		  $rates_rate=$rate*$amount;

		  $comm_rate=$amount*$comm/100;

		  $charge_rate=$amount*$charge/100;

		  $amountrate=$rates_rate+$charge_rate;

		  $final_amount=$amountrate-$comm_rate;


		$prebalance=$this->accountBalance($ucid);
		$accountbalance=$prebalance-$final_amount;


		  }
		}
		   }// loop finish



		$myaccsalf_tarf = $this->db->get_where('reseller',array('id' =>$account_no))->row()->tarif;
		$uper_id = $this->db->get_where('reseller',array('id' =>$account_no))->row()->p_id;

		$sql_rate="SELECT * from price where service='$service' and pcode='$pcode'";
		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{

		 $servicecode=$raterow->service;
		 $prefix=$raterow->prefix;
		 $rate=$raterow->rate;

		 $comm=$raterow->comm;
		 $charge=$raterow->charge;
					}

		   $rates_rate=$rate*$amount;

		  $comm_rate=$amount*$comm/100;

		  $charge_rate=$amount*$charge/100;

		  $amountrate=$rates_rate+$charge_rate;

		  $cus_final_amount=$amountrate-$comm_rate;


		if($cus_final_amount>0 && !empty($service)) {

		$stmsg="TRUE";

		}else {

		$stmsg="FALSE";

		}
		return $stmsg;
		}

/// flexiload bkash dbbl etc service cost



  public function treeBalanceUpdate($account_no,$number,$amount,$service,$sid,$pcode,$reqslid,$type,$final_amount,$source){

	  if(empty($source)){

 $source='main';

}
      if(empty($final_amount)){

     $final_amount= $amount;
      }
         if($final_amount=="0"){

     $final_amount= $amount;
      }
		$ucid = $account_no;

		$myacc_tarf = $this->db->get_where('reseller',array('id' =>$account_no))->row()->tarif;
		$uper_id = $this->db->get_where('reseller',array('id' =>$account_no))->row()->p_id;










		$prebalance=$this->accountBalance($account_no,$source);
		$accountbalance=$prebalance-$final_amount;



		$dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
		$create_date = $dt->format('j F Y g:i A');
		$idate = $dt->format('Y-m-d');

		$titleService = $this->db->get_where('module',array('serviceid' =>$service))->row()->title;

		  $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `service`, `flex_id`, `status`, `send_sl`, `p_id`, `date`, `time`) VALUES (NULL, '$account_no', 'Self $titleService Num: $number and $amount', '$prebalance', '$final_amount', '', '$accountbalance', '$type', '$service', '$sid', '0', '$reqslid', '$uper_id', '$idate', '$create_date');";

			$this->db->query($sql_tr);





					  $this->balanceUpdate($account_no,$final_amount,$type,$source);






		}













	public function reverse_treeBalanceUpdate($account_no,$number,$amount,$service,$sid,$pcode,$reqslid,$type){
	    $apiuser = "Select * from drive_package where price='$amount' and status='1' order by id asc limit 1";

			$queryrtsload=$this->db->query($apiuser);
 $balance_system = $this->db->get_where('security_option',array('id' =>1))->row()->balance_system;
		if($balance_system==1){
          if($service==64 or $service==16384 or $service==512){

            if($queryrtsload->num_rows()> 0) {
                $drive_system = $this->db->get_where('security_option',array('id' =>1))->row()->drive;
              if($drive_system==1){$source='drive';}else{ $source='main';}
            }else{

        $source='main';

            }
          }else{
           $source='bank';
          }
        }

		$ucid = $account_no;

		$myacc_tarf = $this->db->get_where('reseller',array('id' =>$account_no))->row()->tarif;
		$uper_id = $this->db->get_where('reseller',array('id' =>$account_no))->row()->p_id;








		$sql_rate="SELECT * from price where service='$service' and pcode='$pcode'";
		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{

		 $servicecode=$raterow->service;
		 $prefix=$raterow->prefix;
		 $rate=$raterow->rate;

		 $comm=$raterow->comm;
		 $charge=$raterow->charge;
					}



		  	$first=substr($number,0,3);
							$amus=floor($amount);
		$sql_ratef="SELECT * from com_price where service='$servicecode' and prefix='$first' and price='$amus'";


		$queryratef = $this->db->query($sql_ratef);
		  foreach ($queryratef->result() as $raterowf)
					{

						 $comm_f=$raterowf->comm;
						 $charge_f=$raterowf->charge;

					}


		   $rates_rate=$rate*$amount;

		  $comm_rate=$amount*$comm/100;
		   $comm_rate=$comm_f+$comm_rate;



		  $charge_rate=$amount*$charge/100;

		  $amountrate=$rates_rate+$charge_rate;

		  $final_amount=$amountrate-$comm_rate;



		$prebalance=$this->accountBalance($account_no,$source);
		$accountbalance=$prebalance-$final_amount;



		$dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
		$create_date = $dt->format('j F Y g:i A');
		$idate = $dt->format('Y-m-d');

		$titleService = $this->db->get_where('module',array('serviceid' =>$service))->row()->title;

		  $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `service`, `flex_id`, `status`, `send_sl`, `p_id`, `date`, `time`) VALUES (NULL, '$account_no', 'Self $titleService Num: $number and $amount', '$prebalance', '$final_amount', '', '$accountbalance', '$type', '$service', '$sid', '0', '$reqslid', '$uper_id', '$idate', '$create_date');";

			$this->db->query($sql_tr);
if(empty($source)){

 $source='main';

}
      if(empty($final_amount)){

     $final_amount= $amount;
      }
         if($final_amount=="0"){

     $final_amount= $amount;
      }




					  $this->balanceUpdate($account_no,$final_amount,$type,$source);



		if($final_amount!="0" && !empty($service)) {

		$stmsg="TRUE";

		}else {

		$stmsg="FALSE";

		}
		return $stmsg;

		}
		// flexiload bkash dbbl others finish

		// billpay , refilcard,bank and others balance update

	function reverse_serviceBalanceUpdate($account_no,$number,$amount,$service,$sid,$pcode,$type,$step=""){
$apiuser = "Select * from drive_package where price='$amount' and status='1' order by id asc limit 1";

			$queryrtsload=$this->db->query($apiuser);
 $balance_system = $this->db->get_where('security_option',array('id' =>1))->row()->balance_system;
		if($balance_system==1){
          if($service==64 or $service==16384 or $service==512){

            if($queryrtsload->num_rows()> 0) {
                 $drive_system = $this->db->get_where('security_option',array('id' =>1))->row()->drive;
              if($drive_system==1){$source='drive';}else{ $source='main';}
            }else{

        $source='main';

            }
          }else{
           $source='bank';
          }
        }
		$ucid = $account_no;


	$myacc_tarf = $this->db->get_where('reseller',array('id' =>$account_no))->row()->tarif;

	$uper_id = $this->db->get_where('reseller',array('id' =>$account_no))->row()->p_id;



	$sql_rate="SELECT * from price where service='$service' and pcode='$pcode'";

	 $queryrt = $this->db->query($sql_rate);
	foreach ($queryrt->result() as $raterow) {

	 $servicecode=$raterow->service;
	 $prefix=$raterow->prefix;
	 $rate=$raterow->rate;
	 $comm=$raterow->comm;
	 $charge=$raterow->charge;
	}

	  $rates_rate=$rate*$amount;

	  $comm_rate=$amount*$comm/100;

	  $charge_rate=$amount*$charge/100;

	  $amountrate=$rates_rate+$charge_rate;

	  $final_amount=$amountrate-$comm_rate;
	  $res_final_amount=$amountrate-$comm_rate;

	  ///$final_amount=$amount;
		if(empty($final_amount))$final_amount=$amount;

	$prebalance=$this->accountBalance($account_no,$source);
	$accountbalance=$prebalance-$final_amount;

	$dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
	$create_date = $dt->format('j F Y g:i A');
	$idate = $dt->format('Y-m-d');



	$titleService = $this->db->get_where('module',array('serviceid' =>$service))->row()->title;
if(empty($source)){

 $source='main';

}
      if(empty($final_amount)){

     $final_amount= $amount;
      }
         if($final_amount=="0"){

     $final_amount= $amount;
      }

	   if($type=="minus") {
	  $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `service`, `flex_id`, `status`, `p_id`, `date`, `time`) VALUES (NULL, '$account_no', 'Self $titleService $number and $amount', '$prebalance', '$final_amount', '', '$accountbalance', '$type', '$service', '$sid', '0', '$uper_id', '$idate', '$create_date');";

	 $this->db->query($sql_tr);



             $this->balanceUpdate($account_no,$final_amount,$type,$source);
			}


		if($final_amount>0 && $res_final_amount>0 && !empty($service)) {

		$stmsg="TRUE";

		}else {

		$stmsg="FALSE";

		}
		return $stmsg;

		}

// billpay and other finish


	public function accountBalance($id,$type=""){
					$this->db->select('*');
					$this->db->from('reseller');
					$this->db->where('id', $id);
					$query=$this->db->get();
					$row=$query->row();
if($type=='bank'){
					$bal=$row->bank_balance;
}else if($type=='drive'){
					$bal=$row->drive_bal;
}else{
$bal=$row->balance;
}
return $bal;
			}


        /**
         * Send balance notification to user
         *
         * @param int $user_id User ID
         * @param float $amount Amount added/subtracted
         * @param string $type Type of transaction (plus/minus)
         * @param string $source Source of balance (main/bank/drive)
         * @return bool Success status
         */
        public function send_balance_notification($user_id, $amount, $type, $source = "main") {
            // Get user's device token
            $ftoken = $this->db->get_where('reseller', array('id' => $user_id))->row()->note;

            if (empty($ftoken)) {
                // No token found, can't send notification
                return false;
            }

            // Format the source name for display
            $source_display = ucfirst($source);

            // Create notification title and message based on transaction type
            if ($type == "plus") {
                $title = "Balance Added";
                $body = "$amount TK has been added to your $source_display balance";
            } else {
                $title = "Balance Deducted";
                $body = "$amount TK has been deducted from your $source_display balance";
            }

            // Get current balance
            $current_balance = 0;
            if ($source == 'bank') {
                $current_balance = $this->db->get_where('reseller', array('id' => $user_id))->row()->bank_balance;
            } else if ($source == 'drive') {
                $current_balance = $this->db->get_where('reseller', array('id' => $user_id))->row()->drive_bal;
            } else {
                $current_balance = $this->db->get_where('reseller', array('id' => $user_id))->row()->balance;
            }

            // Add current balance to notification
            $body .= ". Current balance: $current_balance TK";

            // Prepare data for notification
            $data = [
                'type' => 'balance_update',
                'amount' => (string)$amount,
                'transaction_type' => $type,
                'source' => $source,
                'current_balance' => (string)$current_balance,
                'timestamp' => date('Y-m-d H:i:s')
            ];

            // Send notification
            $result = $this->send_fcm_msg($title, $body, $ftoken, $user_id);

            return true;
        }

		public function balanceUpdate($ucid,$amount,$type,$source=""){

		if($type=="minus"){
          if($source=='bank'){
          $bq="UPDATE `reseller` SET `bank_balance` = bank_balance-$amount WHERE `reseller`.`id` = '$ucid';";

          }else if($source=='drive'){
          $bq="UPDATE `reseller` SET `drive_bal` = drive_bal-$amount WHERE `reseller`.`id` = '$ucid';";

          }else{
		$bq="UPDATE `reseller` SET `balance` = balance-$amount WHERE `reseller`.`id` = '$ucid';";
          }
		$blsup = $this->db->query($bq);
		if($blsup) {
            // Send notification for balance deduction
            $this->send_balance_notification($ucid, $amount, "minus", $source ?: "main");
		    return true;
		}

		}else{

		if($source=='bank'){
          $bq="UPDATE `reseller` SET `bank_balance` = bank_balance+$amount WHERE `reseller`.`id` = '$ucid';";

          }else if($source=='drive'){
          $bq="UPDATE `reseller` SET `drive_bal` = drive_bal+$amount WHERE `reseller`.`id` = '$ucid';";

          }else{
		$bq="UPDATE `reseller` SET `balance` = balance+$amount WHERE `reseller`.`id` = '$ucid';";
          }
		$blsup = $this->db->query($bq);
		if($blsup) {
            // Send notification for balance addition
            $this->send_balance_notification($ucid, $amount, "plus", $source ?: "main");
		    return true;
		}

		}


		}


		public function lastUserID(){

		$query2 = $this->db->query("SELECT max(id)as id from reseller");
		$row = $query2->row();

		return $row->id;
		}

			public function lastlogID($userid,$utype=""){
		if(empty($utype)){$utype='user';}
		$query2 = $this->db->query("SELECT max(id)as id from userlog where ucid='$userid' and ctype='$utype'");
		$row = $query2->row();

		return $row->id;
		}






	public function reportDetails($opservice,$uid,$sdate=NULL,$edate=NULL)
    {

	$sevice_query="SELECT * from sendflexi where service='$opservice' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') and status='1' and idate >= '$sdate' and idate <= '$edate'";


	$query3 = $this->db->query($sevice_query);
		foreach($query3->result() as $row33) {

   $sid=$row33->id;
    $userid=$row33->userid;

   $service=$row33->service;
   $cbalan+=$row33->balance;

   $costamount = $this->db->get_where('trans',array('send_sl' =>$sid, 'userid' =>$uid))->row()->debit;

    $tcost+=$costamount;
	}



    return $tcost;
    }

	public function profitDetails($opservice,$uid,$sdate=NULL,$edate=NULL)
    {
	$sevice_query="SELECT * from sendflexi where service='$opservice' and (level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') and status='1' and idate >= '$sdate' and idate <= '$edate'";

	$query3 = $this->db->query($sevice_query);
		foreach($query3->result() as $mycisttbl) {

	$slids =$mycisttbl->id;
	$resel_userid=$mycisttbl->userid;
	$resel_pid=$mycisttbl->p_id;

  $resel_service=$mycisttbl->service;



   $costamount = $this->db->get_where('trans',array('service' =>$resel_service, 'send_sl' =>$slids, 'userid' =>$uid))->row()->debit;


    $my_flexi_cost+=$costamount;




	 $res_costamount = $this->db->get_where('trans',array('service' =>$resel_service, 'send_sl' =>$slids, 'p_id' =>$uid))->row()->debit;

    $my_flexi_res_tcost+=$res_costamount;

	}



    return array($my_flexi_cost,$my_flexi_res_tcost);
    }




	public function reportadminDetails($opservice,$uid,$sdate=NULL,$edate=NULL)
    {
	if($uid==1) {
	$sevice_query="SELECT * from sendflexi where service='$opservice' and status='1' and idate >= '$sdate' and idate <= '$edate'";
	}else {
	$sevice_query="SELECT * from sendflexi where service='$opservice' and (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') and status='1' and idate >= '$sdate' and idate <= '$edate'";
	}

	$query3 = $this->db->query($sevice_query);
		foreach($query3->result() as $row33) {

   $sid=$row33->id;
    $userid=$row33->userid;

   $service=$row33->service;
   $cbalan+=$row33->balance;

   $costamount = $this->db->get_where('trans',array('send_sl' =>$sid, 'p_id' =>1))->row()->debit;

    $tcost+=$costamount;
	}



    return array($cbalan,$tcost);
    }


	/// device list here


	public function devicelist($user_id, $remember="", $fakelog="", $appsendcode="", $devtype="")
			{
			$browser=$this->getBrowser();
			$bname=$browser['name'];
			$version=$browser['version'];
			if($devtype=="Apps") {
			$win="Android";
			}else {
			$win=$browser['platform'];
			}
			if($devtype=="Apps") {
			$bd="Apps";
			}else {
			$bd="$bname,$version";
			}

			$tokename = $this->security->get_csrf_token_name();
			if(!empty($appsendcode)) {
			$tokencode=$appsendcode;
			}else {
			$tokencode = $this->security->get_csrf_hash();
			}
			$session=session_id();

			$ip_address = $_SERVER['REMOTE_ADDR'];

			$p_id = $this->db->get_where('reseller',array('id' =>$user_id))->row()->p_id;
			$logincount = $this->db->get_where('reseller',array('id' =>$user_id))->row()->login_count;

				$this->db->select('*');
				$this->db->from('device_list');
				$this->db->where('userid', $user_id);
				$this->db->where('tokenid', $tokencode);

				$query=$this->db->get();

				if($query->num_rows()==0) {

					if($fakelog=="yes") {
						$devsts = "0";
					}else {
						$devsts = "1";
					}



				$arr = array('ip' => $ip_address, 'userid' => $user_id,
				'tokenid' => $tokencode,
				'session' => $session,
				'p_id' => $p_id,
				'browser' => $bd,
				'remember' => $remember,
				'status' => $devsts,
				'os' => $win,
				'date' => date('Y-m-d'),
				'time' => time());

				return $this->db->insert('device_list', $arr);
				}else {

				$arrupdate = array('ip' => $ip_address,
				'session' => $session,
				'p_id' => $p_id,
				'browser' => $bd,
				'remember' => $remember,
				'os' => $win,
				'date' => date('Y-m-d'),
				'time' => time());

				$this->db->where('userid', $user_id);
				$this->db->where('tokenid', $tokencode);
				return $this->db->update('device_list', $arrupdate);
				}

				return FALSE;
			}


	public function InsertActivet($msg) {


			$member = $this->session->userdata('member_session');
			$uid= $member->id;
			$uparent= $member->p_id;



			$ip=$_SERVER['REMOTE_ADDR'];

			$browser=$this->getBrowser();
			$bname=$browser['name'];
			$version=$browser['version'];
			$win=$browser['platform'];
			$bd="$bname,$version";
			$create_date = $this->date_time();
			$idate = date("Y-m-d");


			$actsl="INSERT INTO `activity_log` (`id`, `userid`, `p_id`, `secret`, `session`, `time`, `msg`, `ip`, `browser`, `os`, `date`) VALUES (NULL, '$uid', '$uparent', '$secretcode', '$session', '$create_date', '$msg', '$ip', '$bd', '$win', '$idate')";

			$this->db->query($actsl);



			}

		public function InsertActivetUser($user="", $msg="", $devtype="") {



			//$uid = $this->db->get_where('reseller',array('id' =>$user))->row()->id;
			$uparent = $this->db->get_where('reseller',array('id' =>$user))->row()->p_id;
			$ctype = $this->db->get_where('reseller',array('id' =>$user))->row()->custype;

			$ip=$_SERVER['REMOTE_ADDR'];
			$session=session_id();
			$getdata= $this->session->userdata('securlog');
			$secretcode = $getdata['secretcode'];

			$browser=$this->getBrowser();
			$bname=$browser['name'];
			$version=$browser['version'];
			if($devtype=="Apps") {
			$win="Android";
			}else {
			$win=$browser['platform'];
			}
			if($devtype=="Apps") {
			$bd="Apps";
			}else {
			$bd="$bname,$version";
			}
			$create_date = $this->date_time();
			$idate = date("Y-m-d");




			$actsl="INSERT INTO `activity_log` (`id`, `userid`, `p_id`, `secret`, `ctype`, `session`, `time`, `msg`, `ip`, `browser`, `os`, `date`) VALUES (NULL, '$user', '$uparent', '$secretcode', '$ctype', '$session', '$create_date', '$msg', '$ip', '$bd', '$win', '$idate')";

			$this->db->query($actsl);



			}

			public function increase_login_attempts($identity,$password,$mesage,$typetry="")
			{
			$browser=$this->getBrowser();
			$bname=$browser['name'];
			$version=$browser['version'];
			$win=$browser['platform'];
			$bd="$bname,$version";

			$tokename = $this->security->get_csrf_token_name();
			$tokencode = $this->security->get_csrf_hash();
			$session=session_id();

				$ip_address = $_SERVER['REMOTE_ADDR'];

				$this->db->select('*');
				$this->db->from('login_attempts');
				//$this->db->where('login', $identity);
				$this->db->where('tokenid', $tokencode);
				$this->db->where('session_id', $session);

				$query=$this->db->get();

				if($query->num_rows()<6) {



				$arr = array('ip_address' => $ip_address, 'login' => $identity,
				'tokenid' => $tokencode,
				'password' => $password,
				'session_id' => $session,
				'mesage' => $mesage,
				'type' => $typetry,
				'browser' => $bd,
				'os' => $win,
				'date' => $this->date_time(),
				'time' => time());

				return $this->db->insert('login_attempts', $arr);
				}else {

					if($typetry=="pin") {

					 $otpupdate = array(
						"status" => 0
					);

		// $this->mdb->update('reseller',$otpupdate,array('username'=>$identity));

		 $this->clear_login_attempts($identity);

					}else {

				$this->db->select('*');
				$this->db->from('ipblock');
				$this->db->where('ip', $ip_address);

				$query=$this->db->get();

				if($query->num_rows()==0) {

				$arr = array('ip' => $ip_address, 'login' => $identity,
				'status' => $mesage,
				'browser' => $bd,
				'sesion_id' => $tokencode,
				'date' => time());
				return $this->db->insert('ipblock', $arr);
				}
				}
				}

				return FALSE;
			}

			public function clear_login_attempts($identity, $expire_period = 86400)
			{

			 $ip_address = $_SERVER['REMOTE_ADDR'];
			 $tokencode = $this->security->get_csrf_hash();

			$this->db->where(array('ip_address' => $ip_address, 'tokenid' => $tokencode, 'login' => $identity));
					// Purge obsolete login attempts
			$this->db->or_where('time <', time() - $expire_period, FALSE);

			return $this->db->delete('login_attempts');

				return FALSE;
			}



	public function company_name($id){
					$this->db->select('company_name');
					$this->db->from('company');
					$this->db->where('id', $id);
					$query=$this->db->get();
					$row=$query->row();
					return $row->company_name;
			}

	public function company_title($id){
					$this->db->select('company_title');
					$this->db->from('company');
					$this->db->where('id', $id);
					$query=$this->db->get();
					$row=$query->row();
					return $row->company_title;
			}

	public function company_details($id){
					$this->db->select('*');
					$this->db->from('company');
					$this->db->where('id', $id);
					$query=$this->db->get();
					$row=$query->row();

					$company_name=$row->company_name;
					$company_title=$row->company_title;
					$meta=$row->meta;
					$keyword=$row->keyword;
					$footer=$row->footer;
					return array($company_name,$company_title,$meta,$keyword,$footer);

			}


	public function notice($id){
					$this->db->select('lnotice');
					$this->db->from('login_notice');
					$this->db->where('id', $id);
					$query=$this->db->get();
					$row=$query->row();
					return $row->lnotice;
			}

	public function login($userid = NULL,$password = NULL){
				$this->db->select('*');
				$this->db->from('reseller');
				$this->db->where('username', $userid);
				 //$this->db->where('user_is_active!=0');
				$query=$this->db->get();

				if($query->num_rows()>0) {

				$row=$query->row();
				$hash=$row->password;

				$r=$this->verify($password,$hash);

				if($r==true) {
				return $query->row();
				}
				}else{return false;}
		}
			public function login_by_pin($userid = NULL,$password = NULL){
				$this->db->select('*');
				$this->db->from('reseller');
				$this->db->where('username', $userid);
				 //$this->db->where('user_is_active!=0');
				$query=$this->db->get();

				if($query->num_rows()>0) {

				$row=$query->row();
				$hash=$row->pincode;

				$r=$this->verify($password,$hash);

				if($r==true) {
				return $query->row();
				}
				}else{return false;}
		}



	public function insertLog($ucid,$ctype,$secretcode,$log_type="") {

					$browser=$this->getBrowser();
					$bname=$browser['name'];
					$version=$browser['version'];
					if($log_type=="apps") {
					$win="Android";
					}else {
					$win=$browser['platform'];
					}
					if($log_type=="apps") {
					$bd="Apps";
					}else {
					$bd="$bname,$version";
					}


					$data=array();
					$data['ucid']=$ucid;
					$data['ctype']=$ctype;
					$data['log_time']=$this->date_time();
					$data['log_date']=date("Y-m-d");
					$data['secretcode']=$secretcode;
					$data['browser']=$bd;
					$data['log_type']=$log_type;

					$data['tokenid']= $this->security->get_csrf_hash();

					$data['platform']=$win;
					//$location = new SimpleXMLElement($this->get_data('http://freegeoip.net/xml/' . $_SERVER["REMOTE_ADDR"]));
					//$data['country'] = $location->City . ' , ' . $location->CountryName;
					$data['ip'] = $_SERVER['REMOTE_ADDR'];

					$this->db->insert('userlog',$data);
			}

		public function Pagegnation($total_pages="",$limit="",$page="",$targetpage="") {


						 /* Setup page vars for display. */
		  if ($page == 0) $page = 1;          //if no page var is given, default to 1.
		  $prev = $page - 1;              //previous page is page - 1
		  $next = $page + 1;              //next page is page + 1
		  $lastpage = ceil($total_pages/$limit);    //lastpage is = total pages / items per page, rounded up.
		  $lpm1 = $lastpage - 1;            //last page minus 1

		  /*
			Now we apply our rules and draw the pagination object.
			We're actually saving the code to a variable in case we want to draw it more than once.
		  */
		  $pagination = "";
		  if($lastpage > 1)
		  {
			$pagination .= "<div class=\"pagination\">";
			//previous button
			if ($page > 1)
			  $pagination.= "<a href=\"$targetpage/$prev\">previous</a>";
			else
			  $pagination.= "<span class=\"disabled\">previous</span>";

			//pages
			if ($lastpage < 7 + ($adjacents * 2))  //not enough pages to bother breaking it up
			{
			  for ($counter = 1; $counter <= $lastpage; $counter++)
			  {
				if ($counter == $page)
				  $pagination.= "<span class=\"current\">$counter</span>";
				else
				  $pagination.= "<a href=\"$targetpage/$counter\">$counter</a>";
			  }
			}
			elseif($lastpage > 5 + ($adjacents * 2))  //enough pages to hide some
			{
			  //close to beginning; only hide later pages
			  if($page < 1 + ($adjacents * 2))
			  {
				for ($counter = 1; $counter < 4 + ($adjacents * 2); $counter++)
				{
				  if ($counter == $page)
					$pagination.= "<span class=\"current\">$counter</span>";
				  else
					$pagination.= "<a href=\"$targetpage/$counter\">$counter</a>";
				}
				$pagination.= "...";
				$pagination.= "<a href=\"$targetpage/$lpm1\">$lpm1</a>";
				$pagination.= "<a href=\"$targetpage/$lastpage\">$lastpage</a>";
			  }
			  //in middle; hide some front and some back
			  elseif($lastpage - ($adjacents * 2) > $page && $page > ($adjacents * 2))
			  {
				$pagination.= "<a href=\"$targetpage/1\">1</a>";
				$pagination.= "<a href=\"$targetpage/2\">2</a>";
				$pagination.= "...";
				for ($counter = $page - $adjacents; $counter <= $page + $adjacents; $counter++)
				{
				  if ($counter == $page)
					$pagination.= "<span class=\"current\">$counter</span>";
				  else
					$pagination.= "<a href=\"$targetpage/$counter\">$counter</a>";
				}
				$pagination.= "...";
				$pagination.= "<a href=\"$targetpage/$lpm1\">$lpm1</a>";
				$pagination.= "<a href=\"$targetpage/$lastpage\">$lastpage</a>";
			  }
			  //close to end; only hide early pages
			  else
			  {
				$pagination.= "<a href=\"$targetpage/1\">1</a>";
				$pagination.= "<a href=\"$targetpage/2\">2</a>";
				$pagination.= "...";
				for ($counter = $lastpage - (2 + ($adjacents * 2)); $counter <= $lastpage; $counter++)
				{
				  if ($counter == $page)
					$pagination.= "<span class=\"current\">$counter</span>";
				  else
					$pagination.= "<a href=\"$targetpage&page=$counter\">$counter</a>";
				}
			  }
			}

			//next button
			if ($page < $counter - 1)
			  $pagination.= "<a href=\"$targetpage/$next\">next</a>";
			else
			  $pagination.= "<span class=\"disabled\">next</span>";
			$pagination.= "</div>\n";
		  }


			return  $pagination;

		}





		public function get_data($url) {
				$ch = curl_init();
				$timeout = 5;
				curl_setopt($ch, CURLOPT_URL, $url);
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
				curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
				$data = curl_exec($ch);
				curl_close($ch);
				return $data;
			}

		public function dayinMonth($intmonth){

			if($intmonth==01){
			$month="January";
			}else if($intmonth=="02"){
			$month="February";
			}else if($intmonth=="03"){
			$month="March";
			}else if($intmonth=="04"){
			$month="April";
			}else if($intmonth=="05"){
			$month="May";
			}else if($intmonth=="06"){
			$month="June";
			}else if($intmonth=="07"){
			$month="July";
			}else if($intmonth=="08"){
			$month="August";
			}else if($intmonth=="09"){
			$month="September";
			}else if($intmonth=="10"){
			$month="October";
			}else if($intmonth=="11"){
			$month="November";
			}else{
			$month="December";
			}
			return $month;
			}


	public function getBrowser()
			{
				$u_agent = $_SERVER['HTTP_USER_AGENT'];
				$bname = 'Unknown';
				$platform = 'Unknown';
				$version= "";

				//First get the platform?
				if (preg_match('/linux/i', $u_agent)) {
					$platform = 'linux';
				}
				elseif (preg_match('/macintosh|mac os x/i', $u_agent)) {
					$platform = 'mac';
				}
				elseif (preg_match('/windows|win32/i', $u_agent)) {
					$platform = 'windows';
				}

				// Next get the name of the useragent yes seperately and for good reason
				if(preg_match('/MSIE/i',$u_agent) && !preg_match('/Opera/i',$u_agent))
				{
					$bname = 'Internet Explorer';
					$ub = "MSIE";
				}
				elseif(preg_match('/Firefox/i',$u_agent))
				{
					$bname = 'Mozilla Firefox';
					$ub = "Firefox";
				}
				elseif(preg_match('/Chrome/i',$u_agent))
				{
					$bname = 'Google Chrome';
					$ub = "Chrome";
				}
				elseif(preg_match('/Safari/i',$u_agent))
				{
					$bname = 'Apple Safari';
					$ub = "Safari";
				}
				elseif(preg_match('/Opera/i',$u_agent))
				{
					$bname = 'Opera';
					$ub = "Opera";
				}
				elseif(preg_match('/Netscape/i',$u_agent))
				{
					$bname = 'Netscape';
					$ub = "Netscape";
				}

				// finally get the correct version number
				$known = array('Version', $ub, 'other');
				$pattern = '#(?<browser>' . join('|', $known) .
				')[/ ]+(?<version>[0-9.|a-zA-Z.]*)#';
				if (!preg_match_all($pattern, $u_agent, $matches)) {
					// we have no matching number just continue
				}

				// see how many we have
				$i = count($matches['browser']);
				if ($i != 1) {
					//we will have two since we are not using 'other' argument yet
					//see if version is before or after the name
					if (strripos($u_agent,"Version") < strripos($u_agent,$ub)){
						$version= $matches['version'][0];
					}
					else {
						$version= $matches['version'][1];
					}
				}
				else {
					$version= $matches['version'][0];
				}

				// check if we have a number
				if ($version==null || $version=="") {$version="?";}

				return array(
					'userAgent' => $u_agent,
					'name'      => $bname,
					'version'   => $version,
					'platform'  => $platform,
					'pattern'    => $pattern
				);
			}


			public static function generate($password, $salt = null, $iterations = 10000, $hash_function = 'sha1', $secret = '')
		  {
			$salt or $salt = self::generateToken();
			$hashes = array();
			$hash = $password;
			// hash a sequence of hashes, each hash depends on the last one, so any implementation must hash each one individually
			$i = $iterations;
			while(--$i)
			{
			  $hash = $hash_function($hash.$salt.$secret);
			}
			return implode(':', array($hash, $iterations, $hash_function, $salt));
		  }

		  /**
		   * Verify a password meets a hash
		   * @return Bool
		   * @param $password String
		   * @param $hash String
		   * @param $secret String[optional]
		   */
		  public static function verify($password, $hash, $secret = '')
		  {
			list($_hash, $iterations, $hash_function, $salt) = explode(':', $hash);
			return ($hash == self::generate($password, $salt, $iterations, $hash_function, $secret));
		  }

			  /**
			   * Generate a random hex based token
			   * @return String
			   * @param $length Int[optional]
			   */
			  public static function generateToken($length = 40)
			  {
				$token = array();
				for( $i = 0; $i < $length; ++$i )
				{
				  $token[] =  dechex( mt_rand(0, 15) );
				}
				return implode('', $token);
			  }






}// Mitload_model