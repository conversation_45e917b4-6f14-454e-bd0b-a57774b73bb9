
<?php
defined('BASEPATH') OR exit('No direct script access allowed');
function numberv($msisdn,$type="")
{ if($type!='BILL'){
  $msisdn = trim(preg_replace("/[^0-9]+/", "", $msisdn));
  $msisdn = preg_replace("/^(00)?(88)?0/", "", $msisdn);
  if (strlen($msisdn) != 10
    || strncmp($msisdn, "1", 1)
    != 0)
    return false;
 

$msisdn = "880" . $msisdn;
}
  return $msisdn;
}

class Offline extends CI_Controller {
	
	function __construct()
    {
        parent::__construct();
        
        date_default_timezone_set('Asia/Dhaka');
     
		$this->load->library('form_validation');
		$this->load->library('user_agent');
    }
	
	 private function date_time(){
        return $create_date = date('Y-m-d H:i:s');
     }
	 

	
	
	
	public function index()
	{
	
	$dt = new DateTime('now', new DateTimezone('Asia/Dhaka')); 
	$last_login=$dt->format('j F Y g:i A'); 
	$idate=$dt->format('Y-m-d');

	if($_POST) {
	$this->form_validation->set_rules('username', 'Userid', 'trim|required|min_length[3]|max_length[30]');
	$this->form_validation->set_rules('password', 'Password', 'trim|required|min_length[4]|max_length[30]');
   
     
        if ($this->form_validation->run() == FALSE) {
		
		
		 $response = array(
					'success' => true,
					'errors'  => 'not empty',
					'message' => 'Please Enter Correct Login Info.'
					);
					
		}else {
			
	$username = $this->security->xss_clean($this->input->post('username'));
	$password = $this->security->xss_clean($this->input->post('password'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	
	
	if(!empty($username) && !empty($password) && !empty($deviceid) ) {
	//$this->load->model('company');
        $result = $this->mit->login_by_pin($username,$password);
		 if ($result) {
		 $ucid=$result->id;
		 $logincount=$result->login_count;
		 
		$activests =$result->status;
		$uname =$result->username;
		$custype =$result->custype;
		$appsAccess=$result->appsAccess;
		 
		$loginplus = $logincount+1;
		 
		
		
		$sqlcont="SELECT * FROM device_list WHERE tokenid='$deviceid' and userid='$ucid'";

		$querycount = $this->db->query($sqlcont);
		
		foreach ($querycount->result() as $row3){
		  	 $mysafe_status=$row3->status;
		  	  $myfsttoken=$row3->tokenid;
		  	   $tblremember=$row3->remember;
		  	  
		  	  }
		
		
		if($logincount>0){

		 	$access='no';


		 }else {
		 	$access='yes';

		 }
		 

		if($mysafe_status==1 || $access=='yes') {
			 
		if($appsAccess==1) {
		 
		 if($activests==0) { 
		 
		 $response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'This ID is Block.'
					);
					
		 
		 }else {
		 
		 
		 $loginupdate = array(
						"ip" => $_SERVER['REMOTE_ADDR'],
						"login_count" => $loginplus,
						"last_login" => $this->date_time()
					);
		 
		 $this->mdb->update('reseller',$loginupdate,array('id'=>$ucid));
		 
		$ctype="user";
		$logtype="apps";
		$secretcode=sha1(uniqid(rand(1111111,9999999),true) ); 
		$this->mit->insertLog($ucid,$ctype,$secretcode,$logtype);
		//$this->session->set_userdata('member_session', $result);
		
		$msg= 'Successful Login';
		$this->mit->InsertActivetUser($ucid,$msg,'Apps');
		
		
		$query = $this->db->query("select * from apps_key where userid='$ucid'");
		$time= time();

		if($query->num_rows() == 0 ) {
		
		$applog_key_insert = "INSERT INTO `apps_key` (`id`, `userid`, `token`, `date`, `time`, `deviceid`, `status`) VALUES (NULL, '$ucid', '$secretcode', '$last_login', '$time', '$deviceid', '1')";
		
		$this->db->query($applog_key_insert);
		
		}else {
			
	
			 
			 $data = array(
					'token' => $secretcode, 
					'time' => $time,
					'deviceid' => $deviceid,
					'date' => $last_login
					);
					$this->db->where('userid',$ucid);
					
					$this->db->update('apps_key',$data);

			$bq="UPDATE `apps_key` SET `accesscount` = accesscount+1 WHERE `apps_key`.`userid` = '$ucid'"; 

			$this->db->query($bq);					
					
					//$this->mdb->update('apps_key', $apiupdate, array('userid'=>$ucid));
		}
		
		$response = array(
					'success' => true,
					'status'  => 1,
					'token' =>$secretcode, 
					'username' =>$uname,
					'otp' => 1,
					'message' => 'Successful Login.'
					);

		
		 
		 }
		 }else {
		$msg= 'Your Apps Access Not Allow';
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => $msg
					);		
			 
		 }
		 }else {
			 
		$remember=0;
      	$this->mit->devicelist($ucid,$remember,'yes',$deviceid,'Apps');
      	$msg= 'Access Denai';
		$trytype = 'password';
		
		$this->mit->increase_login_attempts($username,$password,$msg,$trytype);
			 
			 $response = array(
					'success' => false,
					'status'  => 2,
					'message' => $msg
					);
			 
		 }
		 
		 } else {
		
		$msg= 'Invalid ID and password';
		//$this->mit->InsertActivetUser($username,$msg);
		
		$this->mit->increase_login_attempts($username,$password,$msg);
		
		  $response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Invalid ID and password.'
					);
		 }
		 
		 }else {
			 $response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Please enter ID and password.'
					);
					
		 
		 }
		}
		}else {
			
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
			
			
		}
		
		
		/* Output header */ 
			header('Content-type: application/json'); 
			echo json_encode($response); 
			 
		// 
			
	}
	
	

	
	
	public function fbadd() {
	
	$var=$this->input->post();
	
	if($_POST) {
		$type = $this->security->xss_clean($this->input->post('type'));
	$username = $this->security->xss_clean($this->input->post('user'));
	$password = $this->security->xss_clean($this->input->post('pass'));
	

		    
	$result = $this->mit->login_by_pin($username,$password);
		 
	$uid = $result->id;	
	$userid=$uid;
	if(!empty($userid)) {
		$fb = $this->db->get_where('reseller',array('id' =>$userid))->row()->fb_p;
		
			$name = $this->db->get_where('reseller',array('id' =>$userid))->row()->name;
$facebook = $this->db->get_where('reseller',array('id' =>$userid))->row()->facebook;
	
	
		if($type=='add'){
	if(empty($facebook)) {
		    
		    

$facebook_id = $this->security->xss_clean($this->input->post('facebook_id'));

		$q2="UPDATE reseller SET facebook = '$facebook_id',fb_p='1' WHERE id = $userid"; 
		$this->db->query($q2);	
 
	   
	

$response = array(
				'success' => true,
					'status'  => 1,
					'message' => $name
					);


		}else{
		    
		   	$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Contact with admin.'
					); 
		    
		}	
	
	
		}  
	
	
	
	
		if($type=='off'){
	
		if(!empty($facebook)) {
		    
		    

	
		    
		    	$q2="UPDATE reseller SET facebook = null WHERE id = $userid"; 
		$this->db->query($q2);	
		    
		    
	

$response = array(
				'success' => true,
					'status'  => 1,
					'message' => $name
					);


		}else{
		    
		   	$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Already stop.'
					); 
		    
		}

	}	}else {
		
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Your Not Valid.'
					);
		
	}
	}else {
		
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
		
	}
	
	
		
	header('Content-type: application/json'); 
	echo json_encode($response); 
	}
		
	
	
	
	
	
	
	
	
	
	public function smscheck() {
		if($_POST) {
		   



		    	$to = $this->security->xss_clean($this->input->post('sender'));
	$msg = $this->security->xss_clean($this->input->post('body'));
	$gtype = $this->security->xss_clean($this->input->post('type'));

        $apiuser = "Select * from websms where body='$msg' and mobile='$to' order by id desc limit 1"; 
		
			$queryrtsloadm=$this->db->query($apiuser);

if($queryrtsloadm->num_rows()> 0) {
	       

foreach ($queryrtsloadm->result() as $user_datam)
					{

$timest=$user_datam->time;
}
}


				
		    	
$time = $timest; 
$curtime = time(); 

	$ckid = $this->db->get_where('websms',array('time' =>$curtime))->row()->id;
	
if(empty($ckid)){
if(($curtime-$time) > 30) { 
	$sql="INSERT INTO `websms` (`id`, `mobile`, `body`,`time`) VALUES (NULL, '$to', '$msg','$curtime')";
				
				$sendinsert = $this->db->query($sql);
				
$number = $this->db->get_where('reseller',array('facebook' =>$to,'fb_p' =>1))->row()->username;
  $token = $this->db->get_where('company',array('id' =>1))->row()->fb_token;
			$response = array(
					'success' => true,
					'status'  => 2,
					'message' => "sent",
					'type' => $gtype,
					'number' => $number,
					'token' => $token,
			);
		}else{
$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'not sent'
			);}
}else{
   $response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'not sent'); 
    

}
		header('Content-type: application/json'); 
		echo json_encode($response); 
		}
	}
	
		
	
	public function sms() {
	    
		    
		    
		
		    	$to = $this->security->xss_clean($this->input->get('to'));
	$msg = $this->security->xss_clean($this->input->get('message'));
		$type = $this->security->xss_clean($this->input->get('type'));
		$pin = $this->security->xss_clean($this->input->get('pa'));
		if($pin=='5463243'){
      $this->mdb->sms_send($to,$msg,$type);
        }
      $response = array(
					'success' => true,
					'status'  => 2,
					'message' => 'sent'
			);
		header('Content-type: application/json'); 
		echo json_encode($response); 
		
	}
	
	
	
	
	
	
	
	//only user check here
	public function Userchk() {
		if($_POST) {
	$this->form_validation->set_rules('username', 'Userid', 'trim|required|min_length[3]|max_length[30]');
	
        if ($this->form_validation->run() == FALSE) {
		
		
		 $response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Please Enter Correct Login Info.'
					);
					
		}else {
			
	$username = $this->security->xss_clean($this->input->post('username'));
	
	 $this->db->select('*');
            $this->db->from('reseller');
            $this->db->where('username', $username);
			$this->db->where('status',1);
            $query=$this->db->get(); 
			
			if($query->num_rows()>0) {
				$response = array(
					'success' => true,
					'status'  => 1,
					'message' => 'success'
					);
			}else {
				$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Please Enter Correct Login Info.'
					);
				
			}
	
		}
		}// post
		
		else {
			
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
			
		}
		
		
		/* Output header */ 
			header('Content-type: application/json'); 
			echo json_encode($response); 
			 
		// 
		
	}
	
	public function loginchk() {
		
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	
	$ago = time()-1800;
	
	if($_POST) {
		
	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;
	
	$stime = $this->db->get_where('apps_key',array('token' =>$apikey, 'deviceid' =>$deviceid))->row()->time;
	
	$lastlogID = $this->mit->lastlogID($userid,'user');
	
	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;
	
	if(!empty($userid)) {
	
	if($apikey==$securid) {
		if($stime>$ago) {
		
		$response = array(
					'success' => true,
					'status'  => 1,
					//'tiss'  => date('Y-m-d H:i:s',$stime),
					//'lst'  => date('Y-m-d H:i:s',time()),
					'message' => 'logged'
					);
		}else {
			$ctype="user";
		$logtype="apps";
		$secretcode=sha1(uniqid(rand(1111111,9999999),true) ); 
		$this->mit->insertLog($userid,$ctype,$secretcode,$logtype);
		
			 $data = array(
					'token' => $secretcode, 
					'time' => time(),
					'date' => $this->date_time()
					);
					$this->db->where('userid',$userid);
					
					$this->db->update('apps_key',$data);
					
			$response = array(
					'success' => false,
					'status'  => 2,
					'token' => $secretcode, 
					'message' => 'logout'
					);
		}
		
	}else {
		
		
		$ctype="user";
		$logtype="apps";
		$secretcode=sha1(uniqid(rand(1111111,9999999),true) ); 
		$this->mit->insertLog($userid,$ctype,$secretcode,$logtype);
		
		 $data = array(
					'token' => $secretcode, 
					'time' => time(),
					'date' => $this->date_time()
					);
					$this->db->where('userid',$userid);
					
					$this->db->update('apps_key',$data);

			$bq="UPDATE `apps_key` SET `accesscount` = accesscount+1 WHERE `apps_key`.`userid` = '$userid'"; 

					$this->db->query($bq);		
		
		$response = array(
					'success' => false,
					'status'  => 2,
					'token' => $secretcode, 
					'otp' => 1,
					'message' => 'logout'
					);
		
	}
	}else {
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Not Match.'
					);
		
	}
	
	}// post method
	else {
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}
	
	header('Content-type: application/json'); 
	echo json_encode($response); 
	
	}
	
	public function otp() {
		
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	
	$otppass = $this->security->xss_clean($this->input->post('otpcode'));
	
	if($_POST) {
		
	$userid = $this->db->get_where('apps_key',array('token' =>$apikey, 'deviceid' =>$deviceid))->row()->userid;
	
	$lastlogID = $this->mit->lastlogID($userid);
	
	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;
	
	$chkpass = $this->db->get_where('reseller',array('id' =>$userid))->row()->pincode;
	
	$userstatus = $this->db->get_where('reseller',array('id' =>$userid))->row()->status;
	$appsAccess = $this->db->get_where('reseller',array('id' =>$userid))->row()->appsAccess;
	
	$logincount = $this->db->get_where('reseller',array('id' =>$userid))->row()->login_count;
	
	$mysafe_status = $this->db->get_where('device_list',array('userid' =>$userid, 'tokenid' =>$deviceid))->row()->status;
	
	//$mysafe_status =$this->db->query("select * from device_list where userid='$userid' and tokenid='$deviceid' and status=1");
	
	if($logincount>0){
	$access='no';
	}else {
	$access='yes';
	}
		 
	
	if(!empty($userid)) {
		
	if($mysafe_status==1 || $access=='yes') {
		
	$usrtype ='user';
		
	$chk= $this->mit->otpchk($otppass,$usrtype,$userid); 
	if($chk) {
		$remember=1;
		$this->mit->devicelist($userid,$remember,'no', $deviceid, 'Apps');
		//$this->mit->clear_login_attempts($username);
		if($userstatus==1) {
			
		if($appsAccess==1) {
			
		
		$response = array(
					'success' => true,
					'status'  => 1,
					'message' => 'seccess'
					);
					
		}else {
			
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Your Apps Access Is Block.'
					);
			
		}
		
	}else {
			
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Your Account Is Block.'
					);
			
	}
	}else {
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Otp Not Valid.'
					);
		
	}
	
	// devid verify
	}else {
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Your Device ID Not Allow.'
					);
	$remember=0;
    $this->mit->devicelist($userid,$remember,'yes',$deviceid,'Apps');
		
	}
	// user chk
	}else {
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Username Not Match.'
					);
		
	}
		
		
	}else {	
	$response = array(
		'success' => false,
		'status'  => 2,
		'message' => 'Empty Not Work.'
		);
	}
	
	header('Content-type: application/json'); 
	echo json_encode($response); 	
	}
	
	public function balance() {
		if($_POST) {
		    	$username = $this->security->xss_clean($this->input->post('username'));
	$password = $this->security->xss_clean($this->input->post('password'));

		    
	$result = $this->mit->login_by_pin($username,$password);
		 
	$uid = $result->id;

	
	$apiallow = $this->db->get_where('reseller',array('username' =>$username))->row()->api;
	
	if(!empty($uid)) {
		
	
	
	$balance = $this->db->get_where('reseller',array('username' =>$username))->row()->balance;
      $bank = $this->db->get_where('reseller',array('username' =>$username))->row()->bank_balance;
      $drive = $this->db->get_where('reseller',array('username' =>$username))->row()->drive_bal;
				$name = $this->db->get_where('reseller',array('username' =>$username))->row()->name;

			$response = array(
					'success' => true,
					'status'  => 1,
					'balance' =>''.$name.' Your account main balance '.$balance.', bank balance '.$bank.', drive balance '.$drive.' Thank you'
					);
	
	
		
		
		} //empty
		else {
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Not User Found'
			);
		}
		
		header('Content-type: application/json'); 
		echo json_encode($response); 
		}
	}
	
	public function role() {
	
	
	if($_POST) {
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	
	$userid = $this->db->get_where('apps_key',array('token' =>$apikey, 'deviceid' =>$deviceid))->row()->userid;
	
	$lastlogID = $this->mit->lastlogID($userid);
	
	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;
	
	if(!empty($userid)) {
	
	 
	$types = $this->db->get_where('reseller',array('id' =>$userid))->row()->type;
	
			  $values = $this->mit->valuesOnBinPosition($types);
            foreach ($this->mit->valuesOnBinPosition($types) as $Key => $Values):
                $client_typ[$Key] = $Values;
            endforeach;
			
		$response = array();
			
		$sql="SELECT * from module where id>=0 and 5<id and status='1' order by sorder asc"; 
		$query = $this->db->query($sql);
		foreach ($query->result() as $row3){
		$portid=$row3->id;
		$titleop=$row3->title;
		$serviceid=$row3->serviceid;
		$shortid=$row3->order;
		
		$serviceid%2; 
		
		if ($client_typ[$portid] == $serviceid) { 
		$json_data = array('success' => true,
					'status'  => 1,
					'serviceID' => $serviceid,
					'serviceName' => $titleop
					); 
		array_push($response, $json_data); }
		}
		
	}else {
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Not Match.'
					);
		
	}
	}else {
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}
	header('Content-type: application/json'); 
	echo json_encode($response); 
	}
	
	public function password() {
		
	if($_POST) {
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	$oldpass = $this->security->xss_clean($this->input->post('oldpass'));
	$newpass = $this->security->xss_clean($this->input->post('newpass'));
	$cnewpass = $this->security->xss_clean($this->input->post('cnewpass'));
	
	
	$userid = $this->db->get_where('apps_key',array('token' =>$apikey, 'deviceid' =>$deviceid))->row()->userid;
	
	$lastlogID = $this->mit->lastlogID($userid);
	
	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;
	
	$chkpass = $this->db->get_where('reseller',array('id' =>$userid))->row()->password;
	
	if(!empty($userid)) {
		
	$chk= $this->mdb->verify($oldpass,$chkpass); 
	if($chk==true) {
		
		if(!empty($newpass) && $newpass==$cnewpass){
		$this->mdb->update('reseller',array('password'=>$this->mdb->generate($newpass)),array('id'=>$userid));
		
		$response = array(
					'success' => true,
					'status'  => 1,
					'message' => 'Password Change Successful'
					);
		}else {
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Confirm Password Not Match.'
					);
		}
			
		}else {
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Old Password Not Valid'
					);
		}
	}else {
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Not Match.'
					);
	}
	}else{
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}
	header('Content-type: application/json'); 
	echo json_encode($response); 
	
	}
	
	
	
	public function pin() {
		
	if($_POST) {
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	$oldpass = $this->security->xss_clean($this->input->post('oldpin'));
	$newpass = $this->security->xss_clean($this->input->post('newpin'));
	$cnewpass = $this->security->xss_clean($this->input->post('cnewpin'));
	
	
	$userid = $this->db->get_where('apps_key',array('token' =>$apikey, 'deviceid' =>$deviceid))->row()->userid;
	
	$lastlogID = $this->mit->lastlogID($userid);
	
	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;
	
	$chkpass = $this->db->get_where('reseller',array('id' =>$userid))->row()->pincode;
	
	if(!empty($userid)) {
		
	$chk= $this->mdb->verify($oldpass,$chkpass); 
	if($chk==true) {
		
		if(!empty($newpass) && $newpass==$cnewpass){
		$this->mdb->update('reseller',array('pincode'=>$this->mdb->generate($newpass)),array('id'=>$userid));
		
		$response = array(
					'success' => true,
					'status'  => 1,
					'message' => 'PIN Change Successful'
					);
		}else {
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Confirm PIN Not Match.'
					);
		}
			
		}else {
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Old PIN Not Valid'
					);
		}
	}else {
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Not Match.'
					);
	}
	}else{
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}
	header('Content-type: application/json'); 
	echo json_encode($response); 
	
	}
	
	public function RechargeHistory() {
		
		
	if($_POST) {
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	
	$userid = $this->db->get_where('apps_key',array('token' =>$apikey, 'deviceid' =>$deviceid))->row()->userid;
	$lastlogID = $this->mit->lastlogID($userid);
	
	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;
	
	$chkpass = $this->db->get_where('reseller',array('id' =>$userid))->row()->pincode;
	
	if(!empty($userid)) {
		
		$response = array();
		
		$sql="SELECT * from sendflexi where (userid='$userid' or level='$userid' or level2='$userid' or level3='$userid' or level4='$userid' or level5='$userid') order by id desc limit 20"; 
		$query = $this->db->query($sql);
		foreach ($query->result() as $user_data){
			
			$flexistatus=$user_data->status; 
			$flexilocal=$user_data->local; 

			$type=$user_data->type; 

			 $serv=$user_data->service; 
			  
			 $number=$user_data->phone; 
			  
			 $balance=$user_data->balance; 
			 $cost=$user_data->cost; 
			 $send_time_stamp=$user_data->send_time_stamp; 
			  
			 $prebalance=$user_data->prebalance; 
			 $sdate=$user_data->idate; 

			 if($flexistatus==1 or $flexilocal==1) {$sst="Success";} 

			 if ($flexistatus==0 && $flexilocal==0) {$sst="Pending";} 
			 if($flexistatus==3) {$sst="Cancel";} 
			  if($flexistatus==2) {$sst="Fail";} 
			 if($flexistatus==4) {$sst="Proces";} 
			 if($flexistatus==5) {$sst="Wait";} 
			 if($serv=='64' or $serv=='16' or  $serv=='512' or $serv=='16384') { 
			 if($type==1) {$ot="Prepaid";} else {$ot="PostPaid";} 
			}else { 
			if($type==1) {$ot="Personal";} else {$ot="Agent";} 
				  } 
				  
			$sertitle = $this->db->get_where('module',array('serviceid' =>$serv))->row()->title;
			
			 $dt= explode("-", $sdate);
 
			 $intmonth= $dt[1];
			 $datset = $dt[2];
			 
			 if($intmonth==01){
			$month="Jan";
			}else if($intmonth=="02"){
			$month="Feb";
			}else if($intmonth=="03"){
			$month="Mar";
			}else if($intmonth=="04"){
			$month="Apr";
			}else if($intmonth=="05"){
			$month="May";
			}else if($intmonth=="06"){
			$month="Jun";
			}else if($intmonth=="07"){
			$month="July";
			}else if($intmonth=="08"){
			$month="Aug";
			}else if($intmonth=="09"){
			$month="Sep";
			}else if($intmonth=="10"){
			$month="Oct";
			}else if($intmonth=="11"){
			$month="Nov";
			}else{
			$month="Dec";
			}
		
		$dtsss = $datset ." ".$month;
		
	  $json_data = array( 

           // 'username' => $uname, 
            'number' => $user_data->phone, 
            'balance' => $user_data->balance, 
            'cost' => $user_data->cost, 
            'prebalance' => $user_data->prebalance, 
            'serviceName' => $sertitle, 
            'type' => $ot, 
            'status' => $sst, 
            'trxID' => $user_data->trxid, 
            'time' => $dtsss 
        ); 
        array_push($response, $json_data); 
		}
		
	}else {
	$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Not Match.'
					);
		
	}
	
	}else {
	$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
		
	}
		
	header('Content-type: application/json'); 
	echo json_encode($response); 
		
		
	}

	public function NewRequest()
	{

	if($_POST) {
	$username = $this->security->xss_clean($this->input->post('username'));
	$password = $this->security->xss_clean($this->input->post('password'));

	$serviceget = $this->security->xss_clean($this->input->post('service'));
	$number = $this->security->xss_clean($this->input->post('number'));
	$type = $this->security->xss_clean($this->input->post('type'));
	$amount = $this->security->xss_clean($this->input->post('amount'));
	$optn = $this->security->xss_clean($this->input->post('optn'));
	$req_type = $this->security->xss_clean($this->input->post('req_type'));

		

$result = $this->mit->login_by_pin($username,$password);
		 
	$uid = $result->id;

$userid=$uid;

	
	$parent = $this->db->get_where('reseller',array('id' =>$userid))->row()->p_id;
	$prebalance = $this->db->get_where('reseller',array('id' =>$userid))->row()->balance;

	$mybalance = $this->db->get_where('reseller',array('id' =>$userid))->row()->balance;
	$balancelimit = $this->db->get_where('reseller',array('id' =>$userid))->row()->balance_limit;
	
	$appsAccess = $this->db->get_where('reseller',array('id' =>$userid))->row()->appsAccess;
	$userstatus = $this->db->get_where('reseller',array('id' =>$userid))->row()->status;


	if(!empty($userid)) {
		

	
	if($appsAccess==1) {
		
	if($userstatus==1) {

	
	$this->irs->recharge($serviceget,$number,$amount,$type,$uid,$password,'yes',$country,$opid,$req_type,$name,$nidcode,$senderno,$optn);
		$getirdata= $this->session->userdata('irslog');
		$irstsview = $getirdata['irsts'];
		$irsmessage = $getirdata['irsmessage'];
		
		if($irstsview=="error") {
			$response = array(
		'success' => false,
		'status'  => 2,
		'message' => $irsmessage	);
		}else {
		$response = array(
		'success' => true,
		'status'  => 1,
		'message' => $irsmessage
		);
			
		}
		
		} //block
		else {
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Your Account is Block'
					);
		}
		
		} //block
		else {
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Your Apps Access is Block'
					);
		}
		
		 //device id block
		
			

		} //empty
		else {
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Not Match'
					);
		}


		} // post
		else {

			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty not work'
					);


		}

		header('Content-type: application/json'); 
		echo json_encode($response); 
		
	}
	
	// reseller list here
	
	
	public function reseller() {
		
		
	if($_POST) {
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	
	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;
	$lastlogID = $this->mit->lastlogID($userid);
	
	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;
	
	$chkpass = $this->db->get_where('reseller',array('id' =>$userid))->row()->pincode;
	
	if(!empty($userid)) {
		
		
		$response = array();
		
		$lr=$this->mit->leftRight($userid); 
		$lft=$lr[0]; 
		$rgt=$lr[1];
		
		$sql="select * from reseller where p_id='$userid' and status!=2 order by id desc"; 
		$query = $this->db->query($sql);
		foreach ($query->result() as $user_data){
			
			$reselerstatus=$user_data->status; 
			$flexilocal=$user_data->local; 

			$type=$user_data->type; 

			$serv=$user_data->service; 
			  
			$p_id=$user_data->p_id; 
			  
			 $balance=$user_data->balance; 
			 $cost=$user_data->cost; 
			 $send_time_stamp=$user_data->send_time_stamp; 
			  
			 $prebalance=$user_data->prebalance; 
			 $sdate=$user_data->idate; 

			
			
			 if($reselerstatus==1) {$reslsts="active";} else {$ot="lock";} 
		
				  
			$prantname = $this->db->get_where('reseller',array('id' =>$p_id))->row()->username;
	
			$json_data = array( 
					'username' => $user_data->username, 
					'balance' => $user_data->balance, 
					'parent' => $prantname, 
					'lastlogin' => $user_data->last_login, 
					'resellerstatus' => $reslsts, 
					'type' => $user_data->custype,  
					'createdate' => $user_data->create_date,
				); 
			array_push($response, $json_data); 
			}
		
	}else {
	$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Not Match.'
					);
		
	}
	
	}else {
	$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
		
	}
		
	header('Content-type: application/json'); 
	echo json_encode($response); 
		
		
	}
	
	
	/// reseller list finish
	public function reselleradd() {
	
	$var=$this->input->post();
	
	if($_POST) {
	
	$username = $this->security->xss_clean($this->input->post('user'));
	$password = $this->security->xss_clean($this->input->post('pass'));
$postlevel = $this->security->xss_clean($this->input->post('level', true));
		    
	$result = $this->mit->login_by_pin($username,$password);
		 
	$uid = $result->id;	
	$userid=$uid;
if(!empty($userid)) {
	    
	 
	$chk_data=$this->mdb->getData('reseller',array('id'=>$userid));
				$sendername=$chk_data[0]['username'];
				$uparent=$chk_data[0]['p_id'];
				$ctype=$chk_data[0]['custype'];
				$myLeft=$chk_data[0]['lft'];
				$myRight=$chk_data[0]['rgt'];
      $mytype=$chk_data[0]['type'];
				$uid=$userid;
				$ressoff = $chk_data[0]['reseller'];
	$cbalance=$chk_data[0]['balance'];
	$balancelimit=$chk_data[0]['balance_limit'];

	
		$availablebal=$cbalance-$balancelimit;
		
      $postlevel=strtolower($postlevel);
		if($postlevel=='house'){$custype='reseller5';}
				else if($postlevel=='dgm'){$custype='reseller4';}
					else if($postlevel=='dealer'){$custype='reseller3';}
				else if($postlevel=='seller'){$custype='reseller2';}
				else if($postlevel=='retailer'){$custype='reseller1';}else{ 
				
				$custype=$postlevel;
				}
		    
			$mylevel=substr($ctype, -1);
if($ctype=="subadmin" or $ctype=="Subadmin"){$mylevel=6;}
$postlevel=substr($custype, -1);
			
			if($mylevel>$postlevel){$custype="reseller$postlevel";}else{
		if($ctype=="subadmin"){$custype="reseller5";} 
		if($ctype=="reseller5"){$custype="reseller4";} 
		if($ctype=="reseller4"){$custype="reseller3";} 
		if($ctype=="reseller3"){$custype="reseller2";} 
		if($ctype=="reseller2"){$custype="reseller1";}
			}
      
      
      
      
			  $sql_rate="SELECT * from level_list where name='$custype'"; 
		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{
				$amount=$raterow->account;	    
					    
}
	$idate=date('Y-m-d');
	$create_date=date('j F Y g:i A'); 
	$ip = $_SERVER['REMOTE_ADDR']; 	
				
	// parent 
		
	$parent2 = $this->db->get_where('reseller',array('id' =>$uid))->row()->p_id;
	$parent3 = $this->db->get_where('reseller',array('id' =>$parent2))->row()->p_id;
	$parent4 = $this->db->get_where('reseller',array('id' =>$parent3))->row()->p_id;
	$parent5 = $this->db->get_where('reseller',array('id' =>$parent4))->row()->p_id;
		
	// parent 
	
	
	$username = $this->security->xss_clean($this->input->post('username', true));
	
	$name = $this->security->xss_clean($this->input->post('name', true));

$mobile = $this->security->xss_clean($this->input->post('phone', true));
$email = $this->security->xss_clean($this->input->post('email', true));

	$password = $this->security->xss_clean($this->input->post('password', true));
	$pin = $this->security->xss_clean($this->input->post('resellerpin', true));
	
	$client_types = $this->security->xss_clean($this->input->post('client_types', true));
		
	$client_typess = $mytype;
	
	$this->form_validation->set_rules('username', 'Username', 'is_unique[reseller.username]|trim|required|xss_clean|min_length[5]');
		
	$this->form_validation->set_rules('name', 'Name', 'trim|required|xss_clean|min_length[4]');
	
	
	//$this->form_validation->set_rules('client_types[]', 'Reseller Permission', 'trim|required');
	
	if ($this->form_validation->run() == FALSE) {
		$err=validation_errors();	
      $err = str_replace('<p>','',$err);
$err = str_replace('</p>','',$err);
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => $err
					);
		
		
	}else {
	   	if($ressoff=='1' && $ctype!="reseller1"){ 
	    if($availablebal>=$amount && is_numeric($amount)){
		
	$chtype = 'user';
	//$chking= $this->mit->otpchk($nwpass,$chtype); 
	$chking=4;


	if($chking) {
				
		
			$client_types = 0; 
		
		if (!empty($mytype)): 
		foreach ($mytype as $client_types): 
        $add_type_values+= $client_types; 
		endforeach; 
		endif; 
		
		$userpl=strlen($username); 
		$pass=strlen($password);

		$q2="UPDATE reseller SET rgt = rgt + 2 WHERE rgt > $myLeft"; 
		$this->db->query($q2);	

		$q3="UPDATE reseller SET lft = lft + 2 WHERE lft > $myLeft"; 
		$this->db->query($q3);


		$left=$myLeft +1; 
		$right=$myLeft + 2; 
		$hash = $this->mdb->generate($password); 
		$hashpin = $this->mdb->generate($pin);
		
		$rlastids = $this->mit->lastUserID();
		 $rlastid = $rlastids+1;

		$sqltarif="INSERT INTO `tarif` (`id`, `make_by`, `name`, `desc`, `userid`, `duble`, `date`, `time`) VALUES (NULL, '$uid', '$username Tarif', '$add_type_values', '$rlastid', '$duble', '$idate', '$create_date');"; 
		
		$this->db->query($sqltarif);
		
		$tarifidlast = $this->db->insert_id();
		$otp = 0;
		
		$sql="INSERT INTO `reseller` (`id`, `username`, `password`, `pincode`, `type`, `name`, `mobile`, `email`, `note`, `custype`, `p_id`, `admin_id`, `lft`, `rgt`, `balance`, `balance_limit`, `create_date`, `ip`, `enbale_otp`, `tarif`, `oversale`, `status`, `level5`, `level4`, `level3`, `level2`, `level1`, `webAccess`, `appsAccess`,`new_account_price`) VALUES (NULL, '$username', '$hash', '$hashpin', '$client_typess', '$name', '$mobile', '$email', '', '$custype', '$uid', '$admin_id', '$left','$right', '', '', '$create_date', '$ip', '$otp', '$tarifidlast', '0', '1', '$uid', '$parent2', '$parent3', '$parent4', '$parent5', '1', '1','$amount');";
		
		$this->db->query($sql);
		$idlast = $this->db->insert_id();
	
		$msg= 'Add Reseller '.$username.' With Apps';
		$this->mit->InsertActivetUser($uid,$msg);
		
		
	
		$sql33="SELECT * from rate_module where userid='$uid'"; 
		$query33=$this->db->query($sql33);
		
		foreach ($query33->result() as $rowrat)
					{
						
		$opname=$rowrat->service; 
		 
		$country=$rowrat->country; 
		 
		$operator=$rowrat->oparetor; 
		$phonecode=$rowrat->c_code; 
		$opcode=$rowrat->opcode; 
		 
		$priceid=$rowrat->id; 
		 
		$prefix=$rowrat->prefix; 
		$rate=$rowrat->rate; 
		 
		$pcode=$rowrat->pcode; 
		 
	if($opname=='262144'){
		     $comm='0';
		 }else{
		     $comm=$rowrat->comm; 
		}
		$charge=$rowrat->charge; 
		$type=$rowrat->type; 
		 
		$sql_rate="INSERT INTO `rate_module` (`id`, `country`, `c_code`, `oparetor`, `opcode`, `tarif`, `userid`, `create_by`, `service`, `prefix`, `pcode`, `type`, `rate`, `comm`, `charge`, `price_id`, `status`, `date`) VALUES (NULL, '$country', '$phonecode', '$operator', '$opcode', '$tarifidlast', '$idlast', '$uid', '$opname', '$prefix', '$pcode', '$type', '$rate', '$comm', '$charge', '$priceid', '1', '$create_date')";
		$this->db->query($sql_rate);
		
					}
		
			$prebalance= $this->mit->accountBalance($uid); 
	
	$tm="minus";
		$this->mit->balanceUpdate($uid, $amount,$tm); 
		$bal= $this->mit->accountBalance($uid); 
	
		  $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `date`, `time`) VALUES (NULL, '$uid', 'new account for $username', '$prebalance', '$amount', '', '$bal', 'minus', '$idate', '$create_date')"; 
		  $this->db->query($sql_tr);	

		$sql="INSERT INTO `pay_receive` (`id`, `userid`, `desc`, `remark`, `sender`, `debit`, `account`, `type`, `transfer_type`, `idate`, `date`) VALUES (NULL, '$idlast', 'new account for $username', 'By $username', '$uid', '$amount', '$bal', 'new_account', 'recharge', '$idate', '$create_date') 
		"; 
		$this->db->query($sql);	
       $company = $this->db->get_where('company',array('id' =>'1'))->row()->company_name;
 
      
			
		$response = array(
					
					'success' => true,
					'status'  => 1,
					'message' => ' Reseller Added Successfully.'
					);
		
	}else {
		$response = array(
					'success' =>true,
					'status'  => 1,
					'message' => ' Reseller Added Successfully.'
					);

	}
		
	   	}else{$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Need Balance '.$amount.' for new account.'
					);} 
	}else{	$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Your have no permission.'
					);}   
	    
	    
	}
	}else {
		
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Your Not Valid.'
					);
		
	}
	}else {
		
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
		
	}
	
	
		
	header('Content-type: application/json'); 
	echo json_encode($response); 
	}
	
	
	

	
	
	public function paymentlist(){
		
	
	
		
	if($_POST) {
	
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	
	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;
	
	
	if(!empty($userid)) {
		
	$sql="select * from pay_receive where sender='$userid' ORDER BY id desc"; 
	
	
	$query2 = $this->db->query($sql);
	
	$response = array();
		
	foreach($query2->result() as $user_data) {
		
		$payuser = $user_data->userid;
		
		$payusername = $this->db->get_where('reseller',array('id' =>$payuser))->row()->username;
		
		$jsondata = array( 
            'date' => $user_data->date, 
			'member' => $payusername,			
            'type' => $user_data->type,
			'desc' => $user_data->desc,
            'debit' => $user_data->debit, 
            'credit' => $user_data->credit,
            'balance' => $user_data->account
        ); 
        array_push($response, $jsondata); 
			
	}
		
	}else {
		
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Your Not Valid.'
					);
			
	}
		
	}else {
		
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty Not Allow.'
					);
					
	}
					
	header('Content-type: application/json'); 
	echo json_encode($response); 
		
	}
	
	
	
	public function receivelist(){
		
	
	
		
	if($_POST) {
	
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	
	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;
	
	if(!empty($userid)) {
		
	$sql="select * from pay_receive where userid='$userid' ORDER BY id desc"; 
	
	
	$query2 = $this->db->query($sql);
	
	$response = array();
		
	foreach($query2->result() as $user_data) {
		
		
		
		$jsondata = array( 
            'date' => $user_data->date, 		
            'type' => $user_data->type,
			'desc' => $user_data->desc,
            'debit' => $user_data->debit, 
            'credit' => $user_data->credit,
            'balance' => $user_data->account
        ); 
        array_push($response, $jsondata); 
			
	}
		
	}else {
		
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Your Not Valid.'
					);
			
	}
		
	}else {
		
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty Not Allow.'
					);
					
	}
					
	header('Content-type: application/json'); 
	echo json_encode($response); 
		
	}
	
	public function reports(){
	
	$dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
	$create_date=$dt->format('Y-m-d H:i:s');
	$idate = $dt->format('Y-m-d');
	
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	
	$date = $this->security->xss_clean($this->input->post('date'));
	
	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;
	
	if(!empty($userid)) {
	
	$response = array();
	
	$sql="SELECT * from module where status=1 order by sorder asc"; 
    $query = $this->db->query($sql);
    foreach ($query->result() as $row)
          {
    $portid=$row->id; 
    $title_op=$row->title;  
    $serviceidoo=$row->serviceid;
	
	$query1 = "SELECT SUM(balance) as num FROM sendflexi WHERE service='$serviceidoo' and idate='$idate' and status='1' and (userid='$userid' or level='$userid' or level2='$userid' or level3='$userid' or level4='$userid' or level5='$userid') "; 

	$query = $this->db->query($query1);
	   foreach ($query->result() as $total_pages1){ 
	  $Flexiload = $total_pages1->num; 
	}

	$from1=$idate;
	$to1=$idate;
	$fuu_report=$this->mit->reportDetails($serviceidoo,$uid,$from1,$to1);
	if($Flexiload>0) {
	  
	  echo "<tr><td>$title_op</td><td class='tk'>$Flexiload</td> <td class='tk'> $fuu_report</td></tr>";
	  
	  $jsondata = array( 
            'service' => $title_op,
			'serviceID' => $serviceidoo, 			
            'amount' => $Flexiload,
			'cost' => $fuu_report
        ); 
        array_push($response, $jsondata); 
			
	 
	  }
  
	} /// service lsit finish
	
	}else {
		
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Your Not Valid.'
					);
			
	}
	
	header('Content-type: application/json'); 
	echo json_encode($response); 
			
	}
	
	public function myrate(){
		
	
	
		
	if($_POST) {
	
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	
	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;
	
	if(!empty($userid)) {
		
	$myacc_tarf = $this->db->get_where('reseller',array('id' =>$userid))->row()->tarif;
	
		
	$sql="select * from rate_module where tarif='$myacc_tarf' ORDER BY id desc"; 
	
	
	$query2 = $this->db->query($sql);
	
	$response = array();
		
	foreach($query2->result() as $raterow) {
		
		$opname=$raterow->service; 
		$prefix=$raterow->prefix; 
		$rate=$raterow->rate; 
  
		$comm=$raterow->comm; 
		$type=$raterow->type; 
		$ratsts=$raterow->status;
		
	
	$titleop = $this->db->get_where('module',array('serviceid' =>$opname))->row()->title;
	
	if($ratsts==1) {$rts="Active";} else {$rts="Deactive";}
	
	if($opname=='64') {
	if($type==1) {$ot="Prepaid";} else if ($type==2) {$ot="PostPaid";} else {$ot="None";}

	}else {
	if($type==1) {$ot="CashIn";} else if ($type==2) {$ot="CashOut";} else {$ot="None";}
	}
  
	$status_mud = $this->db->get_where('module',array('serviceid' =>$opname))->row()->status;
 
     
    if($status_mud==1) { 
		
		$jsondata = array( 
            'service' => $titleop, 		
            'pcode' => $raterow->pcode,
			'prefix' => $raterow->prefix,
			'rate' => $raterow->rate,
            'comm' => $raterow->comm, 
            'charge' => $raterow->charge,
            'status' => $rts
        ); 
        array_push($response, $jsondata); 
			
	}
	}
		
	}else {
		
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Your Not Valid.'
					);
			
	}
		
	}else {
		
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty Not Allow.'
					);
					
	}
					
	header('Content-type: application/json'); 
	echo json_encode($response); 
		
	}
	
	
	public function myResellerRates(){

	if($_POST) {
	
	$apikey = $this->security->xss_clean($this->input->post('token'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	$resid = $this->security->xss_clean($this->input->post('resellerid'));
	
	$userid = $this->db->get_where('apps_key',array('token' =>$apikey))->row()->userid;
	
	if(!empty($userid)) {
		
	$myacc_tarf = $this->db->get_where('reseller',array('id' =>$resid))->row()->tarif;
	
		
	$sql="select * from rate_module where tarif='$myacc_tarf' ORDER BY id desc"; 
	
	
	$query2 = $this->db->query($sql);
	
	$response = array();
		
	foreach($query2->result() as $raterow) {
		
		$opname=$raterow->service; 
		$prefix=$raterow->prefix; 
		$rate=$raterow->rate; 
  
		$comm=$raterow->comm; 
		$type=$raterow->type; 
		$ratsts=$raterow->status;
		
	
	$titleop = $this->db->get_where('module',array('serviceid' =>$opname))->row()->title;
	
	if($ratsts==1) {$rts="Active";} else {$rts="Deactive";}
	
	if($opname=='64') {
	if($type==1) {$ot="Prepaid";} else if ($type==2) {$ot="PostPaid";} else {$ot="None";}

	}else {
	if($type==1) {$ot="CashIn";} else if ($type==2) {$ot="CashOut";} else {$ot="None";}
	}
  
	$status_mud = $this->db->get_where('module',array('serviceid' =>$opname))->row()->status;
 
     
    if($status_mud==1) { 
		
		$jsondata = array( 
            'service' => $titleop, 		
            'pcode' => $raterow->pcode,
			'prefix' => $raterow->prefix,
			'rate' => $raterow->rate,
            'comm' => $raterow->comm, 
            'charge' => $raterow->charge,
            'status' => $rts
        ); 
        array_push($response, $jsondata); 
			
	}
	}
		
	}else {
		
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Your Not Valid.'
					);
			
	}
		
	}else {
		
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty Not Allow.'
					);
					
	}
					
	header('Content-type: application/json'); 
	echo json_encode($response); 
		
	}
	
	public function netSelect(){
		
		$varget=$this->input->get();
		
		$number= $varget['number'];

		$mobile=trim($number);
		
		$firstThreeDigit=substr($mobile,0,3); 

		$sql="SELECT * from net_op where prefix='$firstThreeDigit' and status='1'"; 
   
   		$query = $this->db->query($sql);
		foreach ($query->result() as $row){
   
   		$opid=$row->id;
	
		$id = $opid;
		}
		
		$response = array();
		$sql="SELECT * from net_package where op='$id' and status='1' order by id asc"; 
   
		$query = $this->db->query($sql);
		foreach ($query->result() as $row){
		
		$jsondata = array( 
            'id' => $row->id, 		
            'name' => $row->pk_name,
			'price' => $row->price
        ); 
			
		 array_push($response, $jsondata); 
		 
		}
		
		header('Content-type: application/json'); 
		echo json_encode($response); 
		
	}
	
	public function countryList(){
	
	
		$response = array();
		$sql="SELECT * FROM `country` order by id asc"; 
   
		$query = $this->db->query($sql);
		foreach ($query->result() as $row){
		
		$jsondata = array( 
            'id' => $row->id, 		
            'countryname' => $row->country_name,
        ); 
			
		 array_push($response, $jsondata); 
		 
		}
		
		
	header('Content-type: application/json'); 
		echo json_encode($response); 	
		
	}
	
	public function oparetorList(){
	
		$var=$this->input->get();

		$countryid=$var['countryid']; 
		
		$response = array();
		$sql="SELECT * FROM `country_op` where country_id='$countryid' order by id desc"; 
   
		$query = $this->db->query($sql);
		foreach ($query->result() as $row){
		
		$country_id=$row->country_id; 
		
		$country_code = $this->db->get_where('country',array('id' =>$country_id))->row()->phone;
		
		$jsondata = array( 
            'id' => $row->id, 		
            'opname' => $row->op_name,
			'country_code' => $row->country_code,
        ); 
			
		 array_push($response, $jsondata); 
		 
		}
		
		
	header('Content-type: application/json'); 
		echo json_encode($response); 	
		
	}
	
	public function billpayProvider(){
	
	
		$response = array();
		$sql="SELECT * FROM `billing_set` order by id desc"; 
   
		$query = $this->db->query($sql);
		foreach ($query->result() as $row){
		
		$jsondata = array( 
            'id' => $row->id, 		
            'name' => $row->name,
			'code' => $row->bill_code,
        ); 
			
		 array_push($response, $jsondata); 
		 
		}
		
		
	header('Content-type: application/json'); 
		echo json_encode($response); 	
		
	}
	
	public function bankList(){
	
	
		$response = array();
		$sql="SELECT * FROM `bank_name` where status=1 order by id desc"; 
   
		$query = $this->db->query($sql);
		foreach ($query->result() as $row){
		
		$jsondata = array( 
            'id' => $row->id, 		
            'name' => $row->bname,
			'code' => $row->bcode,
        ); 
			
		 array_push($response, $jsondata); 
		 
		}
		
		
	header('Content-type: application/json'); 
		echo json_encode($response); 	
		
	}
	
	
  public function payment() {
		$username = $this->security->xss_clean($this->input->post('username'));
	$password = $this->security->xss_clean($this->input->post('password'));

		    
	$result = $this->mit->login_by_pin($username,$password);
		 
	$uid = $result->id;
	$uparent = $result->p_id;
		
		$idate=date('Y-m-d');
		$create_date=date('j F Y g:i A'); 
		$ip = $_SERVER['REMOTE_ADDR']; 		
		
		  
		if($this->input->post()){
		    
		    	$apiallow = $this->db->get_where('reseller',array('id' =>$uid))->row()->api;
	
		    
		    	$username = $this->db->get_where('reseller',array('id' =>$uid))->row()->username;
	
		if(!empty($uid)) {
		    	$name = $this->db->get_where('reseller',array('username' =>$username))->row()->name;
		    
   	 	$var=$this->input->post();
		$description  = 'offline';
		$amount       = $var['amount'];
		$type         = $var['type'];
		$ucin         = $var['ucid'];
		$chk_datau=$this->mdb->getData('reseller',array('username'=>$ucin));
		$ucid=$chk_datau[0]['id'];
		$chk_data=$this->mdb->getData('reseller',array('id'=>$uid));
				$chkpass=$chk_data[0]['pincode'];
				$oversale=$chk_data[0]['oversale'];
				$cbalance=$chk_data[0]['balance'];
	
	
			
	$balancelimit=$chk_data[0]['balance_limit'];

	
		$availablebal=$cbalance-$balancelimit;
				
		$nwpass = $var['pincode'];
		$optuser = 'user';
		$chking= $this->mit->otpchk($nwpass,$optuser); 
	
		
		if($type=="Transfers") {
			
			$myacc_tarf = $this->db->get_where('reseller',array('id' =>$uid))->row()->tarif;
				$sql_rate="SELECT * from rate_module where tarif='$myacc_tarf' and service='262144' and type='1' and status=1"; 
	
		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{
						 $mrservice=$raterow->service; 
						 $prefix=$raterow->prefix; 
						 $rate=$raterow->rate; 
						 $comm=$raterow->comm; 
						 $charge=$raterow->charge; 
						 $prcode=$raterow->pcode;
					}						 
						 
						 $ratesrate=$amount*$rate; 
						 $comm_rate=$amount*$comm/100; 
						 $charge_rate=$amount*$charge/100; 
						 $final_amount=$ratesrate+$charge_rate-$comm_rate; 
				if($queryrate->num_rows() <= 0 ) {
			    $final_amount=$amount;
			    
			}
if(empty($final_amount))$final_amount=$amount;


				if($availablebal>=$final_amount && is_numeric($amount)){
	if(!empty($ucid)){
		$prebalance= $this->mit->accountBalance($ucid); 
		$t="plus";
			$tm="minus";
		$this->mit->balanceUpdate($uid, $final_amount,$tm); 

		$this->mit->balanceUpdate($ucid, $amount,$t); 

		$bal=$this->mit->accountBalance($ucid);
		$getuser = $this->db->get_where('reseller',array('id' =>$ucid))->row()->username;
 
		 $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `date`, `time`) VALUES (NULL, '$ucid', 'Deposit By $sendername', '$prebalance', '0', '$amount', '$bal', 'plus', '$idate', '$create_date');";
		 
		$this->db->query($sql_tr);

	$balm=$this->mit->accountBalance($uid);
		$sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `date`, `time`) VALUES (NULL, '$uid', 'Transfer to $sendername', '$availablebal', '$amount', '0', '$balm', 'Minus', '$idate', '$create_date');";
		 
		$this->db->query($sql_tr);




		$sql="INSERT INTO `pay_receive` (`id`, `userid`, `desc`, `remark`, `sender`, `credit`, `account`, `type`, `transfer_type`, `idate`, `date`) VALUES (NULL, '$ucid', '$description', 'By $sendername', '$uid', '$amount', '$bal', '$type', 'recharge', '$idate', '$create_date');";
		
		$this->db->query($sql);		


		$actsql ="INSERT INTO `activity_log` (`id`, `userid`, `p_id`, `secret`, `session`, `time`, `msg`, `ip`, `browser`, `os`, `date`) VALUES (NULL, '$uid', '$uparent', '', '0', '$create_date', 'Payment Add by reseller $getuser', '$ip', '', '', '$idate')"; 
		
		$this->db->query($actsql);
		$tm=($cbalance-$amount);
			 $sm="Dear $getuser Your balance update by $amount new Balance $bal";
   $msg="$getuser Dear $name Payment update successfull to $getuser by $amount new balance $tm";
			
		$response = array(
				'success' => true,
					'status'  => 1,
						'msg' =>$sm,
					'message' => $msg
					,'mobile'  => $ucin);
			}else{	$response = array(
					'success' => false,
					'status'  => 2,
					'message' => ''.$name.' Your reseller id not found.'
					); }	}else{	$response = array(
					'success' => false,
					'status'  => 2,
					'message' => ''.$name.' Payment transfer Out Of Balance.'
					);} 
	
			}else {
		/*	    
			    
		$ucid=$this->db->get_where('reseller',array('username' =>$ucin))->row()->id;
		
			$chk_data=$this->mdb->getData('reseller',array('id'=>$ucid));
				//$chkpass=$chk_data[0]['pincode'];
				//$oversale=$chk_data[0]['oversale'];
				//$cbalance=$chk_data[0]['balance'];
	
		$my_data=$this->mdb->getData('reseller',array('id'=>$uid));
				$chkpass=$my_data[0]['pincode'];
				$oversale=$my_data[0]['oversale'];
				$cbalance=$my_data[0]['balance'];
	
	
			
	$balancelimit=$my_data[0]['balance_limit'];

	
		$availablebal=$cbalance-$balancelimit;
		
		
		
		
		if(!empty($ucid)){
		
			$myacc_tarf = $this->db->get_where('reseller',array('id' =>$uid))->row()->tarif;
			
				$sql_rate="SELECT * from rate_module where tarif='$myacc_tarf' and service='262144' and type='2' and status=1"; 
	
			    
			
	
		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{
						 $mrservice=$raterow->service; 
						 $prefix=$raterow->prefix; 
						 $rate=$raterow->rate; 
						 $comm=$raterow->comm; 
						 $charge=$raterow->charge; 
						 $prcode=$raterow->pcode;
					}						 
						 
						 $ratesrate=$amount*$rate; 
						 $comm_rate=$amount*$comm/100; 
						 $charge_rate=$amount*$charge/100; 
						 $final_amount=$ratesrate+$charge_rate-$comm_rate; 
			
				if($availablebal>=$final_amount && is_numeric($amount)){
		$prebalance= $this->mit->accountBalance($ucid); 
		$t="plus";
			$tm="minus";
		$this->mit->balanceUpdate($uid, $final_amount,$tm); 

		$this->mit->balanceUpdate($ucid, $amount,$t); 

		$bal=$this->mit->accountBalance($ucid);
		$getuser = $this->db->get_where('reseller',array('id' =>$ucid))->row()->username;
 
		 $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `date`, `time`) VALUES (NULL, '$ucid', 'Deposit By $sendername', '$prebalance', '0', '$amount', '$bal', 'plus', '$idate', '$create_date');";
		 
		$this->db->query($sql_tr);

		$sql="INSERT INTO `pay_receive` (`id`, `userid`, `desc`, `remark`, `sender`, `credit`, `account`, `type`, `transfer_type`, `idate`, `date`) VALUES (NULL, '$ucid', '$description', 'By $sendername', '$uid', '$amount', '$bal', '$type', 'recharge', '$idate', '$create_date');";
		
		$this->db->query($sql);		


		$actsql ="INSERT INTO `activity_log` (`id`, `userid`, `p_id`, `secret`, `session`, `time`, `msg`, `ip`, `browser`, `os`, `date`) VALUES (NULL, '$uid', '$uparent', '', '0', '$create_date', 'Payment Add by reseller $getuser', '$ip', '', '', '$idate')"; 
		
		$this->db->query($actsql);
				
				    
				$response = array(
				'success' => 1,
					'status'  => 1,
					'message' => ''.$name.' Payment Return Successfully.'
					);
			
				}else{$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => ''.$name.' Balance low.'
					);
				    
				    
				}
			
	
	
		}else { 
			$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => ''.$name.' Account not found.'
					);
		
		}
			
	*/	}
		
		
		
	
						
			
		

		}	else{$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'invild login.'
					);}
		}else{	$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty Not Allow.'
					);}
		  
		
	header('Content-type: application/json'); 
	echo json_encode($response); 
	  }
	
	
	public function stc(){
	
	if($_POST) {
		    $username = $this->security->xss_clean($this->input->post('username'));
	$password = $this->security->xss_clean($this->input->post('password'));
	$number = $this->security->xss_clean($this->input->post('number'));

		    
	$result = $this->mit->login_by_pin($username,$password);
		 
	$uid = $result->id;
$name = $result->name;

	
	$apiallow = $this->db->get_where('reseller',array('username' =>$username))->row()->api;
	
	if(!empty($uid)) {	
	$sql="SELECT * from reseller where (p_id='$uid' or level1='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') and username='$number' order by id desc limit 1"; 
		
	
	$query2 = $this->db->query($sql);
	$cn=$query2->num_rows();

	foreach($query2->result() as $order) {
		  $stock=$order->balance_limit;
		 $idbal=$order->balance;
	}
	

	if($cn!=0){
	    $msdh="$name  Stock Balance $stock Tk.Main Balance $idbal  Thanks for your trust.";
	    
	}else{$msdh='Number Not found';}
	
	$response = array(
					'success' => true,
					'status'  => 2,
					'message' => $msdh
					);
			

		
		}else{	$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Not Match.'
					);
	    
	}
	
	
	
	
	header('Content-type: application/json'); 
		echo json_encode($response); 
	
	
		}	
	
	
	}
	
	
		public function st(){
	
	if($_POST) {
		    $username = $this->security->xss_clean($this->input->post('username'));
	$password = $this->security->xss_clean($this->input->post('password'));
	$number = $this->security->xss_clean($this->input->post('number'));
	$amount = $this->security->xss_clean($this->input->post('amount'));

		    
	$result = $this->mit->login_by_pin($username,$password);
		 
	$uid = $result->id;
	$name = $result->name;

	
	$apiallow = $this->db->get_where('reseller',array('username' =>$username))->row()->api;
	
	if(!empty($uid)) {	
	$sql="SELECT * from reseller where (p_id='$uid' or level1='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') and username='$number' order by id desc limit 1"; 
		
	
	$query2 = $this->db->query($sql);
	$cn=$query2->num_rows();

	foreach($query2->result() as $order) {
		  $balance=$order->balance_limit;
		 $id=$order->id;
	}
	 $prebalance= $this->mit->accountBalance($uid); 
	if($prebalance>=$amount && is_numeric($amount)){
	if($cn!=0){ 
	    	$rlimit = $this->db->get_where('reseller',array('id' =>$id))->row()->balance_limit;
	    	if($rlimit=='0.00'){
	$rlimit=($rlimit+$amount);
	    $loginupdate = array(
						"balance_limit" => $rlimit
					);
		
		 $this->mdb->update('reseller',$loginupdate,array('id'=>$id));
	$msdh=	"$name  balance is added Stock Balance by $number Tk. $amount  Thanks for your trust."; }else{$msdh='Account Not found';}
	}else{$msdh='No permission';}
	}else{$msdh='You have insufficient amount recharge first';}
	
	$response = array(
					'success' => true,
					'status'  => 2,
					'message' => $msdh
					);
			

		
		}else{	$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Not Match.'
					);
	    
	}
	
	
	
	
	header('Content-type: application/json'); 
		echo json_encode($response); 
	
	
		}	
	
	
	}

	

		
	public function ck(){
	
	if($_POST) {
		    $username = $this->security->xss_clean($this->input->post('username'));
	$password = $this->security->xss_clean($this->input->post('password'));
	$number = $this->security->xss_clean($this->input->post('number'));

		    
	$result = $this->mit->login_by_pin($username,$password);
		 
	$uid = $result->id;

	
	$apiallow = $this->db->get_where('reseller',array('username' =>$username))->row()->api;
	
	if(!empty($uid)) {	
	$sql="SELECT * from sendflexi where (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') and phone='$number' order by id desc limit 1"; 
		
	
	$query2 = $this->db->query($sql);
	$cn=$query2->num_rows();

	foreach($query2->result() as $order) {
		 $statuss=$order->status;
		if($statuss==1){ $status="Success";} 
		if($statuss==0){$status="Pending";} 
		if($statuss==3){$status="Cancel";} 
		  $phone=$order->phone;
		  $balance=$order->balance;
		   $send_time_stamp=$order->send_time_stamp; 
			
			$sertitle = $this->db->get_where('module',array('serviceid' =>$order->service))->row()->title;

		 $msdh="$phone-$balance-$sertitle-$status($send_time_stamp)"; 

	
	}
	
	if($cn==0){$msdh='Number Not found';}
	
	$response = array(
					'success' => true,
					'status'  => 2,
					'message' => $msdh
					);
			

		
		}else{	$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Not Match.'
					);
	    
	}
	
	
	
	
	header('Content-type: application/json'); 
		echo json_encode($response); 
	
	
		}	
	
	
	}
	
	
	
	
	
	
	
	public function rep(){
		

	
	if($_POST) {
		    	$username = $this->security->xss_clean($this->input->post('username'));
	$password = $this->security->xss_clean($this->input->post('password'));

		    
	$result = $this->mit->login_by_pin($username,$password);
		 
	$uid = $result->id;

	
	$apiallow = $this->db->get_where('reseller',array('username' =>$username))->row()->api;
	
	if(!empty($uid)) {	
	$sql="SELECT * from sendflexi where (userid='$uid' or level='$uid' or level2='$uid' or level3='$uid' or level4='$uid' or level5='$uid') order by id desc limit 3"; 
		
	
	$query2 = $this->db->query($sql);
	
	$cn=$query2->num_rows();

		
	foreach($query2->result() as $order) {
		 	 $statuss=$order->status;
		if($statuss==1){ $status="Success";} 
		if($statuss==0){$status="Pending";} 
		if($statuss==3){$status="Cancel";} 
		$phone=$order->phone;
		  $balance=$order->balance;
		  
			$sertitle = $this->db->get_where('module',array('serviceid' =>$order->service))->row()->title;

		 $msdh[]="$phone-$balance-$sertitle-$status";}
		 if($cn==0){$msdh[]="Not found";}
		 
	$response = array(
					'success' => true,
					'status'  => 2,
					'message' => $msdh
					);
			

		
	}else {
		
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Your Not Valid.'
					);
			
	}
		
	}else {
		
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty Not Allow.'
					);
					
	}
					
	header('Content-type: application/json'); 
	echo json_encode($response); 
		
	}
	
	
	
	
	public function payinfo() {
		
	if($_POST) {
	$username = $this->security->xss_clean($this->input->post('username'));
	$password = $this->security->xss_clean($this->input->post('password'));

		    
	$result = $this->mit->login_by_pin($username,$password);
		 
	$uid = $result->id;


	if(!empty($uid)) {
	    	$pallow = $this->db->get_where('reseller',array('id' =>$userid))->row()->payment;
		$sql="Select * from company where id='1'"; 
    $query = $this->db->query($sql);
	foreach ($query->result() as $row)
					{
   
   	foreach ($row as $key =>$value) 
{ 
$$key = addslashes(trim($value)); 

} 
					}	    

$cbalance = "
Bkash account no: $bkash 

Nogad account no: $nogad

Rocket account no: $rocket

Upay account no: $upay
$extrap

";

	$response = array(
					'success' => 1,
					'status'  => 1,
					'message' => $cbalance
					);
					
	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Match.'
					);
	}
			
	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}
	header('Content-type: application/json'); 
	echo json_encode($response); 
	}
	
	
	
	
	
	public function makepay() {
		
	if($_POST) {
	    
	  

	    
if($_POST) {
		    	$username = $this->security->xss_clean($this->input->post('username'));
	$password = $this->security->xss_clean($this->input->post('password'));

		    
	$result = $this->mit->login_by_pin($username,$password);
		 
	$uid = $result->id;
$name = $result->name;

	if(!empty($uid)) {
	   $userid=$uid;
	    $amount = $this->security->xss_clean($this->input->post('amount'));
	$number = $this->security->xss_clean($this->input->post('number'));
$source = strtolower($this->security->xss_clean($this->input->post('source')));
	    
	    
	$pallow = $this->db->get_where('reseller',array('id' =>$userid))->row()->payment;
	
$ctype =$this->db->get_where('reseller',array('id' =>$userid))->row()->custype;


$uid= $user['id'];
$idate=date('Y-m-d');
$ip = $_SERVER['REMOTE_ADDR']; 		
		$domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);

		$create_date=date('j F Y g:i A'); 
		 $ctrnx = $this->mdb->passwordChanger('encrypt', $number);
 $sql = "SELECT * FROM `trnx` where `trnx`='$ctrnx' and `amount`='$amount' and `status`='0'"; 
	$queryrtsloadm=$this->db->query($sql);
                      if($queryrtsloadm->num_rows()>0){
                       	
   
    

   
    $t="plus";
    	$bal=$this->mit->accountBalance($ucid);
		$idate=date('Y-m-d');
		$create_date=date('j F Y g:i A'); 
	$queryi = "UPDATE `trnx` SET `status`='1' ,`userid`='$userid' where trnx='$ctrnx' and `amount`='$amount'";
		$ife=$this->db->query($queryi);
		if($ife){


	 foreach ($queryrtsloadm->result() as $row){
   
   		$sender=$row->sender;
	
	}	
		if($sender=='16216'){
$sender="rocket";
}		

	$sql_rate="SELECT * from level_list where name='$ctype'"; 
		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{
				$p=$raterow->$sender;	    
					    
}



	
$com=(($p/100)*$amount);
$tamount=($amount+$com);
           if($source=='bank' or $source=='drive'){$tamount=$amount;}
          
  	$this->mit->balanceUpdate($userid, $tamount,$t,$source); 
  	$sql="INSERT INTO `pay_receive` (`id`, `userid`, `desc`, `remark`, `sender`, `credit`, `account`, `type`, `transfer_type`, `idate`, `date`) VALUES (NULL, '$userid', 'auto', 'By auto $number', '$userid', '$tamount', '$bal', 'receive', 'recharge', '$idate', '$create_date');";
		
		$this->db->query($sql);	
		
$bal=$this->mit->accountBalance($userid,$source); 
	
	

    
 
		if($source=='bank' or $source=='drive'){}else{
		$this->mdb->comupdate($userid,$amount,$sender);
        }
	
		
		
		
		}
	$response = array(
					'success' => 1,
					'status'  => 1,
					'message' => "$getusers Dear $name Your $source balance: $amount added, new $source Balance: $bal from: $sender trx id $number"
					);
    
}
else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Not Match.'
					);
	}
	}
			
	}else {
		$response = array(
					'success' => 0,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
	}
	header('Content-type: application/json'); 
	echo json_encode($response); 
	
	}	
	
	
	
	
	
	
	
	
	
}
	
	
	

public function getdrive2() {
	    
		$username = $this->security->xss_clean($this->input->post('username'));
	$password = $this->security->xss_clean($this->input->post('password'));

		    
	$result = $this->mit->login_by_pin($username,$password);
		 
	$uid = $result->id;
	if(!empty($uid)){

	
$sql="SELECT * from drive_package where status='1' order by id desc"; 
   $i=0;
    $query = $this->db->query($sql);
foreach ($query->result() as $row){
    
       $netid=$row->id; 
     
     $sql="SELECT * from net_op where id='$row->op'"; 
   
   		$query = $this->db->query($sql);
		foreach ($query->result() as $rowx){
   
   	
	 		$opname=$rowx->opname;
	
		}
     $i++;
     
    $titleoppck=$row->pk_name; 
     
    $netprice=$row->price; 
     
    $netcharge=$row->charge; 
    $netcomm=$row->comm; 
    $exp=$row->exp; 
	
		
$msdh[]="$i) Operator:$opname Name: $row->pk_name Price: $row->price Commision: $row->comm";

		 
	$response = array(
					'success' => true,
					'status'  => 2,
					'message' => $msdh
					);
}
}else{
    
   $response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Not Match.'
					);
 
    
}
header('Content-type: application/json'); 
	echo json_encode($response); 
		


}

public function getdrive() {
	    
		$username = $this->security->xss_clean($this->input->post('username'));
	$password = $this->security->xss_clean($this->input->post('password'));

	$result = $this->mit->login_by_pin($username,$password);
		 
	$uid = $result->id;
	if(!empty($uid)){

	$msdh[]="";
$sql="SELECT * from net_op where status='1' order by id asc"; 
   $i=0;
    $query = $this->db->query($sql);
foreach ($query->result() as $row){
  
   $firstThreeDigit=$row->prefix; 
  $opname='*'.$row->opname;
   array_push($msdh, $opname);
     if($firstThreeDigit=="017"){ 
		$operator="GP";
		}if($firstThreeDigit=="013"){ 
		$operator="GP";
		}if($firstThreeDigit=="019"){ 
		$operator="BL";
		}if($firstThreeDigit=="014"){ 
		$operator="BL";
		}if($firstThreeDigit=="018"){ 
		$operator="RB"; 
		}if($firstThreeDigit=="016"){ 
		$operator="AT"; 
		}if($firstThreeDigit=="015"){ 
		$operator="TT"; 
		}
		
      
    // echo $operator;
     $sql="SELECT * from drive_package where status='1' and volume='$operator' order by id desc"; 
   
   		$query = $this->db->query($sql);
		foreach ($query->result() as $rowx){
   
   	
	 	//$opname=$rowx->opname;
	
		
     
    $titleoppck=$rowx->pk_name; 
     
    $netprice=$rowx->price; 
     
    $netcharge=$rowx->charge; 
    $netcomm=$rowx->comm; 
    $exp=$rowx->exp; 
	
	 $i++;	
$msdh[]="$i) Name: $rowx->pk_name Price: $rowx->price Commision: $rowx->comm";
}
  
	$response = array(
					'success' => true,
					'status'  => 2,
					'message' => $msdh
					);
}
}else{
    
   $response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Not Match.'
					);
 
    
}
header('Content-type: application/json'); 
	echo json_encode($response); 
		


} 
  
 		
	
	
}



