<?php
// Include the database connection information
include 'mydata.php';

// Check if the required parameters are provided
if (isset($_POST['username']) && isset($_POST['balance'])) {
    // Sanitize input data
    $username = mysqli_real_escape_string($conn, $_POST['username']);
    $newBalance = mysqli_real_escape_string($conn, $_POST['balance']);

    // Query to update user's balance
    $query = "UPDATE reseller SET balance = '$newBalance' WHERE username = '$username'";

    // Execute the query
    $result = mysqli_query($conn, $query);

    // Check if the query was successful
    if ($result) {
        echo json_encode(['success' => true, 'message' => 'Balance updated successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Query failed: ' . mysqli_error($conn)]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
}

// Close the database connection
mysqli_close($conn);
?>
