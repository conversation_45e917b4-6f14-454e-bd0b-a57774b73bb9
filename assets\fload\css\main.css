@charset "UTF-8";
@import url("http://fonts.googleapis.com/css?family=Roboto:300,400");
@import url("http://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400");

[class$="icon-"]:before,
[class*="icon- "]:before {
    font-family:"Ding";
    font-style:normal;
    font-weight:normal;
    speak:none;
    display:inline-block;
    text-decoration:inherit;
    text-align:center;
    font-variant:normal;
    text-transform:none
}
.glyph-3icon-:before {
    content:'?'
}
.asteriskicon-:before {
    content:'?'
}
.glyphicon-:before {
    content:'?'
}
.glyph-1icon-:before {
    content:'?'
}
.glyph-2icon-:before {
    content:'?'
}
.fa {
    display:inline-block;
    font:normal normal normal 14px/1 FontAwesome;
    font-size:inherit;
    text-rendering:auto;
    -webkit-font-smoothing:antialiased;
    -moz-osx-font-smoothing:grayscale;
    transform:translate(0,
    0)
}
.fa-lg {
    font-size:1.33333333em;
    line-height:.75em;
    vertical-align:-15%
}
.fa-2x {
    font-size:2em
}
.fa-3x {
    font-size:3em
}
.fa-4x {
    font-size:4em
}
.fa-5x {
    font-size:5em
}
.fa-fw {
    width:1.28571429em;
    text-align:center
}
.fa-ul {
    padDing-left:0;
    margin-left:2.14285714em;
    list-style-type:none
}
.fa-ul>li {
    position:relative
}
.fa-li {
    position:absolute;
    left:-2.14285714em;
    width:2.14285714em;
    top:.14285714em;
    text-align:center
}
.fa-li.fa-lg {
    left:-1.85714286em
}
.fa-border {
    padDing:.2em .25em .15em;
    border:solid .08em #eee;
    border-radius:.1em
}
.pull-right {
    float:right
}
.pull-left {
    float:left
}
.fa.pull-left {
    margin-right:.3em
}
.fa.pull-right {
    margin-left:.3em
}
.fa-spin {
    -webkit-animation:fa-spin 2s infinite linear;
    animation:fa-spin 2s infinite linear
}
.fa-pulse {
    -webkit-animation:fa-spin 1s infinite steps(8);
    animation:fa-spin 1s infinite steps(8)
}
@-webkit-keyframes fa-spin {
    0% {
        -webkit-transform:rotate(0);
        transform:rotate(0)
    }
    100% {
        -webkit-transform:rotate(359deg);
        transform:rotate(359deg)
    }
}
@keyframes fa-spin {
    0% {
        -webkit-transform:rotate(0);
        transform:rotate(0)
    }
    100% {
        -webkit-transform:rotate(359deg);
        transform:rotate(359deg)
    }
}
.fa-rotate-90 {
    filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=1);
    -webkit-transform:rotate(90deg);
    -ms-transform:rotate(90deg);
    transform:rotate(90deg)
}
.fa-rotate-180 {
    filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
    -webkit-transform:rotate(180deg);
    -ms-transform:rotate(180deg);
    transform:rotate(180deg)
}
.fa-rotate-270 {
    filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
    -webkit-transform:rotate(270deg);
    -ms-transform:rotate(270deg);
    transform:rotate(270deg)
}
.fa-flip-horizontal {
    filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=0,
    mirror=1);
    -webkit-transform:scale(-1,
    1);
    -ms-transform:scale(-1,
    1);
    transform:scale(-1,
    1)
}
.fa-flip-vertical {
    filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2,
    mirror=1);
    -webkit-transform:scale(1,
    -1);
    -ms-transform:scale(1,
    -1);
    transform:scale(1,
    -1)
}
:root .fa-rotate-90,
:root .fa-rotate-180,
:root .fa-rotate-270,
:root .fa-flip-horizontal,
:root .fa-flip-vertical {
    filter:none
}
.fa-stack {
    position:relative;
    display:inline-block;
    width:2em;
    height:2em;
    line-height:2em;
    vertical-align:middle
}
.fa-stack-1x,
.fa-stack-2x {
    position:absolute;
    left:0;
    width:100%;
    text-align:center
}
.fa-stack-1x {
    line-height:inherit
}
.fa-stack-2x {
    font-size:2em
}
.fa-inverse {
    color:#fff
}
.fa-glass:before {
    content:"?"
}
.fa-music:before {
    content:"?"
}
.fa-search:before {
    content:"?"
}
.fa-envelope-o:before {
    content:"?"
}
.fa-heart:before {
    content:"?"
}
.fa-star:before {
    content:"?"
}
.fa-star-o:before {
    content:"?"
}
.fa-user:before {
    content:"?"
}
.fa-film:before {
    content:"?"
}
.fa-th-large:before {
    content:"?"
}
.fa-th:before {
    content:"?"
}
.fa-th-list:before {
    content:"?"
}
.fa-check:before {
    content:"?"
}
.fa-remove:before,
.fa-close:before,
.fa-times:before {
    content:"?"
}
.fa-search-plus:before {
    content:"?"
}
.fa-search-minus:before {
    content:"?"
}
.fa-power-off:before {
    content:"?"
}
.fa-signal:before {
    content:"?"
}
.fa-gear:before,
.fa-cog:before {
    content:"?"
}
.fa-trash-o:before {
    content:"?"
}
.fa-home:before {
    content:"?"
}
.fa-file-o:before {
    content:"?"
}
.fa-clock-o:before {
    content:"?"
}
.fa-road:before {
    content:"?"
}
.fa-download:before {
    content:"?"
}
.fa-arrow-circle-o-down:before {
    content:"?"
}
.fa-arrow-circle-o-up:before {
    content:"?"
}
.fa-inbox:before {
    content:"?"
}
.fa-play-circle-o:before {
    content:"?"
}
.fa-rotate-right:before,
.fa-repeat:before {
    content:"?"
}
.fa-refresh:before {
    content:"?"
}
.fa-list-alt:before {
    content:"?"
}
.fa-lock:before {
    content:"?"
}
.fa-flag:before {
    content:"?"
}
.fa-headphones:before {
    content:"?"
}
.fa-volume-off:before {
    content:"?"
}
.fa-volume-down:before {
    content:"?"
}
.fa-volume-up:before {
    content:"?"
}
.fa-qrcode:before {
    content:"?"
}
.fa-barcode:before {
    content:"?"
}
.fa-tag:before {
    content:"?"
}
.fa-tags:before {
    content:"?"
}
.fa-book:before {
    content:"?"
}
.fa-bookmark:before {
    content:"?"
}
.fa-print:before {
    content:"?"
}
.fa-camera:before {
    content:"?"
}
.fa-font:before {
    content:"?"
}
.fa-bold:before {
    content:"?"
}
.fa-italic:before {
    content:"?"
}
.fa-text-height:before {
    content:"?"
}
.fa-text-width:before {
    content:"?"
}
.fa-align-left:before {
    content:"?"
}
.fa-align-center:before {
    content:"?"
}
.fa-align-right:before {
    content:"?"
}
.fa-align-justify:before {
    content:"?"
}
.fa-list:before {
    content:"?"
}
.fa-dedent:before,
.fa-outdent:before {
    content:"?"
}
.fa-indent:before {
    content:"?"
}
.fa-video-camera:before {
    content:"?"
}
.fa-photo:before,
.fa-image:before,
.fa-picture-o:before {
    content:"?"
}
.fa-pencil:before {
    content:"?"
}
.fa-map-marker:before {
    content:"?"
}
.fa-adjust:before {
    content:"?"
}
.fa-tint:before {
    content:"?"
}
.fa-edit:before,
.fa-pencil-square-o:before {
    content:"?"
}
.fa-share-square-o:before {
    content:"?"
}
.fa-check-square-o:before {
    content:"?"
}
.fa-arrows:before {
    content:"?"
}
.fa-step-backward:before {
    content:"?"
}
.fa-fast-backward:before {
    content:"?"
}
.fa-backward:before {
    content:"?"
}
.fa-play:before {
    content:"?"
}
.fa-pause:before {
    content:"?"
}
.fa-stop:before {
    content:"?"
}
.fa-forward:before {
    content:"?"
}
.fa-fast-forward:before {
    content:"?"
}
.fa-step-forward:before {
    content:"?"
}
.fa-eject:before {
    content:"?"
}
.fa-chevron-left:before {
    content:"?"
}
.fa-chevron-right:before {
    content:"?"
}
.fa-plus-circle:before {
    content:"?"
}
.fa-minus-circle:before {
    content:"?"
}
.fa-times-circle:before {
    content:"?"
}
.fa-check-circle:before {
    content:"?"
}
.fa-question-circle:before {
    content:"?"
}
.fa-info-circle:before {
    content:"?"
}
.fa-crosshairs:before {
    content:"?"
}
.fa-times-circle-o:before {
    content:"?"
}
.fa-check-circle-o:before {
    content:"?"
}
.fa-ban:before {
    content:"?"
}
.fa-arrow-left:before {
    content:"?"
}
.fa-arrow-right:before {
    content:"?"
}
.fa-arrow-up:before {
    content:"?"
}
.fa-arrow-down:before {
    content:"?"
}
.fa-mail-forward:before,
.fa-share:before {
    content:"?"
}
.fa-expand:before {
    content:"?"
}
.fa-compress:before {
    content:"?"
}
.fa-plus:before {
    content:"?"
}
.fa-minus:before {
    content:"?"
}
.fa-asterisk:before {
    content:"?"
}
.fa-exclamation-circle:before {
    content:"?"
}
.fa-gift:before {
    content:"?"
}
.fa-leaf:before {
    content:"?"
}
.fa-fire:before {
    content:"?"
}
.fa-eye:before {
    content:"?"
}
.fa-eye-slash:before {
    content:"?"
}
.fa-warning:before,
.fa-exclamation-triangle:before {
    content:"?"
}
.fa-plane:before {
    content:"?"
}
.fa-calendar:before {
    content:"?"
}
.fa-random:before {
    content:"?"
}
.fa-comment:before {
    content:"?"
}
.fa-magnet:before {
    content:"?"
}
.fa-chevron-up:before {
    content:"?"
}
.fa-chevron-down:before {
    content:"?"
}
.fa-retweet:before {
    content:"?"
}
.fa-shopping-cart:before {
    content:"?"
}
.fa-folder:before {
    content:"?"
}
.fa-folder-open:before {
    content:"?"
}
.fa-arrows-v:before {
    content:"?"
}
.fa-arrows-h:before {
    content:"?"
}
.fa-bar-chart-o:before,
.fa-bar-chart:before {
    content:"?"
}
.fa-twitter-square:before {
    content:"?"
}
.fa-facebook-square:before {
    content:"?"
}
.fa-camera-retro:before {
    content:"?"
}
.fa-key:before {
    content:"?"
}
.fa-gears:before,
.fa-cogs:before {
    content:"?"
}
.fa-comments:before {
    content:"?"
}
.fa-thumbs-o-up:before {
    content:"?"
}
.fa-thumbs-o-down:before {
    content:"?"
}
.fa-star-half:before {
    content:"?"
}
.fa-heart-o:before {
    content:"?"
}
.fa-sign-out:before {
    content:"?"
}
.fa-linkedin-square:before {
    content:"?"
}
.fa-thumb-tack:before {
    content:"?"
}
.fa-external-link:before {
    content:"?"
}
.fa-sign-in:before {
    content:"?"
}
.fa-trophy:before {
    content:"?"
}
.fa-github-square:before {
    content:"?"
}
.fa-upload:before {
    content:"?"
}
.fa-lemon-o:before {
    content:"?"
}
.fa-phone:before {
    content:"?"
}
.fa-square-o:before {
    content:"?"
}
.fa-bookmark-o:before {
    content:"?"
}
.fa-phone-square:before {
    content:"?"
}
.fa-twitter:before {
    content:"?"
}
.fa-facebook-f:before,
.fa-facebook:before {
    content:"?"
}
.fa-github:before {
    content:"?"
}
.fa-unlock:before {
    content:"?"
}
.fa-credit-card:before {
    content:"?"
}
.fa-rss:before {
    content:"?"
}
.fa-hdd-o:before {
    content:"?"
}
.fa-bullhorn:before {
    content:"?"
}
.fa-bell:before {
    content:"?"
}
.fa-certificate:before {
    content:"?"
}
.fa-hand-o-right:before {
    content:"?"
}
.fa-hand-o-left:before {
    content:"?"
}
.fa-hand-o-up:before {
    content:"?"
}
.fa-hand-o-down:before {
    content:"?"
}
.fa-arrow-circle-left:before {
    content:"?"
}
.fa-arrow-circle-right:before {
    content:"?"
}
.fa-arrow-circle-up:before {
    content:"?"
}
.fa-arrow-circle-down:before {
    content:"?"
}
.fa-globe:before {
    content:"?"
}
.fa-wrench:before {
    content:"?"
}
.fa-tasks:before {
    content:"?"
}
.fa-filter:before {
    content:"?"
}
.fa-briefcase:before {
    content:"?"
}
.fa-arrows-alt:before {
    content:"?"
}
.fa-group:before,
.fa-users:before {
    content:"?"
}
.fa-chain:before,
.fa-link:before {
    content:"?"
}
.fa-cloud:before {
    content:"?"
}
.fa-flask:before {
    content:"?"
}
.fa-cut:before,
.fa-scissors:before {
    content:"?"
}
.fa-copy:before,
.fa-files-o:before {
    content:"?"
}
.fa-paperclip:before {
    content:"?"
}
.fa-save:before,
.fa-floppy-o:before {
    content:"?"
}
.fa-square:before {
    content:"?"
}
.fa-navicon:before,
.fa-reorder:before,
.fa-bars:before {
    content:"?"
}
.fa-list-ul:before {
    content:"?"
}
.fa-list-ol:before {
    content:"?"
}
.fa-strikethrough:before {
    content:"?"
}
.fa-underline:before {
    content:"?"
}
.fa-table:before {
    content:"?"
}
.fa-magic:before {
    content:"?"
}
.fa-truck:before {
    content:"?"
}
.fa-pinterest:before {
    content:"?"
}
.fa-pinterest-square:before {
    content:"?"
}
.fa-google-plus-square:before {
    content:"?"
}
.fa-google-plus:before {
    content:"?"
}
.fa-money:before {
    content:"?"
}
.fa-caret-down:before {
    content:"?"
}
.fa-caret-up:before {
    content:"?"
}
.fa-caret-left:before {
    content:"?"
}
.fa-caret-right:before {
    content:"?"
}
.fa-columns:before {
    content:"?"
}
.fa-unsorted:before,
.fa-sort:before {
    content:"?"
}
.fa-sort-down:before,
.fa-sort-desc:before {
    content:"?"
}
.fa-sort-up:before,
.fa-sort-asc:before {
    content:"?"
}
.fa-envelope:before {
    content:"?"
}
.fa-linkedin:before {
    content:"?"
}
.fa-rotate-left:before,
.fa-undo:before {
    content:"?"
}
.fa-legal:before,
.fa-gavel:before {
    content:"?"
}
.fa-dashboard:before,
.fa-tachometer:before {
    content:"?"
}
.fa-comment-o:before {
    content:"?"
}
.fa-comments-o:before {
    content:"?"
}
.fa-flash:before,
.fa-bolt:before {
    content:"?"
}
.fa-sitemap:before {
    content:"?"
}
.fa-umbrella:before {
    content:"?"
}
.fa-paste:before,
.fa-clipboard:before {
    content:"?"
}
.fa-lightbulb-o:before {
    content:"?"
}
.fa-exchange:before {
    content:"?"
}
.fa-cloud-download:before {
    content:"?"
}
.fa-cloud-upload:before {
    content:"?"
}
.fa-user-md:before {
    content:"?"
}
.fa-stethoscope:before {
    content:"?"
}
.fa-suitcase:before {
    content:"?"
}
.fa-bell-o:before {
    content:"?"
}
.fa-coffee:before {
    content:"?"
}
.fa-cutlery:before {
    content:"?"
}
.fa-file-text-o:before {
    content:"?"
}
.fa-builDing-o:before {
    content:"?"
}
.fa-hospital-o:before {
    content:"?"
}
.fa-ambulance:before {
    content:"?"
}
.fa-medkit:before {
    content:"?"
}
.fa-fighter-jet:before {
    content:"?"
}
.fa-beer:before {
    content:"?"
}
.fa-h-square:before {
    content:"?"
}
.fa-plus-square:before {
    content:"?"
}
.fa-angle-double-left:before {
    content:"?"
}
.fa-angle-double-right:before {
    content:"?"
}
.fa-angle-double-up:before {
    content:"?"
}
.fa-angle-double-down:before {
    content:"?"
}
.fa-angle-left:before {
    content:"?"
}
.fa-angle-right:before {
    content:"?"
}
.fa-angle-up:before {
    content:"?"
}
.fa-angle-down:before {
    content:"?"
}
.fa-desktop:before {
    content:"?"
}
.fa-laptop:before {
    content:"?"
}
.fa-tablet:before {
    content:"?"
}
.fa-mobile-phone:before,
.fa-mobile:before {
    content:"?"
}
.fa-circle-o:before {
    content:"?"
}
.fa-quote-left:before {
    content:"?"
}
.fa-quote-right:before {
    content:"?"
}
.fa-spinner:before {
    content:"?"
}
.fa-circle:before {
    content:"?"
}
.fa-mail-reply:before,
.fa-reply:before {
    content:"?"
}
.fa-github-alt:before {
    content:"?"
}
.fa-folder-o:before {
    content:"?"
}
.fa-folder-open-o:before {
    content:"?"
}
.fa-smile-o:before {
    content:"?"
}
.fa-frown-o:before {
    content:"?"
}
.fa-meh-o:before {
    content:"?"
}
.fa-gamepad:before {
    content:"?"
}
.fa-keyboard-o:before {
    content:"?"
}
.fa-flag-o:before {
    content:"?"
}
.fa-flag-checkered:before {
    content:"?"
}
.fa-terminal:before {
    content:"?"
}
.fa-code:before {
    content:"?"
}
.fa-mail-reply-all:before,
.fa-reply-all:before {
    content:"?"
}
.fa-star-half-empty:before,
.fa-star-half-full:before,
.fa-star-half-o:before {
    content:"?"
}
.fa-location-arrow:before {
    content:"?"
}
.fa-crop:before {
    content:"?"
}
.fa-code-fork:before {
    content:"?"
}
.fa-unlink:before,
.fa-chain-broken:before {
    content:"?"
}
.fa-question:before {
    content:"?"
}
.fa-info:before {
    content:"?"
}
.fa-exclamation:before {
    content:"?"
}
.fa-superscript:before {
    content:"?"
}
.fa-subscript:before {
    content:"?"
}
.fa-eraser:before {
    content:"?"
}
.fa-puzzle-piece:before {
    content:"?"
}
.fa-microphone:before {
    content:"?"
}
.fa-microphone-slash:before {
    content:"?"
}
.fa-shield:before {
    content:"?"
}
.fa-calendar-o:before {
    content:"?"
}
.fa-fire-extinguisher:before {
    content:"?"
}
.fa-rocket:before {
    content:"?"
}
.fa-maxcdn:before {
    content:"?"
}
.fa-chevron-circle-left:before {
    content:"?"
}
.fa-chevron-circle-right:before {
    content:"?"
}
.fa-chevron-circle-up:before {
    content:"?"
}
.fa-chevron-circle-down:before {
    content:"?"
}
.fa-html5:before {
    content:"?"
}
.fa-css3:before {
    content:"?"
}
.fa-anchor:before {
    content:"?"
}
.fa-unlock-alt:before {
    content:"?"
}
.fa-bullseye:before {
    content:"?"
}
.fa-ellipsis-h:before {
    content:"?"
}
.fa-ellipsis-v:before {
    content:"?"
}
.fa-rss-square:before {
    content:"?"
}
.fa-play-circle:before {
    content:"?"
}
.fa-ticket:before {
    content:"?"
}
.fa-minus-square:before {
    content:"?"
}
.fa-minus-square-o:before {
    content:"?"
}
.fa-level-up:before {
    content:"?"
}
.fa-level-down:before {
    content:"?"
}
.fa-check-square:before {
    content:"?"
}
.fa-pencil-square:before {
    content:"?"
}
.fa-external-link-square:before {
    content:"?"
}
.fa-share-square:before {
    content:"?"
}
.fa-compass:before {
    content:"?"
}
.fa-toggle-down:before,
.fa-caret-square-o-down:before {
    content:"?"
}
.fa-toggle-up:before,
.fa-caret-square-o-up:before {
    content:"?"
}
.fa-toggle-right:before,
.fa-caret-square-o-right:before {
    content:"?"
}
.fa-euro:before,
.fa-eur:before {
    content:"?"
}
.fa-gbp:before {
    content:"?"
}
.fa-dollar:before,
.fa-usd:before {
    content:"?"
}
.fa-rupee:before,
.fa-inr:before {
    content:"?"
}
.fa-cny:before,
.fa-rmb:before,
.fa-yen:before,
.fa-jpy:before {
    content:"?"
}
.fa-ruble:before,
.fa-rouble:before,
.fa-rub:before {
    content:"?"
}
.fa-won:before,
.fa-krw:before {
    content:"?"
}
.fa-bitcoin:before,
.fa-btc:before {
    content:"?"
}
.fa-file:before {
    content:"?"
}
.fa-file-text:before {
    content:"?"
}
.fa-sort-alpha-asc:before {
    content:"?"
}
.fa-sort-alpha-desc:before {
    content:"?"
}
.fa-sort-amount-asc:before {
    content:"?"
}
.fa-sort-amount-desc:before {
    content:"?"
}
.fa-sort-numeric-asc:before {
    content:"?"
}
.fa-sort-numeric-desc:before {
    content:"?"
}
.fa-thumbs-up:before {
    content:"?"
}
.fa-thumbs-down:before {
    content:"?"
}
.fa-youtube-square:before {
    content:"?"
}
.fa-youtube:before {
    content:"?"
}
.fa-xing:before {
    content:"?"
}
.fa-xing-square:before {
    content:"?"
}
.fa-youtube-play:before {
    content:"?"
}
.fa-dropbox:before {
    content:"?"
}
.fa-stack-overflow:before {
    content:"?"
}
.fa-instagram:before {
    content:"?"
}
.fa-flickr:before {
    content:"?"
}
.fa-adn:before {
    content:"?"
}
.fa-bitbucket:before {
    content:"?"
}
.fa-bitbucket-square:before {
    content:"?"
}
.fa-tumblr:before {
    content:"?"
}
.fa-tumblr-square:before {
    content:"?"
}
.fa-long-arrow-down:before {
    content:"?"
}
.fa-long-arrow-up:before {
    content:"?"
}
.fa-long-arrow-left:before {
    content:"?"
}
.fa-long-arrow-right:before {
    content:"?"
}
.fa-apple:before {
    content:"?"
}
.fa-windows:before {
    content:"?"
}
.fa-android:before {
    content:"?"
}
.fa-linux:before {
    content:"?"
}
.fa-dribbble:before {
    content:"?"
}
.fa-skype:before {
    content:"?"
}
.fa-foursquare:before {
    content:"?"
}
.fa-trello:before {
    content:"?"
}
.fa-female:before {
    content:"?"
}
.fa-male:before {
    content:"?"
}
.fa-gittip:before,
.fa-gratipay:before {
    content:"?"
}
.fa-sun-o:before {
    content:"?"
}
.fa-moon-o:before {
    content:"?"
}
.fa-archive:before {
    content:"?"
}
.fa-bug:before {
    content:"?"
}
.fa-vk:before {
    content:"?"
}
.fa-weibo:before {
    content:"?"
}
.fa-renren:before {
    content:"?"
}
.fa-pagelines:before {
    content:"?"
}
.fa-stack-exchange:before {
    content:"?"
}
.fa-arrow-circle-o-right:before {
    content:"?"
}
.fa-arrow-circle-o-left:before {
    content:"?"
}
.fa-toggle-left:before,
.fa-caret-square-o-left:before {
    content:"?"
}
.fa-dot-circle-o:before {
    content:"?"
}
.fa-wheelchair:before {
    content:"?"
}
.fa-vimeo-square:before {
    content:"?"
}
.fa-turkish-lira:before,
.fa-try:before {
    content:"?"
}
.fa-plus-square-o:before {
    content:"?"
}
.fa-space-shuttle:before {
    content:"?"
}
.fa-slack:before {
    content:"?"
}
.fa-envelope-square:before {
    content:"?"
}
.fa-wordpress:before {
    content:"?"
}
.fa-openid:before {
    content:"?"
}
.fa-institution:before,
.fa-bank:before,
.fa-university:before {
    content:"?"
}
.fa-mortar-board:before,
.fa-graduation-cap:before {
    content:"?"
}
.fa-yahoo:before {
    content:"?"
}
.fa-google:before {
    content:"?"
}
.fa-reddit:before {
    content:"?"
}
.fa-reddit-square:before {
    content:"?"
}
.fa-stumbleupon-circle:before {
    content:"?"
}
.fa-stumbleupon:before {
    content:"?"
}
.fa-delicious:before {
    content:"?"
}
.fa-digg:before {
    content:"?"
}
.fa-pied-piper:before {
    content:"?"
}
.fa-pied-piper-alt:before {
    content:"?"
}
.fa-drupal:before {
    content:"?"
}
.fa-joomla:before {
    content:"?"
}
.fa-language:before {
    content:"?"
}
.fa-fax:before {
    content:"?"
}
.fa-builDing:before {
    content:"?"
}
.fa-child:before {
    content:"?"
}
.fa-paw:before {
    content:"?"
}
.fa-spoon:before {
    content:"?"
}
.fa-cube:before {
    content:"?"
}
.fa-cubes:before {
    content:"?"
}
.fa-behance:before {
    content:"?"
}
.fa-behance-square:before {
    content:"?"
}
.fa-steam:before {
    content:"?"
}
.fa-steam-square:before {
    content:"?"
}
.fa-recycle:before {
    content:"?"
}
.fa-automobile:before,
.fa-car:before {
    content:"?"
}
.fa-cab:before,
.fa-taxi:before {
    content:"?"
}
.fa-tree:before {
    content:"?"
}
.fa-spotify:before {
    content:"?"
}
.fa-deviantart:before {
    content:"?"
}
.fa-soundcloud:before {
    content:"?"
}
.fa-database:before {
    content:"?"
}
.fa-file-pdf-o:before {
    content:"?"
}
.fa-file-word-o:before {
    content:"?"
}
.fa-file-excel-o:before {
    content:"?"
}
.fa-file-powerpoint-o:before {
    content:"?"
}
.fa-file-photo-o:before,
.fa-file-picture-o:before,
.fa-file-image-o:before {
    content:"?"
}
.fa-file-zip-o:before,
.fa-file-archive-o:before {
    content:"?"
}
.fa-file-sound-o:before,
.fa-file-audio-o:before {
    content:"?"
}
.fa-file-movie-o:before,
.fa-file-video-o:before {
    content:"?"
}
.fa-file-code-o:before {
    content:"?"
}
.fa-vine:before {
    content:"?"
}
.fa-codepen:before {
    content:"?"
}
.fa-jsfiddle:before {
    content:"?"
}
.fa-life-bouy:before,
.fa-life-buoy:before,
.fa-life-saver:before,
.fa-support:before,
.fa-life-ring:before {
    content:"?"
}
.fa-circle-o-notch:before {
    content:"?"
}
.fa-ra:before,
.fa-rebel:before {
    content:"?"
}
.fa-ge:before,
.fa-empire:before {
    content:"?"
}
.fa-git-square:before {
    content:"?"
}
.fa-git:before {
    content:"?"
}
.fa-hacker-news:before {
    content:"?"
}
.fa-tencent-weibo:before {
    content:"?"
}
.fa-qq:before {
    content:"?"
}
.fa-wechat:before,
.fa-weixin:before {
    content:"?"
}
.fa-send:before,
.fa-paper-plane:before {
    content:"?"
}
.fa-send-o:before,
.fa-paper-plane-o:before {
    content:"?"
}
.fa-history:before {
    content:"?"
}
.fa-genderless:before,
.fa-circle-thin:before {
    content:"?"
}
.fa-header:before {
    content:"?"
}
.fa-paragraph:before {
    content:"?"
}
.fa-sliders:before {
    content:"?"
}
.fa-share-alt:before {
    content:"?"
}
.fa-share-alt-square:before {
    content:"?"
}
.fa-bomb:before {
    content:"?"
}
.fa-soccer-ball-o:before,
.fa-futbol-o:before {
    content:"?"
}
.fa-tty:before {
    content:"?"
}
.fa-binoculars:before {
    content:"?"
}
.fa-plug:before {
    content:"?"
}
.fa-slideshare:before {
    content:"?"
}
.fa-twitch:before {
    content:"?"
}
.fa-yelp:before {
    content:"?"
}
.fa-newspaper-o:before {
    content:"?"
}
.fa-wifi:before {
    content:"?"
}
.fa-calculator:before {
    content:"?"
}
.fa-paypal:before {
    content:"?"
}
.fa-google-wallet:before {
    content:"?"
}
.fa-cc-visa:before {
    content:"?"
}
.fa-cc-mastercard:before {
    content:"?"
}
.fa-cc-discover:before {
    content:"?"
}
.fa-cc-amex:before {
    content:"?"
}
.fa-cc-paypal:before {
    content:"?"
}
.fa-cc-stripe:before {
    content:"?"
}
.fa-bell-slash:before {
    content:"?"
}
.fa-bell-slash-o:before {
    content:"?"
}
.fa-trash:before {
    content:"?"
}
.fa-copyright:before {
    content:"?"
}
.fa-at:before {
    content:"?"
}
.fa-eyedropper:before {
    content:"?"
}
.fa-paint-brush:before {
    content:"?"
}
.fa-birthday-cake:before {
    content:"?"
}
.fa-area-chart:before {
    content:"?"
}
.fa-pie-chart:before {
    content:"?"
}
.fa-line-chart:before {
    content:"?"
}
.fa-lastfm:before {
    content:"?"
}
.fa-lastfm-square:before {
    content:"?"
}
.fa-toggle-off:before {
    content:"?"
}
.fa-toggle-on:before {
    content:"?"
}
.fa-bicycle:before {
    content:"?"
}
.fa-bus:before {
    content:"?"
}
.fa-ioxhost:before {
    content:"?"
}
.fa-angellist:before {
    content:"?"
}
.fa-cc:before {
    content:"?"
}
.fa-shekel:before,
.fa-sheqel:before,
.fa-ils:before {
    content:"?"
}
.fa-meanpath:before {
    content:"?"
}
.fa-buysellads:before {
    content:"?"
}
.fa-connectdevelop:before {
    content:"?"
}
.fa-dashcube:before {
    content:"?"
}
.fa-forumbee:before {
    content:"?"
}
.fa-leanpub:before {
    content:"?"
}
.fa-sellsy:before {
    content:"?"
}
.fa-shirtsinbulk:before {
    content:"?"
}
.fa-simplybuilt:before {
    content:"?"
}
.fa-skyatlas:before {
    content:"?"
}
.fa-cart-plus:before {
    content:"?"
}
.fa-cart-arrow-down:before {
    content:"?"
}
.fa-diamond:before {
    content:"?"
}
.fa-ship:before {
    content:"?"
}
.fa-user-secret:before {
    content:"?"
}
.fa-motorcycle:before {
    content:"?"
}
.fa-street-view:before {
    content:"?"
}
.fa-heartbeat:before {
    content:"?"
}
.fa-venus:before {
    content:"?"
}
.fa-mars:before {
    content:"?"
}
.fa-mercury:before {
    content:"?"
}
.fa-transgender:before {
    content:"?"
}
.fa-transgender-alt:before {
    content:"?"
}
.fa-venus-double:before {
    content:"?"
}
.fa-mars-double:before {
    content:"?"
}
.fa-venus-mars:before {
    content:"?"
}
.fa-mars-stroke:before {
    content:"?"
}
.fa-mars-stroke-v:before {
    content:"?"
}
.fa-mars-stroke-h:before {
    content:"?"
}
.fa-neuter:before {
    content:"?"
}
.fa-facebook-official:before {
    content:"?"
}
.fa-pinterest-p:before {
    content:"?"
}
.fa-whatsapp:before {
    content:"?"
}
.fa-server:before {
    content:"?"
}
.fa-user-plus:before {
    content:"?"
}
.fa-user-times:before {
    content:"?"
}
.fa-hotel:before,
.fa-bed:before {
    content:"?"
}
.fa-viacoin:before {
    content:"?"
}
.fa-train:before {
    content:"?"
}
.fa-subway:before {
    content:"?"
}
.fa-medium:before {
    content:"?"
}
#ez-spinner-wrap {
    position:fixed;
    top:50%;
    left:50%;
    margin-left:-185px
}
#ez-spinner,
#ez-spinner div,
#ez-spinner div:before,
#ez-spinner div:after {
    border-radius:50%
}
#ez-spinner {
    width:90px;
    height:90px;
    position:relative;
    margin:0 auto;
    background:#fff
}
#ez-spinner div:before,
#ez-spinner div:after {
    position:absolute;
    content:'';
    background:#fff
}
#ez-spinner div:before {
    width:5.2em;
    height:10.2em;
    border-radius:10.2em 0 0 10.2em;
    top:-.1em;
    left:-.1em;
    -webkit-transform-origin:5.2em 5.1em;
    transform-origin:5.2em 5.1em;
    -webkit-animation:load2 2s infinite ease 1.5s;
    animation:load2 2s infinite ease 1.5s
}
#ez-spinner div {
    font-size:11px;
    text-indent:-99999em;
    position:relative;
    width:10em;
    height:10em;
    box-shadow:inset 0 0 0 1em #ccc;
    position:absolute;
    top:-10px;
    left:-10px;
    -webkit-transform:translateZ(0) scale(.58);
    -ms-transform:translateZ(0) scale(.58);
    transform:translateZ(0) scale(.58)
}
#ez-spinner div:after {
    width:5.2em;
    height:10.2em;
    border-radius:0 10.2em 10.2em 0;
    top:-.1em;
    left:5.1em;
    -webkit-transform-origin:0 5.1em;
    transform-origin:0 5.1em;
    -webkit-animation:load2 2s infinite ease;
    animation:load2 2s infinite ease
}
@-webkit-keyframes load2 {
    0% {
        -webkit-transform:rotate(0);
        transform:rotate(0)
    }
    100% {
        -webkit-transform:rotate(360deg);
        transform:rotate(360deg)
    }
}
@keyframes load2 {
    0% {
        -webkit-transform:rotate(0);
        transform:rotate(0)
    }
    100% {
        -webkit-transform:rotate(360deg);
        transform:rotate(360deg)
    }
}
* {
    margin:0;
    padDing:0;
    outline:none;
    box-sizing:border-box
}
html.ez-extend-footer {
    background:#1c262b
}
body {
    width:100%;
    color:#4c4c59;
    font:normal 18px/24px Roboto,
    sans-serif;
    -webkit-font-smoothing:antialiased
}
body.ez-main-background {
    background-color:#fff
}
body.ez-second-background {
    background-color:#f6f6f6
}
body.ta,
body.ru {
    font-size:17px
}
body.ta *:not(.fa) {
    font-family:NotoTamil,
    sans-serif!important
}
body.hi *:not(.fa) {
    font-family:NotoDevanagari,
    sans-serif!important
}
body.ar,
body.ur {
    direction:rtl
}
body.ar *:not(.fa),
body.ur *:not(.fa) {
    font-family:NotoNaskh,
    sans-serif
}
img {
    border:0
}
img[width="1"] {
    height:0;
    display:block
}
hr {
    border:none;
    border-bottom:1px solid #c2c2c2
}
li {
    list-style-type:none
}
a {
    text-decoration:none;
    color:#005bac;
    line-height:44px;
    display:inline-block
}
a:hover {
    text-decoration:underline
}
a.fa:hover {
    text-decoration:none
}
p a {
    line-height:22px
}
h1,
h2,
h3,
h4 {
    color:#001a6e
}
h1 br,
h2 br,
h3 br,
h4 br {
    content:' '
}
h1 br:after,
h2 br:after,
h3 br:after,
h4 br:after {
    content:" "
}
h1 span.third,
h2 span.third,
h3 span.third,
h4 span.third {
    color:#33c541
}
h1 span.smaller,
h2 span.smaller,
h3 span.smaller,
h4 span.smaller {
    color:#001a6e;
    font-size:smaller
}
h1 strong,
h2 strong,
h3 strong,
h4 strong {
    font-weight:300
}
h1 {
    font-size:44px;
    line-height:50px;
    font-weight:400;
    margin-bottom:15px
}
h2 {
    font-size:32px;
    line-height:38px;
    font-weight:300
}
h3 {
    font-size:28px;
    line-height:34px;
    font-weight:300
}
label {
    font-size:20px;
    line-height:20px;
    display:inline-block;
    margin-bottom:5px;
    position:relative
}
em {
    font-style:normal
}
input[type=text],
input[type=password],
input[type=email],
input[type=tel],
input[type=number],
.ez-select-wrap,
.ez-checkbox-wrap,
textarea {
    height:50px;
    font:normal 24px Roboto,
    san-serif;
    color:#4c4c59;
    border:1px solid #c2c2c2;
    background:#fff;
    border-radius:4px
}
input[type=text]:focus,
input[type=text]:active,
input[type=text].ng-focused,
input[type=password]:focus,
input[type=password]:active,
input[type=password].ng-focused,
input[type=email]:focus,
input[type=email]:active,
input[type=email].ng-focused,
input[type=tel]:focus,
input[type=tel]:active,
input[type=tel].ng-focused,
input[type=number]:focus,
input[type=number]:active,
input[type=number].ng-focused,
.ez-select-wrap:focus,
.ez-select-wrap:active,
.ez-select-wrap.ng-focused,
.ez-checkbox-wrap:focus,
.ez-checkbox-wrap:active,
.ez-checkbox-wrap.ng-focused,
textarea:focus,
textarea:active,
textarea.ng-focused {
    border:1px solid #2c9ada!important;
    box-shadow:0,
    0,
    5px,
    #fff;
    -webkit-box-shadow:0,
    0,
    5px,
    0,
    rgba(44,
    154,
    218,
    .8);
    -moz-box-shadow:0,
    0,
    5px,
    0,
    rgba(44,
    154,
    218,
    .8);
    box-shadow:0 0 5px 0 rgba(44,
    154,
    218,
    .8)
}
input[type=text].input-validation-error,
input[type=text].ng-invalid,
input[type=text].ez-error,
input[type=text].ez-error,
input[type=text].ezeotu-error,
input[type=password].input-validation-error,
input[type=password].ng-invalid,
input[type=password].ez-error,
input[type=password].ez-error,
input[type=password].ezeotu-error,
input[type=email].input-validation-error,
input[type=email].ng-invalid,
input[type=email].ez-error,
input[type=email].ez-error,
input[type=email].ezeotu-error,
input[type=tel].input-validation-error,
input[type=tel].ng-invalid,
input[type=tel].ez-error,
input[type=tel].ez-error,
input[type=tel].ezeotu-error,
input[type=number].input-validation-error,
input[type=number].ng-invalid,
input[type=number].ez-error,
input[type=number].ez-error,
input[type=number].ezeotu-error,
.ez-select-wrap.input-validation-error,
.ez-select-wrap.ng-invalid,
.ez-select-wrap.ez-error,
.ez-select-wrap.ez-error,
.ez-select-wrap.ezeotu-error,
.ez-checkbox-wrap.input-validation-error,
.ez-checkbox-wrap.ng-invalid,
.ez-checkbox-wrap.ez-error,
.ez-checkbox-wrap.ez-error,
.ez-checkbox-wrap.ezeotu-error,
textarea.input-validation-error,
textarea.ng-invalid,
textarea.ez-error,
textarea.ez-error,
textarea.ezeotu-error {
    border:1px solid #ac0000
}
input[type=text].ng-invalid.ng-pristine,
input[type=password].ng-invalid.ng-pristine,
input[type=email].ng-invalid.ng-pristine,
input[type=tel].ng-invalid.ng-pristine,
input[type=number].ng-invalid.ng-pristine,
.ez-select-wrap.ng-invalid.ng-pristine,
.ez-checkbox-wrap.ng-invalid.ng-pristine,
textarea.ng-invalid.ng-pristine {
    border:1px solid #c2c2c2
}
body.ez-main-background input[type=text],
body.ez-main-background input[type=password],
body.ez-main-background input[type=email],
body.ez-main-background input[type=tel],
body.ez-main-background input[type=number],
body.ez-main-background .ez-select-wrap,
body.ez-main-background .ez-checkbox-wrap,
body.ez-main-background textarea,
body.ez-main-background .ez-airtime-btn,
body.ez-main-background .ez-airtime-btn .fa-chevron-down {
    background:#fff
}
input[type=text],
input[type=password],
input[type=email],
input[type=tel],
input[type=number],
textarea {
    -webkit-appearance:none
}
input[type=text],
input[type=password],
input[type=email],
input[type=tel],
input[type=number] {
    width:100%;
    padDing:0 8px;
    line-height:normal
}
input:-webkit-autofill {
    -webkit-box-shadow:0 0 0 1000px white inset
}
input:-webkit-autofill:focus {
    -webkit-box-shadow:0 0 5px 0 rgba(44,
    154,
    218,
    .8),
    inset 0 0 0 1000px white
}
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance:none;
    margin:0
}
textarea {
    width:100%;
    max-width:100%;
    padDing:8px;
    font-size:18px;
    line-height:22px;
    color:#4c4c59;
    resize:none
}
select {
    font-size:13px
}
.ez-checkbox-wrap {
    width:30px;
    height:30px;
    position:relative;
    cursor:default;
    margin-top:-6px
}
.ez-checkbox-wrap * {
    cursor:default
}
.ez-checkbox-wrap input {
    width:inherit;
    height:inherit;
    opacity:0;
    filter:alpha(opacity=0)
}
.ez-checkbox-wrap .fa {
    position:absolute;
    top:3px;
    left:3px;
    font-size:22px;
    color:#33c541;
    display:none
}
.ez-checkbox-wrap.ez-checked .fa {
    display:block!important
}
button,
input[type=submit],
.ez-button {
    line-height:50px;
    padDing:0 10px;
    text-align:center;
    font-size:22px;
    font-family:Roboto,
    sans-serif;
    border:none;
    color:#fff;
    cursor:pointer;
    background:#0072C6;
    vertical-align:top;
    border-radius:4px
}
button:hover,
input[type=submit]:hover,
.ez-button:hover {
    text-decoration:none;
    color:#fff;
    background:#001a6e
}
button b,
input[type=submit] b,
.ez-button b {
    font-weight:normal
}
button.ez-main-cta,
input[type=submit].ez-main-cta,
.ez-button.ez-main-cta {
    height:64px;
    line-height:60px;
    background:#5cb8e7;
    font-size:26px
}
button.ez-main-cta:hover,
input[type=submit].ez-main-cta:hover,
.ez-button.ez-main-cta:hover {
    background:#7dc6ec
}
button.ez-main-cta:focus,
input[type=submit].ez-main-cta:focus,
.ez-button.ez-main-cta:focus {
    background:#53a5cf
}
button.ez-second-cta,
input[type=submit].ez-second-cta,
.ez-button.ez-second-cta {
    background:#33c541
}
button.ez-second-cta:hover,
input[type=submit].ez-second-cta:hover,
.ez-button.ez-second-cta:hover {
    background:#33c541
}
button.ez-third-cta,
input[type=submit].ez-third-cta,
.ez-button.ez-third-cta {
    background:#001a6e
}
button.ez-third-cta:hover,
input[type=submit].ez-third-cta:hover,
.ez-button.ez-third-cta:hover {
    background:#0072C6
}
button.ez-fourth-cta,
input[type=submit].ez-fourth-cta,
.ez-button.ez-fourth-cta {
    background:#ff3dd9
}
button.ez-fourth-cta:hover,
input[type=submit].ez-fourth-cta:hover,
.ez-button.ez-fourth-cta:hover {
    background:#ff5cdf
}
button.ez-delete-cta,
input[type=submit].ez-delete-cta,
.ez-button.ez-delete-cta {
    background:#ac0000
}
button.ez-delete-cta:hover,
input[type=submit].ez-delete-cta:hover,
.ez-button.ez-delete-cta:hover {
    background:#ac3a3a
}
button.ez-disabled-button,
button[disabled=disabled],
input[type=submit].ez-disabled-button,
input[type=submit][disabled=disabled],
.ez-button.ez-disabled-button,
.ez-button[disabled=disabled] {
    opacity:.3;
    filter:alpha(opacity=30)
}
button.ez-disabled-button:hover,
button[disabled=disabled]:hover,
input[type=submit].ez-disabled-button:hover,
input[type=submit][disabled=disabled]:hover,
.ez-button.ez-disabled-button:hover,
.ez-button[disabled=disabled]:hover {
    cursor:default
}
button.ez-disabled-button:hover.ez-second-cta,
button[disabled=disabled]:hover.ez-second-cta,
input[type=submit].ez-disabled-button:hover.ez-second-cta,
input[type=submit][disabled=disabled]:hover.ez-second-cta,
.ez-button.ez-disabled-button:hover.ez-second-cta,
.ez-button[disabled=disabled]:hover.ez-second-cta {
    background:#33c541
}
button.ez-active-btn,
input[type=submit].ez-active-btn,
.ez-button.ez-active-btn {
    color:#33c541
}
button.ez-active-btn:hover,
input[type=submit].ez-active-btn:hover,
.ez-button.ez-active-btn:hover {
    color:#33c541;
    background:#0072C6
}
button.ez-topup-btn,
input[type=submit].ez-topup-btn,
.ez-button.ez-topup-btn {
    height:64px;
    line-height:64px
}
body.ta button,
body.ta input[type=submit],
body.ta .ez-button,
body.ru button,
body.ru input[type=submit],
body.ru .ez-button,
body.de button,
body.de input[type=submit],
body.de .ez-button {
    font-size:20px
}
.ez-button {
    display:inline-block
}
.ez-button .ez-asterisk {
    font-size:16px;
    position:relative;
    top:-10px;
    right:4px
}
.ez-button.ez-second-cta .ez-asterisk {
    color:#001a6e
}
.ez-padded-button {
    padDing-left:30px;
    padDing-right:30px
}
input::-ms-clear {
    display:none
}
input::-ms-reveal {
    display:none
}
[ng-cloak],
[data-ng-cloak],
[x-ng-cloak],
.ng-cloak,
.x-ng-cloak,
.ng-hide {
    display:none!important
}
.ez-debug-border {
    border:1px dashed #888
}
.fa {
    line-height:normal
}
.ez-second-color {
    color:#001a6e
}
.ez-error-color {
    color:#ac0000
}
.ez-asterisk-holder {
    position:relative;
    overflow:hidden;
	background: #F6F6F6;
}
.ez-asterisk-holder>* {
    position:relative;
    z-index:2
}
.ez-background-asterisk {
    width:920px;
    height:920px;
    position:absolute;
    bottom:0;
    right:0;
    z-index:1;
    margin:0 -210px -460px
}
.ez-background-asterisk path {
    fill:#fff
}
.ez-asterisk {
    line-height:normal;
    color:#ff3dd9
}
.ez-main-color {
    color:#ff3dd9
}
.ez-block {
    display:block
}
.ez-hidden {
    display:none
}
.ez-hidden-important {
    display:none!important
}
.ez-full-width {
    width:100%
}
.ez-desktop-hide,
.ez-767-show,
.ez-480-show,
.ez-380-show {
    display:none!important
}
.ez-clear-both,
.clear {
    clear:both
}
.ez-headline {
    color:#001a6e;
    margin:-1px 0 20px 0;
    font-size:36px
}
.ez-float-left {
    float:left
}
.ez-float-right {
    float:right
}
body.ar .ez-float-left,
body.ur .ez-float-left {
    float:right
}
body.ar .ez-float-right,
body.ur .ez-float-right {
    float:left
}
.ez-ellipsis {
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis
}
.ez-align-center {
    clear:both;
    text-align:center
}
.ez-cursor-pointer {
    cursor:pointer
}
.ez-convert-svg {
    display:none
}
.ez-expand-arrow,
.ez-expand-container {
    opacity:1
}
.ez-full-width {
    width:100%
}
a.ez-forgot-pass {
    font-size:20px
}
em.ez-error,
div.ez-error,
span.field-validation-error,
em.ez-good,
em.ezeotu-error {
    color:#ac0000;
    margin:3px 0 0 2px;
    display:block;
    font-size:14px;
    line-height:16px;
    font-weight:400
}
em.ez-error a,
div.ez-error a,
span.field-validation-error a,
em.ez-good a,
em.ezeotu-error a {
    line-height:normal
}
.ez-black {
    color:#000
}
em.ez-good,
div.ez-good {
    color:#33c541
}
input.input-validation-error,
ul.ez-error {
    border-color:#ac0000!important
}
#ez-topup-auth-wrap em.ez-error a {
    font-size:10px
}
.ez-opaque {
    /*opacity:0;*/
    filter:alpha(opacity=0)
}
.ez-optional {
    float:right;
    font-size:18px
}
.ez-mandatory-star {
    color:#4c4c59;
    font-weight:bold;
    font-size:18px;
    float:left;
    margin:1px 4px 0 0
}
.clear {
    clear:both
}
.ez-account-menu-logged-in,
.ez-account-menu-logged-out {
    /*display:none*/
}
.ez-label-hint-icon {
    position:absolute;
    bottom:-5px;
    right:0
}
.ez-label-hint-icon span {
    font-size:28px;
    line-height:34px!important
}
body.ar .ez-label-hint-icon,
body.ur .ez-label-hint-icon {
    right:initial;
    left:0
}
.ez-mobile-focus-out {
    width:1px!important;
    height:1px!important;
    border:0!important;
    padDing:0!important;
    display:inherit;
    margin:0
}
.ez-tap-area {
    min-width:44px;
    cursor:pointer;
    text-align:center;
    margin-top:-4px
}
.ez-tap-area span {
    line-height:44px
}
.ez-tap-area.ez-tap-close {
    font-size:30px
}
.ez-success-table-header {
    float:left;
    font-weight:400;
    font-size:2em;
    color:#ff3dd9;
    border-bottom:1px solid #8a9597;
    width:100%
}
.ez-margin-top {
    margin-top:20px
}
a.ez-link-no-effect {
    color:#001a6e
}
a.ez-link-no-effect:hover {
    text-decoration:none
}
.ez-icon {
    width:28px;
    height:28px;
    text-align:center;
    line-height:28px;
    color:#001a6e;
    border:2px solid #001a6e;
    display:inline-block;
    font-size:16px;
    box-sizing:content-box;
    border-radius:50%
}
.ez-icon-text {
    color:#001a6e;
    display:inline-block;
    line-height:30px;
    margin-left:12px;
    font-size:18px
}
.ez-tagline {
    background:#8a9597;
    padDing-top:20px;
    padDing-bottom:20px
}
.ez-tagline h3,
.ez-tagline h2 {
    color:#fff!important;
    margin-bottom:0!important
}
.ez-tagline h3 span,
.ez-tagline h2 span {
    color:inherit;
    font-weight:bold
}
.ez-cool-top-border {
    border-top:5px solid #ff3dd9
}
.ez-cool-top-border.ez-border-second-color {
    border-color:#001a6e
}
.ez-cool-top-border.ez-border-third-color {
    border-color:#33c541
}
.ez-cool-bottom-border {
    border-bottom:1px solid #33c541
}
.ez-inline-block {
    display:inline-block
}
#ez-ildterms {
    font-size:14px;
    color:#fff
}
#ez-ildterms a {
    line-height:normal;
    color:#eeedef
}
.ez-bold {
    font-weight:bold
}
.ez-smallnote {
    font-size:14px
}
.ez-hittable-link {
    margin:-17px 0
}
.ez-operator-detected {
    color:#0072C6;
    font-size:30px
}
.ez-input-icon-wrap {
    position:relative;
    direction:ltr
}
.ez-input-icon-wrap input,
.ez-input-icon-wrap.ez-dplaceholder-wrap p {
    padDing-left:40px
}
.ez-input-icon-wrap span {
    margin-top:-9px;
    font-size:19px;
    line-height:19px;
    position:absolute;
    left:10px;
    top:50%;
    z-index:10
}
.ez-input-icon-wrap span.lockicon- {
    font-size:21px;
    line-height:21px
}
.ez-input-icon-wrap span.ez-valid {
    color:#33c541
}
.ez-dplaceholder-wrap p {
    position:absolute;
    z-index:1;
    line-height:52px;
    font-size:24px;
    padDing-left:8px;
    color:#a9a9a9
}
.ez-dplaceholder-wrap input {
    position:relative;
    z-index:3;
    background:transparent
}
.ez-select-wrap {
    position:relative;
    line-height:48px
}
.ez-select-wrap:hover {
    border:1px solid #2c9ada
}
.ez-select-wrap .ez-select-info {
    line-height:inherit;
    margin:0 10px
}
.ez-select-wrap .ez-select-info .ez-select-icon {
    float:left;
    margin:7px 12px 0 0
}
.ez-select-wrap .ez-select-info .fa {
    float:left;
    font-size:22px;
    line-height:inherit
}
.ez-select-wrap .ez-select-info .fa.ez-mright {
    margin-right:10px
}
.ez-select-wrap .ez-select-info .fa-angle-down,
.ez-select-wrap .ez-select-info .fa-angle-up,
.ez-select-wrap .ez-select-info .angle-downicon- {
    float:right
}
.ez-select-wrap span {
    display:inline-block;
    max-width:90%;
    line-height:inherit
}
.ez-select-wrap select {
    width:100%;
    height:100%;
    -webkit-appearance:none;
    position:absolute;
    top:0;
    left:0;
    cursor:pointer;
    opacity:0;
    filter:alpha(opacity=0)
}
.ez-select-wrap .ez-custom-select {
    width:100%;
    background:#414c53;
    overflow:auto;
    position:absolute;
    top:49px;
    left:0;
    display:none;
    margin-left:0
}
.ez-select-wrap .ez-custom-select.ez-top {
    top:auto;
    bottom:49px
}
.ez-select-wrap .ez-custom-select::-webkit-scrollbar {
    width:12px
}
.ez-select-wrap .ez-custom-select::-webkit-scrollbar-track {
    margin:1px 0;
    -webkit-box-shadow:inset 0 0 6px rgba(0,
    0,
    0,
    .3);
    border-radius:4px
}
.ez-select-wrap .ez-custom-select::-webkit-scrollbar-thumb {
    background:rgba(82,
    95,
    104,
    .8);
    -webkit-box-shadow:inset 0 0 6px rgba(0,
    0,
    0,
    .5);
    border-radius:4px
}
.ez-select-wrap .ez-custom-select::-webkit-scrollbar-thumb:window-inactive {
    background:rgba(82,
    95,
    104,
    .4)
}
.ez-select-wrap .ez-custom-select li {
    height:36px;
    padDing:0 20px
}
.ez-select-wrap .ez-custom-select li:hover {
    background:#525f68
}
.ez-select-wrap .ez-custom-select a {
    height:auto;
    line-height:36px
}
.ez-select-wrap .ez-custom-select a:hover {
    text-decoration:none
}
body.ar .ez-select-wrap span,
body.ar .ez-select-wrap .fa,
body.ur .ez-select-wrap span,
body.ur .ez-select-wrap .fa {
    float:right
}
body.ar .ez-select-wrap span.ez-mright,
body.ar .ez-select-wrap .fa.ez-mright,
body.ur .ez-select-wrap span.ez-mright,
body.ur .ez-select-wrap .fa.ez-mright {
    margin-right:0;
    margin-left:10px
}
body.ar .ez-select-wrap .ez-select-info .fa-angle-down,
body.ar .ez-select-wrap .ez-select-info .fa-angle-up,
body.ar .ez-select-wrap .ez-select-info .angle-downicon-,
body.ur .ez-select-wrap .ez-select-info .fa-angle-down,
body.ur .ez-select-wrap .ez-select-info .fa-angle-up,
body.ur .ez-select-wrap .ez-select-info .angle-downicon- {
    float:left
}
.ez-custom-select-wrap select {
    display:none
}
body[data-device="mobile"] .ez-custom-select-wrap select {
    display:block
}
.ez-language-selector-wrap {
    background:#1c262b!important;
    border-color:#676d71;
    color:#ebebeb;
    margin-top:11px
}
.ez-language-selector-wrap span {
    font-weight:300;
    font-size:16px!important;
    line-height:inherit;
    float:left
}
.ez-language-selector-wrap .ez-select-info {
    color:#ebebeb
}
.ez-disabled-scroll {
    overflow-y:scroll;
    position:fixed
}
.ez-flag-ad {
    background-position:0 0
}
.ez-flag-ae {
    background-position:-25px 0
}
.ez-flag-af {
    background-position:-50px 0
}
.ez-flag-ag {
    background-position:-75px 0
}
.ez-flag-ai {
    background-position:-100px 0
}
.ez-flag-al {
    background-position:-125px 0
}
.ez-flag-am {
    background-position:-150px 0
}
.ez-flag-an {
    background-position:-175px 0
}
.ez-flag-ao {
    background-position:-200px 0
}
.ez-flag-aq {
    background-position:-225px 0
}
.ez-flag-ar {
    background-position:-250px 0
}
.ez-flag-as {
    background-position:-275px 0
}
.ez-flag-at {
    background-position:-300px 0
}
.ez-flag-au {
    background-position:-325px 0
}
.ez-flag-aw {
    background-position:-350px 0
}
.ez-flag-az {
    background-position:-375px 0
}
.ez-flag-ba {
    background-position:-400px 0
}
.ez-flag-bb {
    background-position:-425px 0
}
.ez-flag-bd {
    background-position:-450px 0
}
.ez-flag-be {
    background-position:-475px 0
}
.ez-flag-bf {
    background-position:-500px 0
}
.ez-flag-bg {
    background-position:-525px 0
}
.ez-flag-bh {
    background-position:-550px 0
}
.ez-flag-bi {
    background-position:-575px 0
}
.ez-flag-bj {
    background-position:-600px 0
}
.ez-flag-bm {
    background-position:-625px 0
}
.ez-flag-bn {
    background-position:-650px 0
}
.ez-flag-bo {
    background-position:-675px 0
}
.ez-flag-br {
    background-position:-700px 0
}
.ez-flag-bs {
    background-position:-725px 0
}
.ez-flag-bt {
    background-position:-750px 0
}
.ez-flag-bw {
    background-position:-775px 0
}
.ez-flag-by {
    background-position:-800px 0
}
.ez-flag-bz {
    background-position:-825px 0
}
.ez-flag-ca {
    background-position:-850px 0
}
.ez-flag-cc {
    background-position:-875px 0
}
.ez-flag-cd {
    background-position:-900px 0
}
.ez-flag-cf {
    background-position:-925px 0
}
.ez-flag-cg {
    background-position:-950px 0
}
.ez-flag-ch {
    background-position:-975px 0
}
.ez-flag-ci {
    background-position:-1000px 0
}
.ez-flag-ck {
    background-position:-1025px 0
}
.ez-flag-cl {
    background-position:-1050px 0
}
.ez-flag-cm {
    background-position:-1075px 0
}
.ez-flag-cn {
    background-position:-1100px 0
}
.ez-flag-co {
    background-position:-1125px 0
}
.ez-flag-cr {
    background-position:-1150px 0
}
.ez-flag-cu {
    background-position:-1175px 0
}
.ez-flag-cun {
    background-position:-1175px 0
}
.ez-flag-cv {
    background-position:-1200px 0
}
.ez-flag-cw {
    background-position:-1225px 0
}
.ez-flag-cx {
    background-position:-1250px 0
}
.ez-flag-cy {
    background-position:-1275px 0
}
.ez-flag-cz {
    background-position:-1300px 0
}
.ez-flag-de {
    background-position:-1325px 0
}
.ez-flag-dj {
    background-position:-1350px 0
}
.ez-flag-dk {
    background-position:-1375px 0
}
.ez-flag-dm {
    background-position:-1400px 0
}
.ez-flag-do {
    background-position:-1425px 0
}
.ez-flag-dz {
    background-position:-1450px 0
}
.ez-flag-ec {
    background-position:-1475px 0
}
.ez-flag-ee {
    background-position:-1500px 0
}
.ez-flag-eg {
    background-position:-1525px 0
}
.ez-flag-eh {
    background-position:-1550px 0
}
.ez-flag-er {
    background-position:-1575px 0
}
.ez-flag-es {
    background-position:-1600px 0
}
.ez-flag-et {
    background-position:-1625px 0
}
.ez-flag-fi {
    background-position:-1650px 0
}
.ez-flag-fj {
    background-position:-1675px 0
}
.ez-flag-fm {
    background-position:-1700px 0
}
.ez-flag-fo {
    background-position:-1725px 0
}
.ez-flag-fr,
.ez-flag-bl,
.ez-flag-mf,
.ez-flag-gf,
.ez-flag-yt {
    background-position:-1750px 0
}
.ez-flag-ga {
    background-position:-1775px 0
}
.ez-flag-gb {
    background-position:-1800px 0
}
.ez-flag-gd {
    background-position:-1825px 0
}
.ez-flag-ge {
    background-position:-1850px 0
}
.ez-flag-gg {
    background-position:-1875px 0
}
.ez-flag-gh {
    background-position:-1900px 0
}
.ez-flag-gi {
    background-position:-1925px 0
}
.ez-flag-gl {
    background-position:-1950px 0
}
.ez-flag-gm {
    background-position:-1975px 0
}
.ez-flag-gn {
    background-position:-2000px 0
}
.ez-flag-gp {
    background-position:-2025px 0
}
.ez-flag-gq {
    background-position:-2050px 0
}
.ez-flag-gr {
    background-position:-2075px 0
}
.ez-flag-gt {
    background-position:-2100px 0
}
.ez-flag-gu {
    background-position:-2125px 0
}
.ez-flag-gw {
    background-position:-2150px 0
}
.ez-flag-gy {
    background-position:-2175px 0
}
.ez-flag-hk {
    background-position:-2200px 0
}
.ez-flag-hn {
    background-position:-2225px 0
}
.ez-flag-hr {
    background-position:-2250px 0
}
.ez-flag-ht {
    background-position:-2275px 0
}
.ez-flag-hu {
    background-position:-2300px 0
}
.ez-flag-id {
    background-position:-2325px 0
}
.ez-flag-ie {
    background-position:-2350px 0
}
.ez-flag-il {
    background-position:-2375px 0
}
.ez-flag-im {
    background-position:-2400px 0
}
.ez-flag-in {
    background-position:-2425px 0
}
.ez-flag-iq {
    background-position:-2450px 0
}
.ez-flag-ir {
    background-position:-2475px 0
}
.ez-flag-is {
    background-position:-2500px 0
}
.ez-flag-it {
    background-position:-2525px 0
}
.ez-flag-je {
    background-position:-2550px 0
}
.ez-flag-jm {
    background-position:-2575px 0
}
.ez-flag-jo {
    background-position:-2600px 0
}
.ez-flag-jp {
    background-position:-2625px 0
}
.ez-flag-ke {
    background-position:-2650px 0
}
.ez-flag-kg {
    background-position:-2675px 0
}
.ez-flag-kh {
    background-position:-2700px 0
}
.ez-flag-ki {
    background-position:-2725px 0
}
.ez-flag-km {
    background-position:-2750px 0
}
.ez-flag-kn {
    background-position:-2775px 0
}
.ez-flag-kp {
    background-position:-2800px 0
}
.ez-flag-kr {
    background-position:-2825px 0
}
.ez-flag-kw {
    background-position:-2850px 0
}
.ez-flag-ky {
    background-position:-2875px 0
}
.ez-flag-kz {
    background-position:-2900px 0
}
.ez-flag-la {
    background-position:-2925px 0
}
.ez-flag-lb {
    background-position:-2950px 0
}
.ez-flag-lc {
    background-position:-2975px 0
}
.ez-flag-li {
    background-position:-3000px 0
}
.ez-flag-lk {
    background-position:-3025px 0
}
.ez-flag-lr {
    background-position:-3050px 0
}
.ez-flag-ls {
    background-position:-3075px 0
}
.ez-flag-lt {
    background-position:-3100px 0
}
.ez-flag-lu {
    background-position:-3125px 0
}
.ez-flag-lv {
    background-position:-3150px 0
}
.ez-flag-ly {
    background-position:-3175px 0
}
.ez-flag-ma {
    background-position:-3200px 0
}
.ez-flag-mc {
    background-position:-3225px 0
}
.ez-flag-md {
    background-position:-3250px 0
}
.ez-flag-me {
    background-position:-3275px 0
}
.ez-flag-mg {
    background-position:-3300px 0
}
.ez-flag-mh {
    background-position:-3325px 0
}
.ez-flag-mk {
    background-position:-3350px 0
}
.ez-flag-ml {
    background-position:-3375px 0
}
.ez-flag-mm {
    background-position:-3400px 0
}
.ez-flag-mn {
    background-position:-3425px 0
}
.ez-flag-mo {
    background-position:-3450px 0
}
.ez-flag-mp {
    background-position:-3475px 0
}
.ez-flag-mq {
    background-position:-3500px 0
}
.ez-flag-mr {
    background-position:-3525px 0
}
.ez-flag-ms,
.ez-flag-fk,
.ez-flag-pn {
    background-position:-3550px 0
}
.ez-flag-mt {
    background-position:-3575px 0
}
.ez-flag-mu {
    background-position:-3600px 0
}
.ez-flag-mv {
    background-position:-3625px 0
}
.ez-flag-mw {
    background-position:-3650px 0
}
.ez-flag-mx {
    background-position:-3675px 0
}
.ez-flag-my {
    background-position:-3700px 0
}
.ez-flag-mz {
    background-position:-3725px 0
}
.ez-flag-na {
    background-position:-3750px 0
}
.ez-flag-nc {
    background-position:-3775px 0
}
.ez-flag-ne {
    background-position:-3800px 0
}
.ez-flag-nf {
    background-position:-3825px 0
}
.ez-flag-ng {
    background-position:-3850px 0
}
.ez-flag-ni {
    background-position:-3875px 0
}
.ez-flag-nl {
    background-position:-3900px 0
}
.ez-flag-no {
    background-position:-3925px 0
}
.ez-flag-np {
    background-position:-3950px 0
}
.ez-flag-nr {
    background-position:-3975px 0
}
.ez-flag-nu {
    background-position:-4000px 0
}
.ez-flag-nz {
    background-position:-4025px 0
}
.ez-flag-om {
    background-position:-4050px 0
}
.ez-flag-pa {
    background-position:-4075px 0
}
.ez-flag-pe {
    background-position:-4100px 0
}
.ez-flag-pf {
    background-position:-4125px 0
}
.ez-flag-pg {
    background-position:-4150px 0
}
.ez-flag-ph {
    background-position:-4175px 0
}
.ez-flag-pk {
    background-position:-4200px 0
}
.ez-flag-pl {
    background-position:-4225px 0
}
.ez-flag-pm {
    background-position:-4250px 0
}
.ez-flag-pr {
    background-position:-4275px 0
}
.ez-flag-ps {
    background-position:-4300px 0
}
.ez-flag-pt {
    background-position:-4325px 0
}
.ez-flag-pw {
    background-position:-4350px 0
}
.ez-flag-py {
    background-position:-4375px 0
}
.ez-flag-qa {
    background-position:-4400px 0
}
.ez-flag-re {
    background-position:-4425px 0
}
.ez-flag-ro {
    background-position:-4450px 0
}
.ez-flag-rs {
    background-position:-4475px 0
}
.ez-flag-ru {
    background-position:-4500px 0
}
.ez-flag-rw {
    background-position:-4525px 0
}
.ez-flag-sa {
    background-position:-4550px 0
}
.ez-flag-sb {
    background-position:-4575px 0
}
.ez-flag-sc {
    background-position:-4600px 0
}
.ez-flag-sd {
    background-position:-4625px 0
}
.ez-flag-se {
    background-position:-4650px 0
}
.ez-flag-sg {
    background-position:-4675px 0
}
.ez-flag-si {
    background-position:-4700px 0
}
.ez-flag-sk {
    background-position:-4725px 0
}
.ez-flag-sl {
    background-position:-4750px 0
}
.ez-flag-sm {
    background-position:-4775px 0
}
.ez-flag-sn {
    background-position:-4800px 0
}
.ez-flag-so {
    background-position:-4825px 0
}
.ez-flag-sr {
    background-position:-4850px 0
}
.ez-flag-st {
    background-position:-4875px 0
}
.ez-flag-sv {
    background-position:-4900px 0
}
.ez-flag-sy {
    background-position:-4925px 0
}
.ez-flag-sz {
    background-position:-4950px 0
}
.ez-flag-tc {
    background-position:-4975px 0
}
.ez-flag-td {
    background-position:-5000px 0
}
.ez-flag-tg {
    background-position:-5025px 0
}
.ez-flag-th {
    background-position:-5050px 0
}
.ez-flag-tj {
    background-position:-5075px 0
}
.ez-flag-tk {
    background-position:-5100px 0
}
.ez-flag-tl,
.ez-flag-tp {
    background-position:-5125px 0
}
.ez-flag-tm {
    background-position:-5150px 0
}
.ez-flag-tn {
    background-position:-5175px 0
}
.ez-flag-to {
    background-position:-5200px 0
}
.ez-flag-tr {
    background-position:-5225px 0
}
.ez-flag-tt {
    background-position:-5250px 0
}
.ez-flag-tv {
    background-position:-5275px 0
}
.ez-flag-tw {
    background-position:-5300px 0
}
.ez-flag-tz {
    background-position:-5325px 0
}
.ez-flag-ua {
    background-position:-5350px 0
}
.ez-flag-ug {
    background-position:-5375px 0
}
.ez-flag-us {
    background-position:-5400px 0
}
.ez-flag-uy {
    background-position:-5425px 0
}
.ez-flag-uz {
    background-position:-5450px 0
}
.ez-flag-va {
    background-position:-5475px 0
}
.ez-flag-vc {
    background-position:-5500px 0
}
.ez-flag-ve {
    background-position:-5525px 0
}
.ez-flag-vg {
    background-position:-5550px 0
}
.ez-flag-vi {
    background-position:-5575px 0
}
.ez-flag-vn {
    background-position:-5600px 0
}
.ez-flag-vu {
    background-position:-5625px 0
}
.ez-flag-wf {
    background-position:-5650px 0
}
.ez-flag-ws {
    background-position:-5675px 0
}
.ez-flag-xa {
    background-position:-5700px 0
}
.ez-flag-ye {
    background-position:-5725px 0
}
.ez-flag-za {
    background-position:-5750px 0
}
.ez-flag-zm {
    background-position:-5775px 0
}
.ez-flag-zw {
    background-position:-5800px 0
}
.ez-flag-xk {
    background-position:-5825px 0
}
.ez-flag-big-ad {
    background-position:0 0
}
.ez-flag-big-ae {
    background-position:-46px 0
}
.ez-flag-big-af {
    background-position:-92px 0
}
.ez-flag-big-ag {
    background-position:-138px 0
}
.ez-flag-big-ai {
    background-position:-184px 0
}
.ez-flag-big-al {
    background-position:-230px 0
}
.ez-flag-big-am {
    background-position:-276px 0
}
.ez-flag-big-an {
    background-position:-322px 0
}
.ez-flag-big-ao {
    background-position:-368px 0
}
.ez-flag-big-aq {
    background-position:-414px 0
}
.ez-flag-big-ar {
    background-position:-460px 0
}
.ez-flag-big-as {
    background-position:-506px 0
}
.ez-flag-big-at {
    background-position:-552px 0
}
.ez-flag-big-au {
    background-position:-598px 0
}
.ez-flag-big-aw {
    background-position:-644px 0
}
.ez-flag-big-az {
    background-position:-690px 0
}
.ez-flag-big-ba {
    background-position:-736px 0
}
.ez-flag-big-bb {
    background-position:-782px 0
}
.ez-flag-big-bd {
    background-position:-828px 0
}
.ez-flag-big-be {
    background-position:-874px 0
}
.ez-flag-big-bf {
    background-position:-920px 0
}
.ez-flag-big-bg {
    background-position:-966px 0
}
.ez-flag-big-bh {
    background-position:-1012px 0
}
.ez-flag-big-bi {
    background-position:-1058px 0
}
.ez-flag-big-bj {
    background-position:-1104px 0
}
.ez-flag-big-bm {
    background-position:-1150px 0
}
.ez-flag-big-bn {
    background-position:-1196px 0
}
.ez-flag-big-bo {
    background-position:-1242px 0
}
.ez-flag-big-br {
    background-position:-1288px 0
}
.ez-flag-big-bs {
    background-position:-1334px 0
}
.ez-flag-big-bt {
    background-position:-1380px 0
}
.ez-flag-big-bw {
    background-position:-1426px 0
}
.ez-flag-big-by {
    background-position:-1472px 0
}
.ez-flag-big-bz {
    background-position:-1518px 0
}
.ez-flag-big-ca {
    background-position:-1564px 0
}
.ez-flag-big-cd {
    background-position:-1610px 0
}
.ez-flag-big-cf {
    background-position:-1656px 0
}
.ez-flag-big-cg {
    background-position:-1702px 0
}
.ez-flag-big-ch {
    background-position:-1748px 0
}
.ez-flag-big-ci {
    background-position:-1794px 0
}
.ez-flag-big-ck {
    background-position:-1840px 0
}
.ez-flag-big-cl {
    background-position:-1886px 0
}
.ez-flag-big-cm {
    background-position:-1932px 0
}
.ez-flag-big-cn {
    background-position:-1978px 0
}
.ez-flag-big-co {
    background-position:-2024px 0
}
.ez-flag-big-cr {
    background-position:-2070px 0
}
.ez-flag-big-cu {
    background-position:-2116px 0
}
.ez-flag-big-cv {
    background-position:-2162px 0
}
.ez-flag-big-cy {
    background-position:-2208px 0
}
.ez-flag-big-cz {
    background-position:-2254px 0
}
.ez-flag-big-de {
    background-position:-2300px 0
}
.ez-flag-big-dj {
    background-position:-2346px 0
}
.ez-flag-big-dk {
    background-position:-2392px 0
}
.ez-flag-big-dm {
    background-position:-2438px 0
}
.ez-flag-big-do {
    background-position:-2484px 0
}
.ez-flag-big-dz {
    background-position:-2530px 0
}
.ez-flag-big-ec {
    background-position:-2576px 0
}
.ez-flag-big-ee {
    background-position:-2622px 0
}
.ez-flag-big-eg {
    background-position:-2668px 0
}
.ez-flag-big-eh {
    background-position:-2714px 0
}
.ez-flag-big-er {
    background-position:-2760px 0
}
.ez-flag-big-es {
    background-position:-2806px 0
}
.ez-flag-big-et {
    background-position:-2852px 0
}
.ez-flag-big-fi {
    background-position:-2898px 0
}
.ez-flag-big-fj {
    background-position:-2944px 0
}
.ez-flag-big-fm {
    background-position:-2990px 0
}
.ez-flag-big-fo {
    background-position:-3036px 0
}
.ez-flag-big-fr,
.ez-flag-bl,
.ez-flag-mf,
.ez-flag-gf,
.ez-flag-yt {
    background-position:-3082px 0
}
.ez-flag-big-ga {
    background-position:-3128px 0
}
.ez-flag-big-gb {
    background-position:-3174px 0
}
.ez-flag-big-gd {
    background-position:-3220px 0
}
.ez-flag-big-ge {
    background-position:-3266px 0
}
.ez-flag-big-gg {
    background-position:-3312px 0
}
.ez-flag-big-gh {
    background-position:-3358px 0
}
.ez-flag-big-gi {
    background-position:-3404px 0
}
.ez-flag-big-gl {
    background-position:-3450px 0
}
.ez-flag-big-gm {
    background-position:-3496px 0
}
.ez-flag-big-gn {
    background-position:-3542px 0
}
.ez-flag-big-gp {
    background-position:-3588px 0
}
.ez-flag-big-gq {
    background-position:-3634px 0
}
.ez-flag-big-gr {
    background-position:-3680px 0
}
.ez-flag-big-gt {
    background-position:-3726px 0
}
.ez-flag-big-gu {
    background-position:-3772px 0
}
.ez-flag-big-gw {
    background-position:-3818px 0
}
.ez-flag-big-gy {
    background-position:-3864px 0
}
.ez-flag-big-hk {
    background-position:-3910px 0
}
.ez-flag-big-hn {
    background-position:-3956px 0
}
.ez-flag-big-hr {
    background-position:-4002px 0
}
.ez-flag-big-ht {
    background-position:-4048px 0
}
.ez-flag-big-hu {
    background-position:-4094px 0
}
.ez-flag-big-id {
    background-position:-4140px 0
}
.ez-flag-big-ie {
    background-position:-4186px 0
}
.ez-flag-big-il {
    background-position:-4232px 0
}
.ez-flag-big-im {
    background-position:-4278px 0
}
.ez-flag-big-in {
    background-position:-4324px 0
}
.ez-flag-big-iq {
    background-position:-4370px 0
}
.ez-flag-big-ir {
    background-position:-4416px 0
}
.ez-flag-big-is {
    background-position:-4462px 0
}
.ez-flag-big-it {
    background-position:-4508px 0
}
.ez-flag-big-je {
    background-position:-4554px 0
}
.ez-flag-big-jm {
    background-position:-4600px 0
}
.ez-flag-big-jo {
    background-position:-4646px 0
}
.ez-flag-big-jp {
    background-position:-4692px 0
}
.ez-flag-big-ke {
    background-position:-4738px 0
}
.ez-flag-big-kg {
    background-position:-4784px 0
}
.ez-flag-big-kh {
    background-position:-4830px 0
}
.ez-flag-big-ki {
    background-position:-4876px 0
}
.ez-flag-big-km {
    background-position:-4922px 0
}
.ez-flag-big-kn {
    background-position:-4968px 0
}
.ez-flag-big-kp {
    background-position:-5014px 0
}
.ez-flag-big-kr {
    background-position:-5060px 0
}
.ez-flag-big-kw {
    background-position:-5106px 0
}
.ez-flag-big-ky {
    background-position:-5152px 0
}
.ez-flag-big-kz {
    background-position:-5198px 0
}
.ez-flag-big-la {
    background-position:-5244px 0
}
.ez-flag-big-lb {
    background-position:-5290px 0
}
.ez-flag-big-lc {
    background-position:-5336px 0
}
.ez-flag-big-li {
    background-position:-5382px 0
}
.ez-flag-big-lk {
    background-position:-5428px 0
}
.ez-flag-big-lr {
    background-position:-5474px 0
}
.ez-flag-big-ls {
    background-position:-5520px 0
}
.ez-flag-big-lt {
    background-position:-5566px 0
}
.ez-flag-big-lu {
    background-position:-5612px 0
}
.ez-flag-big-lv {
    background-position:-5658px 0
}
.ez-flag-big-ly {
    background-position:-5704px 0
}
.ez-flag-big-ma {
    background-position:-5750px 0
}
.ez-flag-big-mc {
    background-position:-5796px 0
}
.ez-flag-big-md {
    background-position:-5842px 0
}
.ez-flag-big-me {
    background-position:-5888px 0
}
.ez-flag-big-mg {
    background-position:-5934px 0
}
.ez-flag-big-mh {
    background-position:-5980px 0
}
.ez-flag-big-mk {
    background-position:-6026px 0
}
.ez-flag-big-ml {
    background-position:-6072px 0
}
.ez-flag-big-mm {
    background-position:-6118px 0
}
.ez-flag-big-mn {
    background-position:-6164px 0
}
.ez-flag-big-mo {
    background-position:-6210px 0
}
.ez-flag-big-mq {
    background-position:-6256px 0
}
.ez-flag-big-mr {
    background-position:-6302px 0
}
.ez-flag-big-ms {
    background-position:-6348px 0
}
.ez-flag-big-mt {
    background-position:-6394px 0
}
.ez-flag-big-mu {
    background-position:-6440px 0
}
.ez-flag-big-mv {
    background-position:-6486px 0
}
.ez-flag-big-mw {
    background-position:-6532px 0
}
.ez-flag-big-mx {
    background-position:-6578px 0
}
.ez-flag-big-my {
    background-position:-6624px 0
}
.ez-flag-big-mz {
    background-position:-6670px 0
}
.ez-flag-big-na {
    background-position:-6716px 0
}
.ez-flag-big-nc {
    background-position:-6762px 0
}
.ez-flag-big-ne {
    background-position:-6808px 0
}
.ez-flag-big-ng {
    background-position:-6854px 0
}
.ez-flag-big-ni {
    background-position:-6900px 0
}
.ez-flag-big-nl {
    background-position:-6946px 0
}
.ez-flag-big-no {
    background-position:-6992px 0
}
.ez-flag-big-np {
    background-position:-7038px 0
}
.ez-flag-big-nr {
    background-position:-7084px 0
}
.ez-flag-big-nz {
    background-position:-7130px 0
}
.ez-flag-big-om {
    background-position:-7176px 0
}
.ez-flag-big-pa {
    background-position:-7222px 0
}
.ez-flag-big-pe {
    background-position:-7268px 0
}
.ez-flag-big-pf {
    background-position:-7314px 0
}
.ez-flag-big-pg {
    background-position:-7360px 0
}
.ez-flag-big-ph {
    background-position:-7406px 0
}
.ez-flag-big-pk {
    background-position:-7452px 0
}
.ez-flag-big-pl {
    background-position:-7498px 0
}
.ez-flag-big-pr {
    background-position:-7544px 0
}
.ez-flag-big-ps {
    background-position:-7590px 0
}
.ez-flag-big-pt {
    background-position:-7636px 0
}
.ez-flag-big-pw {
    background-position:-7682px 0
}
.ez-flag-big-py {
    background-position:-7728px 0
}
.ez-flag-big-qa {
    background-position:-7774px 0
}
.ez-flag-big-re {
    background-position:-7820px 0
}
.ez-flag-big-ro {
    background-position:-7866px 0
}
.ez-flag-big-rs {
    background-position:-7912px 0
}
.ez-flag-big-ru {
    background-position:-7958px 0
}
.ez-flag-big-rw {
    background-position:-8004px 0
}
.ez-flag-big-sa {
    background-position:-8050px 0
}
.ez-flag-big-sb {
    background-position:-8096px 0
}
.ez-flag-big-sc {
    background-position:-8142px 0
}
.ez-flag-big-sd {
    background-position:-8188px 0
}
.ez-flag-big-se {
    background-position:-8234px 0
}
.ez-flag-big-sg {
    background-position:-8280px 0
}
.ez-flag-big-si {
    background-position:-8326px 0
}
.ez-flag-big-sk {
    background-position:-8372px 0
}
.ez-flag-big-sl {
    background-position:-8418px 0
}
.ez-flag-big-sm {
    background-position:-8464px 0
}
.ez-flag-big-sn {
    background-position:-8510px 0
}
.ez-flag-big-so {
    background-position:-8556px 0
}
.ez-flag-big-sr {
    background-position:-8602px 0
}
.ez-flag-big-st {
    background-position:-8648px 0
}
.ez-flag-big-sv {
    background-position:-8694px 0
}
.ez-flag-big-sy {
    background-position:-8740px 0
}
.ez-flag-big-sz {
    background-position:-8786px 0
}
.ez-flag-big-tc {
    background-position:-8832px 0
}
.ez-flag-big-td {
    background-position:-8878px 0
}
.ez-flag-big-tg {
    background-position:-8924px 0
}
.ez-flag-big-th {
    background-position:-8970px 0
}
.ez-flag-big-tj {
    background-position:-9016px 0
}
.ez-flag-big-tl {
    background-position:-9062px 0
}
.ez-flag-big-tm {
    background-position:-9108px 0
}
.ez-flag-big-tn {
    background-position:-9154px 0
}
.ez-flag-big-to {
    background-position:-9200px 0
}
.ez-flag-big-tr {
    background-position:-9246px 0
}
.ez-flag-big-tt {
    background-position:-9292px 0
}
.ez-flag-big-tv {
    background-position:-9338px 0
}
.ez-flag-big-tw {
    background-position:-9384px 0
}
.ez-flag-big-tz {
    background-position:-9430px 0
}
.ez-flag-big-ua {
    background-position:-9476px 0
}
.ez-flag-big-ug {
    background-position:-9522px 0
}
.ez-flag-big-us {
    background-position:-9568px 0
}
.ez-flag-big-uy {
    background-position:-9614px 0
}
.ez-flag-big-uz {
    background-position:-9660px 0
}
.ez-flag-big-va {
    background-position:-9706px 0
}
.ez-flag-big-vc {
    background-position:-9752px 0
}
.ez-flag-big-ve {
    background-position:-9798px 0
}
.ez-flag-big-vg {
    background-position:-9844px 0
}
.ez-flag-big-vi {
    background-position:-9890px 0
}
.ez-flag-big-vn {
    background-position:-9936px 0
}
.ez-flag-big-vu {
    background-position:-9982px 0
}
.ez-flag-big-ws {
    background-position:-10028px 0
}
.ez-flag-big-ye {
    background-position:-10074px 0
}
.ez-flag-big-za {
    background-position:-10120px 0
}
.ez-flag-big-zm {
    background-position:-10166px 0
}
.ez-flag-big-zw {
    background-position:-10212px 0
}
.ez-flag-big-xk {
    background-position:-10258px 0
}
.ez-flag-rounded-ae {
    background-position:0 0
}
.ez-flag-rounded-af {
    background-position:-43px 0
}
.ez-flag-rounded-ag {
    background-position:-86px 0
}
.ez-flag-rounded-ai {
    background-position:-129px 0
}
.ez-flag-rounded-al {
    background-position:-172px 0
}
.ez-flag-rounded-am {
    background-position:-215px 0
}
.ez-flag-rounded-an {
    background-position:-258px 0
}
.ez-flag-rounded-ao {
    background-position:-301px 0
}
.ez-flag-rounded-ar {
    background-position:-344px 0
}
.ez-flag-rounded-aw {
    background-position:-387px 0
}
.ez-flag-rounded-bb {
    background-position:-430px 0
}
.ez-flag-rounded-bd {
    background-position:-473px 0
}
.ez-flag-rounded-bf {
    background-position:-516px 0
}
.ez-flag-rounded-bh {
    background-position:-559px 0
}
.ez-flag-rounded-bi {
    background-position:-602px 0
}
.ez-flag-rounded-bj {
    background-position:-645px 0
}
.ez-flag-rounded-bm {
    background-position:-688px 0
}
.ez-flag-rounded-bo {
    background-position:-731px 0
}
.ez-flag-rounded-br {
    background-position:-774px 0
}
.ez-flag-rounded-bt {
    background-position:-817px 0
}
.ez-flag-rounded-by {
    background-position:-860px 0
}
.ez-flag-rounded-bz {
    background-position:-903px 0
}
.ez-flag-rounded-ca {
    background-position:-946px 0
}
.ez-flag-rounded-cd {
    background-position:-989px 0
}
.ez-flag-rounded-cf {
    background-position:-1032px 0
}
.ez-flag-rounded-cg {
    background-position:-1075px 0
}
.ez-flag-rounded-ci {
    background-position:-1118px 0
}
.ez-flag-rounded-cl {
    background-position:-1161px 0
}
.ez-flag-rounded-cm {
    background-position:-1204px 0
}
.ez-flag-rounded-cn {
    background-position:-1247px 0
}
.ez-flag-rounded-co {
    background-position:-1290px 0
}
.ez-flag-rounded-cr {
    background-position:-1333px 0
}
.ez-flag-rounded-cu {
    background-position:-1376px 0
}
.ez-flag-rounded-cw {
    background-position:-1419px 0
}
.ez-flag-rounded-cy {
    background-position:-1462px 0
}
.ez-flag-rounded-de {
    background-position:-1505px 0
}
.ez-flag-rounded-dm {
    background-position:-1548px 0
}
.ez-flag-rounded-do {
    background-position:-1591px 0
}
.ez-flag-rounded-dz {
    background-position:-1634px 0
}
.ez-flag-rounded-ec {
    background-position:-1677px 0
}
.ez-flag-rounded-ee {
    background-position:-1720px 0
}
.ez-flag-rounded-eg {
    background-position:-1763px 0
}
.ez-flag-rounded-es {
    background-position:-1806px 0
}
.ez-flag-rounded-fj {
    background-position:-1849px 0
}
.ez-flag-rounded-ga {
    background-position:-1892px 0
}
.ez-flag-rounded-gb {
    background-position:-1935px 0
}
.ez-flag-rounded-gd {
    background-position:-1978px 0
}
.ez-flag-rounded-ge {
    background-position:-2021px 0
}
.ez-flag-rounded-gf {
    background-position:-2064px 0
}
.ez-flag-rounded-gh {
    background-position:-2107px 0
}
.ez-flag-rounded-gm {
    background-position:-2150px 0
}
.ez-flag-rounded-gn {
    background-position:-2193px 0
}
.ez-flag-rounded-gp {
    background-position:-2236px 0
}
.ez-flag-rounded-gt {
    background-position:-2279px 0
}
.ez-flag-rounded-gw {
    background-position:-2322px 0
}
.ez-flag-rounded-gy {
    background-position:-2365px 0
}
.ez-flag-rounded-hn {
    background-position:-2408px 0
}
.ez-flag-rounded-ht {
    background-position:-2451px 0
}
.ez-flag-rounded-id {
    background-position:-2494px 0
}
.ez-flag-rounded-in {
    background-position:-2537px 0
}
.ez-flag-rounded-iq {
    background-position:-2580px 0
}
.ez-flag-rounded-jm {
    background-position:-2623px 0
}
.ez-flag-rounded-jo {
    background-position:-2666px 0
}
.ez-flag-rounded-ke {
    background-position:-2709px 0
}
.ez-flag-rounded-kh {
    background-position:-2752px 0
}
.ez-flag-rounded-kn {
    background-position:-2795px 0
}
.ez-flag-rounded-kw {
    background-position:-2838px 0
}
.ez-flag-rounded-ky {
    background-position:-2881px 0
}
.ez-flag-rounded-la {
    background-position:-2924px 0
}
.ez-flag-rounded-lb {
    background-position:-2967px 0
}
.ez-flag-rounded-lk {
    background-position:-3010px 0
}
.ez-flag-rounded-lr {
    background-position:-3053px 0
}
.ez-flag-rounded-ls {
    background-position:-3096px 0
}
.ez-flag-rounded-lt {
    background-position:-3139px 0
}
.ez-flag-rounded-ma {
    background-position:-3182px 0
}
.ez-flag-rounded-md {
    background-position:-3225px 0
}
.ez-flag-rounded-mg {
    background-position:-3268px 0
}
.ez-flag-rounded-ml {
    background-position:-3311px 0
}
.ez-flag-rounded-mq {
    background-position:-3354px 0
}
.ez-flag-rounded-ms {
    background-position:-3397px 0
}
.ez-flag-rounded-mw {
    background-position:-3440px 0
}
.ez-flag-rounded-mx {
    background-position:-3483px 0
}
.ez-flag-rounded-my {
    background-position:-3526px 0
}
.ez-flag-rounded-mz {
    background-position:-3569px 0
}
.ez-flag-rounded-ne {
    background-position:-3612px 0
}
.ez-flag-rounded-ng {
    background-position:-3655px 0
}
.ez-flag-rounded-ni {
    background-position:-3698px 0
}
.ez-flag-rounded-np {
    background-position:-3741px 0
}
.ez-flag-rounded-nr {
    background-position:-3784px 0
}
.ez-flag-rounded-pa {
    background-position:-3827px 0
}
.ez-flag-rounded-pe {
    background-position:-3870px 0
}
.ez-flag-rounded-pg {
    background-position:-3913px 0
}
.ez-flag-rounded-ph {
    background-position:-3956px 0
}
.ez-flag-rounded-pk {
    background-position:-3999px 0
}
.ez-flag-rounded-pl {
    background-position:-4042px 0
}
.ez-flag-rounded-pr {
    background-position:-4085px 0
}
.ez-flag-rounded-ps {
    background-position:-4128px 0
}
.ez-flag-rounded-py {
    background-position:-4171px 0
}
.ez-flag-rounded-ro {
    background-position:-4214px 0
}
.ez-flag-rounded-rs {
    background-position:-4257px 0
}
.ez-flag-rounded-ru {
    background-position:-4300px 0
}
.ez-flag-rounded-rw {
    background-position:-4343px 0
}
.ez-flag-rounded-sa {
    background-position:-4386px 0
}
.ez-flag-rounded-sd {
    background-position:-4429px 0
}
.ez-flag-rounded-sn {
    background-position:-4472px 0
}
.ez-flag-rounded-so {
    background-position:-4515px 0
}
.ez-flag-rounded-sr {
    background-position:-4558px 0
}
.ez-flag-rounded-sv {
    background-position:-4601px 0
}
.ez-flag-rounded-sy {
    background-position:-4644px 0
}
.ez-flag-rounded-sz {
    background-position:-4687px 0
}
.ez-flag-rounded-tc {
    background-position:-4730px 0
}
.ez-flag-rounded-tg {
    background-position:-4773px 0
}
.ez-flag-rounded-th {
    background-position:-4816px 0
}
.ez-flag-rounded-tj {
    background-position:-4859px 0
}
.ez-flag-rounded-tn {
    background-position:-4902px 0
}
.ez-flag-rounded-to {
    background-position:-4945px 0
}
.ez-flag-rounded-tr {
    background-position:-4988px 0
}
.ez-flag-rounded-tt {
    background-position:-5031px 0
}
.ez-flag-rounded-tz {
    background-position:-5074px 0
}
.ez-flag-rounded-ua {
    background-position:-5117px 0
}
.ez-flag-rounded-ug {
    background-position:-5160px 0
}
.ez-flag-rounded-us {
    background-position:-5203px 0
}
.ez-flag-rounded-uy {
    background-position:-5246px 0
}
.ez-flag-rounded-uz {
    background-position:-5289px 0
}
.ez-flag-rounded-vc {
    background-position:-5332px 0
}
.ez-flag-rounded-vg {
    background-position:-5375px 0
}
.ez-flag-rounded-vn {
    background-position:-5418px 0
}
.ez-flag-rounded-vu {
    background-position:-5461px 0
}
.ez-flag-rounded-ws {
    background-position:-5504px 0
}
.ez-flag-rounded-xa {
    background-position:-5547px 0
}
.ez-flag-rounded-xk {
    background-position:-5590px 0
}
.ez-flag-rounded-ye {
    background-position:-5633px 0
}
.ez-flag-rounded-za {
    background-position:-5676px 0
}
.ez-flag-rounded-zm {
    background-position:-5719px 0
}
.ez-flag-rounded-zw {
    background-position:-5762px 0
}
.ez-screen-overlay {
    background:rgba(0,
    0,
    0,
    .35);
    position:fixed;
    top:0;
    right:0;
    bottom:0;
    left:0;
    z-index:999;
    display:none
}
#ez-cvv-tip {
    width:460px;
    position:relative;
    margin:0 auto
}
#ez-cvv-tip img {
    width:100%
}
#ez-cvv-tip #ez-close-cvv-tip {
    position:absolute;
    top:-41px;
    right:-30px
}
#ez-cvv-tip #ez-close-cvv-tip .fa {
    color:#fff
}
#simplemodal-overlay {
    background:#000
}
.simplemodal-container {
    height:auto!important;
    opacity:0;
    -webkit-transform:scale(.7);
    -ms-transform:scale(.7);
    transform:scale(.7);
    webkit-transition:all .2s;
    -moz-transition:all .2s;
    transition:all .2s
}
.simplemodal-container.ez-modal-scale {
    opacity:1;
    -webkit-transform:scale(1);
    -ms-transform:scale(1);
    transform:scale(1)
}
.simplemodal-container.ez-modal-scale .ez-modal-progress .ez-modal-icon .fa-check {
    -webkit-transform:scale(1);
    -ms-transform:scale(1);
    transform:scale(1)
}
.simplemodal-container.ez-modal-noanimation {
    -webkit-transition:none;
    -moz-transition:none;
    transition:none
}
.ez-big-modal .ez-modal-content {
    padDing:20px 24px 18px 20px
}
div.simplemodal-close {
    line-height:44px
}
.simplemodal-close {
    position:absolute;
    top:10px;
    right:4px;
    z-index:2;
    padDing:10px;
    line-height:normal
}
.simplemodal-close:hover {
    text-decoration:none
}
.simplemodal-close span {
    width:24px;
    height:24px;
    line-height:24px;
    border:1px solid #4c4c59;
    color:#4c4c59;
    font-size:12px;
    font-weight:300;
    text-align:center;
    display:block;
    padDing-left:1px;
    border-radius:50%
}
.simplemodal-close em {
    font-size:13px;
    font-weight:300
}
.ez-modal-wrap {
    display:none;
    box-shadow:3px,
    4px,
    4px,
    #fff;
    -webkit-box-shadow:3px,
    4px,
    4px,
    -1px,
    rgba(0,
    0,
    0,
    .4);
    -moz-box-shadow:3px,
    4px,
    4px,
    -1px,
    rgba(0,
    0,
    0,
    .4);
    box-shadow:3px 4px 4px -1px rgba(0,
    0,
    0,
    .4)
}
.ez-modal {
    text-align:center
}
.ez-modal .ez-modal-header {
    background:#e5e5e6;
    text-align:center;
    padDing:12px 20px;
    position:relative;
    z-index:2;
    bottom:-4px;
    border-top-left-radius:4px;
    border-top-right-radius:4px
}
.ez-modal .ez-modal-header h3 {
    font-size:19px;
    font-weight:bold;
    color:#4c4c59;
    padDing-right:26px
}
.ez-modal .ez-modal-header h3 .fa {
    margin:4px 10px 0 0
}
.ez-modal .ez-modal-header .fa {
    float:left;
    font-size:30px
}
.ez-modal .ez-modal-header .fa-facebook-square {
    color:#3b5998
}
.ez-modal .ez-modal-content {
    min-height:200px;
    max-height:330px;
    padDing:20px;
    overflow:auto;
    font-size:16px;
    background:#fff;
    border-radius:4px
}
.ez-modal .ez-modal-content p {
    font-weight:bold;
    line-height:30px
}
.ez-modal .ez-modal-content p span {
    font-weight:normal;
    font-size:15px
}
.ez-modal .ez-modal-footer {
    width:100%;
    min-height:70px;
    margin-top:-4px;
    display:table;
    vertical-align:middle;
    background:#e5e5e6;
    border-bottom-left-radius:4px;
    border-bottom-right-radius:4px
}
.ez-modal .ez-modal-footer button {
    height:100%;
    min-width:50%;
    padDing:0;
    display:table-cell;
    line-height:34px;
    font-size:16px;
    font-weight:bold;
    color:#4c4c59;
    background:transparent;
    border-radius:0
}
.ez-modal .ez-modal-footer button.simplemodal-left-btn {
    border-right:1px solid #cececf
}
.ez-modal .ez-modal-footer button.simplemodal-left-btn,
.ez-modal .ez-modal-footer button.simplemodal-right-btn {
    border-top:1px solid #cececf
}
.ez-modal .ez-modal-footer button:hover {
    text-decoration:underline
}
.ez-modal .ez-modal-footer button.ez-full-width {
    border:none!important
}
.ez-modal .ez-modal-footer h3 {
    font-size:24px;
    font-weight:normal
}
.ez-modal #ez-spinner {
    margin-bottom:8px
}
.ez-modal .ez-modal-icon {
    margin:12px 0 20px 0;
    display:inline-block
}
.ez-modal .ez-modal-icon .fa {
    width:55px;
    height:55px;
    line-height:55px;
    font-size:40px;
    box-sizing:initial;
    border:3px solid #4c4c59;
    border-radius:50%
}
.ez-modal .ez-modal-confirm-msg {
    font-size:18px
}
#ez-tc-modal-wrap .ez-modal-content {
    text-align:left;
    font-size:15px
}
#ez-tc-modal-wrap table {
    border-bottom:1px solid #e5e5e6;
    padDing-bottom:20px
}
#ez-tc-modal-wrap tr:first-child td:last-child strong {
    font-weight:normal
}
#ez-tc-modal-wrap td {
    width:60%
}
#ez-tc-modal-wrap td:first-child {
    width:40%
}
#ez-tc-modal-wrap h4 {
    color:#4c4c59;
    margin:20px 0 6px 0
}
#ez-tc-modal-wrap ul {
    margin-left:18px
}
#ez-tc-modal-wrap li {
    list-style-type:disc;
    line-height:20px;
    margin-bottom:10px
}
.ez-modal-progress button:after,
.ez-modal button.ez-full-width:after {
    content:'?';
    display:inline-block;
    margin-left:10px;
    font-size:24px;
    position:relative;
    top:1px
}
.ez-modal-progress {
    width:370px
}
body.ru .ez-modal button,
body.ta .ez-modal button,
body.de .ez-modal button {
    font-size:17px
}
#ez-preloader .ez-modal-content {
    background:transparent
}
#ez-preloader .ez-caption .ez-modal-content {
    background:#fff
}
#ez-preloader .ez-modal-footer {
    display:none
}
#ez-preloader .ez-modal-footer button {
    margin-top:15px
}
header,
#div-header {
    width:100%;
    height:84px;
    position:relative;
    z-index:4;
    background:#0072C6!important;
}
header .ez-wrap,
#div-header .ez-wrap {
    padDing-top:0
}
header #ez-logo-wrap,
#div-header #ez-logo-wrap {
    position:relative;
    float:left;
    margin-top:18px
}
header #ez-logo-wrap a,
#div-header #ez-logo-wrap a {
    width:250px;
    height:50px;
   
    /*background:url("../../logo/cobrand.png") 0 0 no-repeat;*/
    background-size:120px 50px;
    font-size:36px;
    font-weight: bold;
    color:white;
}
#cobrand  {
	position:relative;
	float:left;
    margin-top:10px;
    width:720px;
    height:50px;
    background:url("../../logo/cobrand.png") 0 0 no-repeat;
	 background-size:700px 50px;
    
}
	
	
	
header #ez-logo-wrap a img,
#div-header #ez-logo-wrap a img {
    float: left;
}

header #ez-logo-wrap a:hover,
#div-header #ez-logo-wrap a:hover {
    text-decoration: none;
}

header nav,
#div-header nav {
    float:right;
    margin-top:20px;
    overflow:visible
}
header nav ul,
#div-header nav ul {
    float:right
}
header nav li,
#div-header nav li {
    display:inline-block;
    padDing:0 12px
}
header nav a,
#div-header nav a {
    color:#fff;
    font-size:18px
}
header nav a:hover,
#div-header nav a:hover {
    text-decoration:none
}
header nav #ez-menu-list>li>a,
#div-header nav #ez-menu-list>li>a {
    padDing-left:5px;
    padDing-right:5px
}
header nav #ez-menu-list>li>a:hover,
#div-header nav #ez-menu-list>li>a:hover {
    border-bottom:1px solid rgba(255,
    255,
    255,
    .4)
}
header nav #ez-menu-list>li>a.ez-active,
#div-header nav #ez-menu-list>li>a.ez-active {
    border-bottom:1px solid #fff
}
header nav #ez-menu-main-items,
#div-header nav #ez-menu-main-items {
    border:1px solid rgba(255,
    255,
    255,
    .4)
}
header nav #ez-menu-main-items:hover .ez-separator,
#div-header nav #ez-menu-main-items:hover .ez-separator {
    opacity:0;
    filter:alpha(opacity=0)
}
header nav #ez-menu-main-items>li:hover,
#div-header nav #ez-menu-main-items>li:hover {
    background:rgba(255,
    255,
    255,
    .2)
}
header nav #ez-menu-main-items>li>a:hover,
#div-header nav #ez-menu-main-items>li>a:hover {
    color:#fff
}
header #ez-menu-main-items,
#div-header #ez-menu-main-items {
    margin:0 0 0 10px;
    background:rgba(255,
    255,
    255,
    .1);
    border-radius:4px
}
header #ez-menu-main-items li,
#div-header #ez-menu-main-items li {
    float:left
}
header #ez-menu-main-items .ez-separator,
#div-header #ez-menu-main-items .ez-separator {
    width:1px;
    height:14px;
    margin-top:15px;
    padDing:0;
    background:rgba(255,
    255,
    255,
    .3)
}
header #ez-menu-main-items a,
#div-header #ez-menu-main-items a {
    display:block
}
header #ez-menu-main-items span.fa-circle,
#div-header #ez-menu-main-items span.fa-circle {
    color:#ff3dd9;
    font-size:24px;
    position:absolute;
    top:-16px;
    margin-left:-7px;
    display:none
}
header #ez-menu-main-items span.fa-circle span.ez-amenu-vouchers-count,
#div-header #ez-menu-main-items span.fa-circle span.ez-amenu-vouchers-count {
    font-family:roboto;
    color:#fff;
    font-size:15px;
    position:relative;
    top:-3.3px;
    left:-14.3px
}
header #ez-menu-main-items span.fa-circle span.ez-two-digit-voucher-count,
#div-header #ez-menu-main-items span.fa-circle span.ez-two-digit-voucher-count {
    left:-18.3px
}
header .ez-drop-down-holder,
#div-header .ez-drop-down-holder {
    position:relative
}
header .ez-drop-down-holder.ez-padDing,
#div-header .ez-drop-down-holder.ez-padDing {
    padDing-right:29px
}
header .ez-drop-down-holder a,
#div-header .ez-drop-down-holder a {
    font-weight:normal!important
}
header .ez-drop-down-holder #ez-account-menu,
#div-header .ez-drop-down-holder #ez-account-menu {
    width:100%;
    max-width:160px;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis
}
header .ez-dropdown-btn,
#div-header .ez-dropdown-btn {
    cursor:pointer
}
header .ez-menu-arrow,
#div-header .ez-menu-arrow {
    position:absolute;
    top:20px;
    right:10px;
    width:1px;
    border:7px solid;
    border-color:transparent transparent transparent transparent;
    border-top-color:#fff
}
header .ez-menu-arrow.ez-active,
#div-header .ez-menu-arrow.ez-active {
    border-top-color:#33c541
}
header .ez-menu-arrow.ez-arrow-up,
#div-header .ez-menu-arrow.ez-arrow-up {
    top:13px;
    width:1px;
    border:7px solid;
    border-color:transparent transparent transparent transparent;
    border-bottom-color:#fff
}
header .ez-menu-arrow.ez-arrow-up.ez-active,
#div-header .ez-menu-arrow.ez-arrow-up.ez-active {
    border-bottom-color:#33c541
}
header .ez-drop-down,
#div-header .ez-drop-down {
    min-width:80%;
    position:absolute;
    top:58px;
    left:50%;
    cursor:default;
    display:none;
    background:#fff;
    border-radius:4px;
    -webkit-transform:translateX(-50%);
    -ms-transform:translateX(-50%);
    transform:translateX(-50%);
    box-shadow:0,
    1px,
    5px,
    #fff;
    -webkit-box-shadow:0,
    1px,
    5px,
    0,
    rgba(0,
    0,
    0,
    .4);
    -moz-box-shadow:0,
    1px,
    5px,
    0,
    rgba(0,
    0,
    0,
    .4);
    box-shadow:0 1px 5px 0 rgba(0,
    0,
    0,
    .4)
}
header .ez-drop-down .ez-drop-down-arrow,
#div-header .ez-drop-down .ez-drop-down-arrow {
    position:absolute;
    top:-20px;
    left:50%;
    margin-left:-11px;
    width:1px;
    border:11px solid;
    border-color:transparent transparent transparent transparent;
    border-bottom-color:#fff
}
header .ez-drop-down li,
#div-header .ez-drop-down li {
    min-width:100%;
    float:left;
    white-space:nowrap;
    border-top:1px solid #e7e7e7;
    padDing:0
}
header .ez-drop-down li:first-child,
#div-header .ez-drop-down li:first-child {
    border-top:0;
    border-top-left-radius:4px;
    border-top-right-radius:4px
}
header .ez-drop-down li:last-child,
#div-header .ez-drop-down li:last-child {
    border-bottom-left-radius:4px;
    border-bottom-right-radius:4px
}
header .ez-drop-down li:hover,
#div-header .ez-drop-down li:hover {
    background:#ededed
}
header .ez-drop-down a,
#div-header .ez-drop-down a {
    padDing:0 12px;
    display:block
}
header .ez-drop-down a,
header .ez-drop-down a:hover,
#div-header .ez-drop-down a,
#div-header .ez-drop-down a:hover {
    color:#4c4c59
}
body.ar header #ez-logo-wrap,
body.ar #div-header #ez-logo-wrap,
body.ur header #ez-logo-wrap,
body.ur #div-header #ez-logo-wrap {
    float:right
}
body.ar header nav,
body.ar #div-header nav,
body.ur header nav,
body.ur #div-header nav {
    float:left
}
body.ar header #ez-menu-main-items,
body.ar #div-header #ez-menu-main-items,
body.ur header #ez-menu-main-items,
body.ur #div-header #ez-menu-main-items {
    float:left
}
body.ru header nav #ez-menu-list>li>a,
body.de header nav #ez-menu-list>li>a,
body.ta header nav #ez-menu-list>li>a {
    padDing:0
}
body.ta header nav>ul>li>a {
    font-size:17px
}
.ez-header-notification-content {
    display:none
}
.ez-header-notification,
.ez-header-notification-red {
    background:#33c541
}
.ez-header-notification .ez-header-notification-content,
.ez-header-notification-red .ez-header-notification-content {
    color:#fff;
    padDing:6px 0;
    position:relative
}
.ez-header-notification .ez-header-notification-content p,
.ez-header-notification-red .ez-header-notification-content p {
    font-size:18px;
    line-height:20px;
    margin-right:35px
}
.ez-header-notification .ez-header-notification-content a,
.ez-header-notification-red .ez-header-notification-content a {
    line-height:44px;
    margin:-14px 0
}
.ez-header-notification .ez-header-notification-content .ez-mobile-link,
.ez-header-notification-red .ez-header-notification-content .ez-mobile-link {
    display:none
}
.ez-header-notification .ez-header-notification-content .ez-notification-light,
.ez-header-notification-red .ez-header-notification-content .ez-notification-light {
    font-weight:lighter
}
.ez-header-notification .ez-header-notification-content .ez-notification-alternate,
.ez-header-notification-red .ez-header-notification-content .ez-notification-alternate {
    color:#1a317d
}
.ez-header-notification .ez-header-notification-content .ez-notification-icon,
.ez-header-notification-red .ez-header-notification-content .ez-notification-icon {
    margin-right:10px
}
.ez-header-notification .ez-header-notification-content .ez-hide-notification,
.ez-header-notification-red .ez-header-notification-content .ez-hide-notification {
    font-size:24px;
    padDing:0 12px;
    color:#fff;
    margin-top:-22px;
    position:absolute;
    top:50%;
    right:-12px
}
.ez-header-notification-red {
    background:#ac0000
}
nav#ez-mobile-menu {
    width:300px;
    height:100%;
    background:#0072C6;
    padDing:30px 40px 30px 50px;
    position:fixed;
    top:0;
    right:-300px;
    z-index:110
}
nav#ez-mobile-menu .ez-asterisk {
    font-size:30px;
    position:fixed;
    right:260px;
    top:24px;
    display:none
}
nav#ez-mobile-menu .ez-asterisk:hover {
    text-decoration:none
}
nav#ez-mobile-menu #ez-mobile-menu-logo {
    width:85px;
    height:35px;
    display:block;
    float:left;
    background:url("../images/logo.svg") 0 0 no-repeat;
    background-size:85px 35px
}
nav#ez-mobile-menu .ez-menu-close-btn {
    float:right;
    color:#fff;
    margin-right:-9px
}
nav#ez-mobile-menu ul {
    margin-top:28px;
    padDing-bottom:28px;
    border-bottom:1px solid #53616e
}
nav#ez-mobile-menu ul.ez-margin {
    margin-top:10px
}
nav#ez-mobile-menu li {
    width:100%
}
nav#ez-mobile-menu li a {
    display:block;
    font-size:17px;
    color:#fff;
    font-weight:300;
    line-height:30px;
    margin-bottom:14px
}
nav#ez-mobile-menu li a span {
    font-size:22px;
    line-height:32px;
    float:right
}
nav#ez-mobile-menu li a:hover {
    text-decoration:none;
    color:#fff
}
nav#ez-mobile-menu li a.ez-active {
    color:#33c541
}
nav#ez-mobile-menu li .fa-angle-left {
    float:left;
    display:none
}
nav#ez-mobile-menu li#ez-lang-title {
    font-size:20px;
    color:#999
}
nav#ez-mobile-menu .ez-submenu {
    width:250px;
    padDing:30px 0;
    height:100%;
    position:fixed;
    top:0;
    right:-250px;
    background:#001a6e;
    overflow:auto
}
nav#ez-mobile-menu .ez-submenu .ez-submenu-header {
    padDing:0 20px;
    margin-bottom:30px
}
nav#ez-mobile-menu .ez-submenu .ez-submenu-header h3 {
    color:#fff;
    font-size:17px;
    float:left
}
nav#ez-mobile-menu .ez-submenu .ez-submenu-header .ez-menu-close-btn {
    margin-top:-7px
}
nav#ez-mobile-menu .ez-submenu ul {
    margin:0;
    padDing:24px 20px;
    border:none
}
nav#ez-mobile-menu .ez-submenu li {
    margin-bottom:8px
}
nav#ez-mobile-menu .ez-submenu .ez-menu-back-btn {
    background:#0072C6;
    color:#8a9597;
    padDing:12px 20px;
    cursor:pointer
}
nav#ez-mobile-menu .ez-submenu .ez-menu-back-btn span {
    font-size:18px;
    font-family:Roboto,
    sans-serif;
    font-weight:400
}
nav#ez-mobile-menu .ez-submenu .ez-menu-back-btn .fa {
    font-size:20px;
    margin-right:10px
}
nav#ez-mobile-menu .ez-submenu .ez-menu-back-btn .fa-angle-right {
    display:none;
    margin:0 0 0 10px
}
body.ar nav#ez-mobile-menu,
body.ur nav#ez-mobile-menu {
    right:initial;
    left:-300px;
    padDing:30px 52px 30px 40px
}
body.ar nav#ez-mobile-menu .ez-asterisk,
body.ur nav#ez-mobile-menu .ez-asterisk {
    left:260px;
    right:initial
}
body.ar nav#ez-mobile-menu .ez-submenu,
body.ur nav#ez-mobile-menu .ez-submenu {
    right:initial;
    left:-250px
}
body.ar nav#ez-mobile-menu .ez-submenu-header h3,
body.ur nav#ez-mobile-menu .ez-submenu-header h3 {
    float:right
}
body.ar nav#ez-mobile-menu #ez-mobile-menu-logo,
body.ur nav#ez-mobile-menu #ez-mobile-menu-logo {
    float:right
}
body.ar nav#ez-mobile-menu .ez-menu-close-btn,
body.ur nav#ez-mobile-menu .ez-menu-close-btn {
    float:left;
    margin:0 0 0 -9px
}
body.ar nav#ez-mobile-menu li .fa-angle-left,
body.ur nav#ez-mobile-menu li .fa-angle-left {
    display:block
}
body.ar nav#ez-mobile-menu li .fa-angle-right,
body.ur nav#ez-mobile-menu li .fa-angle-right {
    display:none
}
body.ar nav#ez-mobile-menu .ez-menu-back-btn .fa-angle-left,
body.ur nav#ez-mobile-menu .ez-menu-back-btn .fa-angle-left {
    display:none
}
body.ar nav#ez-mobile-menu .ez-menu-back-btn .fa-angle-right,
body.ur nav#ez-mobile-menu .ez-menu-back-btn .fa-angle-right {
    display:inline-block
}
#ez-menu-customer-care {
    margin-top:28px
}
#ez-menu-customer-care .ez-chat-icon {
    font-size:40px;
    color:#ff3dd9;
    float:left;
    width:100%
}
#ez-menu-customer-care .ez-live-help {
    margin:5px 0 0 0;
    float:left
}
#ez-menu-customer-care .ez-live-help span {
    line-height:18px;
    font-size:14px;
    color:#fff;
    display:block
}
#ez-menu-customer-care .ez-live-help span:first-child {
    font-size:16px;
    font-weight:bold;
    color:#ff3dd9
}
#ez-menu-customer-care p {
    font-size:12px;
    color:#8a9597;
    margin-top:4px
}
#ez-menu-customer-care p a {
    color:#fff
}
.ez-wrap {
    width:100%;
    margin:0 auto;
    padDing-left:7%;
    padDing-right:7%
}
.ez-main-wrap {
    padDing-top:40px;
    padDing-bottom:30px;
    min-height:540px
}
.ez-wrap-margin-right {
    margin-right:7%
}
.ez-wrap-margin-left {
    margin-left:7%
}
.ez-middle-content {
    max-width:600px;
    margin:0 auto
}
.ez-middle-content .ez-page-title {
    text-align:center
}
.ez-content-left-info {
    width:520px
}
.ez-select-placeholder {
    color:#b1b1c6
}
.ez-details-wrap {
    padDing:20px;
    margin-bottom:30px;
    background:#fff
}
.ez-details-wrap .ez-transaction-status .fa {
    margin-top:-2px
}
.ez-details-wrap .ez-button {
    min-width:250px
}
body.ez-main-background .ez-details-wrap {
    background:#f6f6f6
}
#ez-left-content {
    width:62%;
    float:left
}
#ez-left-content.ez-topup-wrap {
    width:41%
}
#ez-right-content {
    width:32%;
    float:right
}
#ez-right-content.ez-topup-wrap {
    width:50%
}
#ez-right-content #ez-right-content-info {
    margin-top:20px;
    padDing:20px 20px;
    background:#fff
}
#ez-right-content #ez-site-map h2 {
    margin-bottom:10px
}
#ez-right-content #ez-site-map li {
    margin-bottom:6px
}
#ez-right-content #ez-lanDing-img img {
    width:100%;
    height:auto;
    margin-bottom:-7px
}
.ez-page-title {
    margin-bottom:30px
}
.ez-page-title #ez-page-subtitle {
    margin:-3px 0 0 3px;
    font-size:18px
}
.ez-cmenu {
    width:100%;
    display:table;
    margin-bottom:30px;
    border-bottom:5px solid #33c541
}
.ez-cmenu.ez-cmenu-table li {
    float:none;
    min-width:initial
}
.ez-cmenu.ez-cmenu-table li a {
    padDing:0
}
.ez-cmenu li {
    min-width:150px;
    min-height:50px;
    display:table-cell;
    vertical-align:middle;
    text-align:center;
    background:#b4bec0;
    color:#4c4c59;
    white-space:nowrap;
    border-left:3px solid #fff;
    list-style-type:none!important;
    margin-bottom:0!important
}
.ez-cmenu li:hover,
.ez-cmenu li.ez-active {
    text-decoration:none;
    background:#eeedef
}
.ez-cmenu li.ez-active {
    cursor:default
}
.ez-cmenu li:first-child,
.ez-cmenu li.ez-noborder {
    border-left:0
}
.ez-cmenu li#ez-cmenu-mobile-btn {
    height:55px;
    display:none;
    background:#eeedef;
    text-align:left;
    cursor:pointer;
    font-size:18px;
    line-height:55px!important
}
.ez-cmenu li#ez-cmenu-mobile-btn span.fa {
    margin:15px 6px 0 0;
    float:right;
    color:#001a6e;
    font-size:28px
}
.ez-cmenu li a {
    font-size:18px;
    display:block;
    color:inherit;
    padDing:0 6px
}
.ez-cmenu li a:hover {
    text-decoration:none
}
body.ar .ez-cmenu li:first-child,
body.ar .ez-cmenu li.ez-noborder,
body.ur .ez-cmenu li:first-child,
body.ur .ez-cmenu li.ez-noborder {
    border-left:3px solid #fff
}
body.ar .ez-cmenu li:last-child,
body.ur .ez-cmenu li:last-child {
    border-left:0
}
body.ta .ez-cmenu li a {
    font-size:17px
}
.auto-resizable-iframe {
    margin:0 auto
}
.auto-resizable-iframe>div {
    position:relative;
    padDing-bottom:75%;
    height:0
}
.auto-resizable-iframe iframe {
    position:absolute;
    top:0;
    left:0;
    width:100%;
    height:100%
}
.ez-form-row {
    min-height:114px;
    clear:both;
    position:relative
}
.ez-form-row.ez-airtime-row {
    min-height:84px;
    margin-top:50px
}
.ez-form-row-margin {
    margin-bottom:30px
}
.ez-form-row-captcha {
    height:200px;
    margin-bottom:5px
}
.ez-form-checkbox {
    margin-top:4px
}
.ez-form-checkbox .ez-checkbox-wrap {
    float:left;
    margin-right:8px
}
.ez-form-checkbox label {
    font-size:18px;
    display:block;
    margin-bottom:20px
}
body.ar .ez-form-checkbox .ez-checkbox-wrap,
body.ur .ez-form-checkbox .ez-checkbox-wrap {
    float:right;
    margin-right:0;
    margin-left:8px
}
.ez-form-hint {
    font-size:13px;
    padDing:3px 5px 0 5px
}
.ez-show-password {
    font-size:22px;
    line-height:44px;
    color:#8a9597;
    position:absolute;
    right:9px;
    top:39px;
    z-index:3
}
.ez-show-password:hover {
    text-decoration:none
}
.no-svg .ez-show-password {
    display:none
}
.ez-auth-type-btn .fa-facebook-square,
.ez-auth-type-btn .fa-envelope {
    float:left;
    line-height:inherit;
    font-size:26px;
    margin-left:5px
}
.ez-auth-type-btn .fa-facebook-square {
    font-size:30px
}
.ez-hr-divide {
    display:table;
    text-align:center;
    padDing:0 30px
}
.ez-hr-divide h4 {
    white-space:pre;
    padDing:0 15px;
    color:#b9c7c4
}
.ez-hr-divide:before,
.ez-hr-divide:after {
    width:50%;
    content:'';
    display:table-cell;
    background:#b9c7c4;
    -webkit-transform:scaleY(.05);
    -ms-transform:scaleY(.05);
    transform:scaleY(.05)
}
#ez-signup-with-email {
    margin-top:16px
}
.ez-register-subheaDing {
    margin-bottom:20px
}
.ez-table {
    width:100%;
    border-collapse:collapse
}
.ez-table.ez-bottom-margin {
    margin-bottom:20px
}
.ez-table tr {
    background:#eeedef
}
.ez-table tr:nth-child(odd) {
    background:#f7f7f7
}
.ez-table tr.ez-more-info-item {
    display:none
}
.ez-table th,
.ez-table td {
    text-align:left;
    font-size:16px
}
.ez-table th:first-child,
.ez-table td:first-child {
    padDing-left:16px
}
.ez-table th {
    padDing-right:10px;
    background:#001a6e;
    color:#fff;
    font-weight:normal;
    line-height:48px
}
.ez-table th .fa {
    font-size:14px;
    margin-left:8px;
    opacity:.4;
    filter:alpha(opacity=40);
    line-height:48px
}
.ez-table th .ez-ellipsis-row {
    padDing-right:6px;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis
}
.ez-table td {
    font-weight:300;
    line-height:55px;
    font-size:16px;
    position:relative
}
.ez-table td a {
    margin-left:30px;
    float:left
}
.ez-table td a:first-child {
    margin-left:0
}
.ez-table td a.ez-noleftmargin {
    margin-left:0
}
.ez-table td span {
    white-space:nowrap
}
.ez-table td .ez-transaction-status {
    height:100%
}
.ez-table td .ez-transaction-status .fa {
    line-height:56px
}
.ez-table td .ez-button {
    min-width:140px;
    line-height:44px
}
.ez-table td.ez-table-amount span {
    text-transform:uppercase
}
.ez-table td .ez-flag {
    display:inline-block;
    margin:0 5% -2px 5%
}
.ez-table .ez-resend-mobile {
    width:100%;
    margin-left:0
}
.ez-table .ez-resend-mobile .fa-refresh {
    width:26px;
    height:26px;
    text-align:center;
    line-height:26px;
    font-size:17px;
    color:#fff;
    float:0;
    background:#001a6e;
    border-radius:50%
}
.ez-table .ez-show-details-mobile {
    width:100%;
    height:100%;
    position:absolute;
    top:0;
    left:0;
    margin:0
}
.ez-table .ez-table-row-compact {
    height:56px;
    display:none;
    position:relative;
    line-height:14px
}
.ez-table .ez-table-row-compact a.fa-chevron-right {
    color:#4c4c59
}
.ez-table .ez-table-row-compact .fa-chevron-right {
    padDing:18px;
    position:absolute;
    font-size:12px;
    top:6px;
    right:3px
}
.ez-table .ez-table-row-compact span {
    white-space:nowrap
}
.ez-table .ez-table-row-compact .ez-table-bold {
    font-weight:normal;
    font-size:18px;
    padDing-bottom:6px
}
body.ta .ez-table th,
body.ru .ez-table th {
    font-size:16px
}
body.ta .ez-table td,
body.ru .ez-table td {
    font-size:15px
}
body.ta .ez-table button,
body.ta .ez-table .ez-button,
body.ru .ez-table button,
body.ru .ez-table .ez-button {
    font-size:14px!important
}
body.ar .ez-table th,
body.ar .ez-table td,
body.ur .ez-table th,
body.ur .ez-table td {
    text-align:right
}
body.ar .ez-table th:first-child,
body.ar .ez-table td:first-child,
body.ur .ez-table th:first-child,
body.ur .ez-table td:first-child {
    padDing-left:0;
    padDing-right:16px
}
body.ar .ez-table th a,
body.ar .ez-table td a,
body.ur .ez-table th a,
body.ur .ez-table td a {
    margin-left:0;
    margin-right:30px;
    float:right
}
body.ar .ez-table th a:first-child,
body.ar .ez-table td a:first-child,
body.ur .ez-table th a:first-child,
body.ur .ez-table td a:first-child {
    margin-right:0
}
.ez-show-more {
    margin-top:10px
}
.ez-show-more a {
    font-size:18px;
    color:#4c4c59;
    font-weight:bold;
    background:#eeedef;
    padDing:0 18px;
    border-radius:4px
}
.ez-show-more a:hover {
    text-decoration:none
}
.ez-show-more .fa {
    margin-left:6px;
    font-size:15px
}
#ez-cookies-table li {
    float:left;
    margin-bottom:12px;
    font-size:16px;
    line-height:22px;
    list-style-type:none!important
}
#ez-cookies-table li.ez-bold {
    margin-bottom:8px
}
#ez-cookies-table li:nth-child(4n+1) {
    width:22%;
    padDing-right:6px;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis
}
#ez-cookies-table li:nth-child(4n+2) {
    width:17%;
    padDing-right:6px
}
#ez-cookies-table li:nth-child(4n+3) {
    width:16%;
    padDing-right:6px
}
#ez-cookies-table li:nth-child(4n+4) {
    width:45%
}
#ez-cookies-table li a {
    margin:-12px 0
}
.ez-list>li {
    list-style-type:none!important;
    margin-bottom:28px!important
}
.ez-list li.ez-list-item {
    width:19%;
    float:left;
    margin:0 0 28px 1.2%;
    position:relative
}
.ez-list li.ez-list-item p {
    padDing:5px 0;
    font-size:16px!important;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis
}
.ez-list li.ez-list-item p a {
    color:#fff
}
.ez-list li.ez-list-item.ez-one-item-row {
    width:210px
}
.ez-list li.ez-list-item.ez-five-items-row {
    margin-left:0;
    clear:both
}
.ez-list li.ez-list-item.ez-four-items-row {
    clear:none
}
.ez-list li.ez-list-item.ez-three-items-row {
    clear:none
}
.ez-list li.ez-list-item>div:first-child {
    height:70px
}
.ez-list li.ez-list-item .ez-flag-big {
    float:left;
    margin:20px 0 0 20px
}
.ez-list li.ez-list-item .ez-item-amount-wrap {
    float:right;
    margin:15px 6px 0 0
}
.ez-list li.ez-list-item .ez-item-amount-wrap span {
    font-weight:normal
}
.ez-list li.ez-list-item .ez-item-amount-wrap .ez-item-amount {
    font-size:22px
}
.ez-list li.ez-list-item .ez-item-amount-wrap .ez-item-amount-text {
    font-size:15px
}
.ez-list li.ez-list-item .ez-list-item-arrow {
    position:absolute;
    bottom:-29px;
    left:50%;
    margin-left:-12px;
    display:none;
    width:1px;
    border:12px solid;
    border-color:transparent transparent transparent transparent;
    border-bottom-color:#727272
}
.ez-list li.ez-list-item .ez-item-link {
    position:absolute;
    top:0;
    left:0;
    right:0;
    bottom:0
}
.ez-table-list {
    width:100%;
    clear:both;
    direction:ltr
}
.ez-table-list li {
    max-width:48%;
    text-align:left;
    margin-bottom:6px;
    font-size:20px;
    font-weight:300
}
.ez-table-list li.ez-first-cell {
    float:left;
    clear:both
}
.ez-table-list li.ez-last-cell {
    text-align:right;
    float:right;
    font-weight:normal;
    font-family:Roboto,
    sans-serif;
    overflow:hidden;
    overflow-wrap:break-word
}
.ez-table-list li.ez-row-line {
    width:100%;
    max-width:100%;
    height:4px;
    clear:both;
    margin-bottom:8px;
    border-bottom:1px solid #c2c2c2
}
.ez-table-list li.ez-row-margin {
    margin-bottom:20px
}
.ez-table-list li.ez-summary {
    font-size:24px
}
.ez-table-list.ez-table-list-summary li {
    font-size:24px
}
.ez-summary-margin {
    height:20px
}
body.ar .ez-table-list li.ez-first-cell,
body.ur .ez-table-list li.ez-first-cell {
    float:right
}
body.ar .ez-table-list li.ez-last-cell,
body.ur .ez-table-list li.ez-last-cell {
    float:left
}
.ez-alert-box {
    max-width:520px;
    padDing-left:44px;
    border:1px solid transparent;
    position:relative;
    display:inline-block;
    margin-bottom:36px;
    border-radius:4px
}
.ez-alert-box.ez-third-color .fa {
    color:#33c541
}
.ez-alert-box .fa {
    position:absolute;
    top:12px;
    left:12px;
    color:#fff;
    font-size:20px
}
.ez-alert-box .fa.fa-info {
    top:13px;
    left:18px
}
.ez-alert-box .fa.fa-check {
    left:8px
}
.ez-alert-box h2 {
    font-size:20px;
    line-height:22px;
    margin-bottom:4px;
    text-transform:none;
    white-space:pre-line
}
.ez-alert-box.ez-success-box {
    background:#33c541;
    border-color:#33c541
}
.ez-alert-box.ez-success-box h2 {
    color:#33c541
}
.ez-alert-box.ez-info-box {
    background:#001a6e;
    border-color:#001a6e
}
.ez-alert-box.ez-info-box h2 {
    color:#001a6e
}
.ez-alert-box.ez-error-box {
    background:#ac0000;
    border-color:#ac0000
}
.ez-alert-box.ez-error-box h2 {
    color:#ac0000
}
.ez-alert-box.ez-hidden {
    display:none
}
.ez-alert-box .ez-alert-box-message {
    word-break:break-word;
    min-height:40px;
    background:#fff;
    padDing:12px 20px;
    border-top-right-radius:4px;
    border-bottom-right-radius:4px
}
.ez-alert-box .ez-alert-box-message span {
    color:#ff3dd9
}
.ez-alert-box .ez-alert-box-message em {
    color:#001a6e
}
.ez-alert-box .ez-alert-box-message ul {
    padDing:0 20px 12px 20px
}
.ez-alert-box .ez-alert-box-message ul li {
    list-style-type:disc
}
.ez-alert-box .ez-alert-box-message p {
    white-space:pre-line
}
.ez-alert-box a {
    margin:0
}
.ez-trust-panel {
    text-align:center;
    color:#001a6e;
    display:table
}
.ez-trust-panel.ez-one-line span {
    float:left;
    text-align:left
}
.ez-trust-panel.ez-one-line .ez-trust-text {
    margin:8px 0 0 10px
}
.ez-trust-panel li {
    width:32%;
    display:table-cell;
    vertical-align:top
}
.ez-trust-panel li:first-child {
    width:33%
}
.ez-trust-panel li.ez-nomargin {
    margin-left:0
}
.ez-trust-panel li.ez-list-brake {
    display:block
}
.ez-trust-panel li .ez-trust-icon {
    width:52px;
    height:52px;
    display:block;
    margin:0 auto;
    background-position:0 0;
    background-repeat:no-repeat;
    background-size:cover
}
.ez-trust-panel li .ez-trust-text {
    line-height:18px;
    display:block;
    margin-top:10px;
    font-size:16px
}
.ez-trust-panel li .ez-trust-h {
    display:block;
    font-weight:bold
}
#ez-asterisks-rain {
    position:relative
}
#ez-asterisks-rain .ez-asterisk {
    position:absolute
}
#ez-asterisks-rain .ez-asterisk.ez-55 {
    top:65px;
    left:21%;
    font-size:55px
}
#ez-asterisks-rain .ez-asterisk.ez-60 {
    top:20px;
    left:56%;
    font-size:60px
}
#ez-asterisks-rain .ez-asterisk.ez-75 {
    top:230px;
    left:10%;
    font-size:75px
}
#ez-asterisks-rain .ez-asterisk.ez-100 {
    top:120px;
    left:40%;
    font-size:100px
}
#ez-asterisks-rain .ez-asterisk.ez-120 {
    top:270px;
    left:60%;
    font-size:120px
}
.ez-airtime-toggle {
    position:absolute;
    top:12px;
    right:14px;
    z-index:10
}
.ez-airtime-toggle label {
    font-size:13px;
    font-weight:bold;
    color:#c6cad9;
    cursor:pointer
}
.ez-airtime-toggle label.ez-active,
.ez-airtime-toggle label:hover {
    color:#001a6e
}
.ez-airtime-toggle label.ez-active {
    cursor:default
}
.ez-airtime-toggle input[type=radio] {
    opacity:0
}
.ez-airtime-toggle .ez-separator {
    width:1px;
    height:16px;
    margin:0 -6px 0 7px;
    display:inline-block;
    background:#c6cad9;
    position:relative;
    top:4px
}
.ez-countrypage #ez-topup-number-label,
.ez-countryoperatorpage #ez-topup-number-label {
    display:none
}
.ez-countrypage .ez-topup-number,
.ez-countryoperatorpage .ez-topup-number {
    outline:none
}
.ez-countrypage .ez-airtime-wrap,
.ez-countryoperatorpage .ez-airtime-wrap {
    border:3px solid #319fdf
}
.ez-countrypage .ez-airtime-wrap ul,
.ez-countryoperatorpage .ez-airtime-wrap ul {
    border:none
}
.ez-airtime-wrap {
    height:50px;
    position:relative
}
.ez-airtime-wrap ul {
    width:100%;
    height:100%;
    border:1px solid #c2c2c2;
    border-radius:4px
}
.ez-airtime-wrap ul li {
    height:100%;
    overflow:hidden;
    white-space:nowrap
}
.ez-airtime-wrap ul li input {
    height:48px;
    border:none!important;
    outline:none!important;
    font-family:Roboto,
    sans-serif!important;
    border-top-left-radius:0;
    border-bottom-left-radius:0
}
.ez-airtime-wrap ul li input:focus {
    border:none!important
}
.ez-airtime-wrap ul.ng-focused {
    border:1px solid #2c9ada!important;
    box-shadow:0,
    0,
    5px,
    #fff;
    -webkit-box-shadow:0,
    0,
    5px,
    0,
    rgba(44,
    154,
    218,
    .8);
    -moz-box-shadow:0,
    0,
    5px,
    0,
    rgba(44,
    154,
    218,
    .8);
    box-shadow:0 0 5px 0 rgba(44,
    154,
    218,
    .8)
}
.ez-airtime-wrap .ez-airtime-btn {
    min-width:37px;
    height:inherit;
    float:left;
    position:relative;
    cursor:pointer;
    line-height:normal;
    background:#fff;
    border-top-left-radius:4px;
    border-bottom-left-radius:4px
}
.ez-airtime-wrap .ez-airtime-btn.ez-airtime-full-btn {
    width:100%;
    border-radius:4px
}
.ez-airtime-wrap .ez-airtime-btn.ez-airtime-full-btn .ez-btn-icons {
    border:none
}
.ez-airtime-wrap .ez-airtime-btn.ez-airtime-full-btn .ez-nocountry-placeholder {
    display:block
}
.ez-airtime-wrap .ez-airtime-btn .ez-nocountry-placeholder {
    position:absolute;
    left:40px;
    top:50%;
    font-size:22px;
    color:#b1b1c6;
    display:none;
    -webkit-transform:translateY(-50%);
    -ms-transform:translateY(-50%);
    transform:translateY(-50%)
}
.ez-airtime-wrap .ez-airtime-btn .ez-btn-icons {
    width:100%;
    text-align:left;
    position:relative;
    top:50%;
    -webkit-transform:translateY(-50%);
    -ms-transform:translateY(-50%);
    transform:translateY(-50%)
}
.ez-airtime-wrap .ez-airtime-btn .fa-globe {
    margin-left:10px;
    font-size:27px;
    color:#5CB8E7;
	position:absolute;
}
.ez-airtime-wrap .ez-airtime-btn .fa-money {
    float:left;
    margin-left:10px;
    font-size:27px;
    color:#b1b1c6
}
.ez-airtime-wrap .ez-airtime-btn .fa-angle-down {
    padDing:8px 9px;
    float:right;
    background:#fff;
    margin-top:-6px;
    position:relative;
    font-size:26px;
    z-index:3
}
.ez-airtime-wrap .ez-airtime-btn .ez-flag,
.ez-airtime-wrap .ez-airtime-btn .ez-phone-prefix,
.ez-airtime-wrap .ez-airtime-btn .ez-btn-border {
    display:inline-block;
    vertical-align:middle
}
.ez-airtime-wrap .ez-airtime-btn .ez-flag {
    margin:0 0 0 10px
}
.ez-airtime-wrap .ez-airtime-btn .ez-phone-prefix {
    margin-right:5px;
    margin-left:5px;
    font-size:24px;
    font-family:Roboto,
    sans-serif
}
.ez-airtime-wrap .ez-airtime-btn .ez-btn-border {
    width:2px;
    height:30px;
    border-left:1px solid #c2c2c2
}
.ez-airtime-wrap .ez-airtime-btn select {
    width:100%;
    height:100%;
    position:absolute;
    -webkit-appearance:none;
    top:0;
    left:0;
    z-index:4;
    cursor:pointer;
    opacity:0;
    filter:alpha(opacity=0)
}
body.ar .ez-airtime-wrap .ez-airtime-btn .ez-nocountry-placeholder,
body.ur .ez-airtime-wrap .ez-airtime-btn .ez-nocountry-placeholder {
    right:40px;
    left:initial;
    margin-top:4px
}
body.ar .ez-airtime-wrap .ez-airtime-btn .ez-btn-icons,
body.ur .ez-airtime-wrap .ez-airtime-btn .ez-btn-icons {
    text-align:right
}
body.ar .ez-airtime-wrap .ez-airtime-btn .fa-globe,
body.ur .ez-airtime-wrap .ez-airtime-btn .fa-globe {
    margin-right:10px
}
body.ar .ez-airtime-wrap .ez-airtime-btn .fa-angle-down,
body.ur .ez-airtime-wrap .ez-airtime-btn .fa-angle-down {
    float:left
}
body.ar .ez-airtime-wrap .ez-airtime-btn.ez-airtime-full-btn .ez-flag,
body.ur .ez-airtime-wrap .ez-airtime-btn.ez-airtime-full-btn .ez-flag {
    margin-left:0;
    margin-right:10px
}
#use-voucher-modal .ez-second-block {
    width:70%;
    font-size:16px;
    line-height:22px;
    display:table;
    margin-top:14px
}
#use-voucher-modal .ez-second-block .ez-note-head {
    display:table-cell;
    padDing-right:6px;
    color:#ac0000
}
#use-voucher-modal .ez-second-block .ez-note-text {
    display:table-cell
}
#use-voucher-modal .simplemodal-right-btn {
    background-color:#33c541
}
body.ar #use-voucher-modal button {
    font-size:15px
}
#ez-operators-wrap,
#ez-denominations-wrap {
    margin-bottom:18px
}
#ez-operators-wrap .ez-topup-carousel-item p {
    font-family:Roboto,
    sans-serif
}
#ez-operators-wrap .ez-change-operator-btn {
    margin-left:20px;
    position:relative;
    top:7px;
    float:right
}
body.ar #ez-operators-wrap .ez-change-operator-btn,
body.ur #ez-operators-wrap .ez-change-operator-btn {
    margin-left:0;
    margin-right:20px;
    float:left
}
#ez-denominations {
    clear:both
}
#ez-denominations .ez-topup-carousel-item div span.ez-promotion-text {
    color:#33c541
}
#ez-denominations .ez-topup-carousel-item div span.ez-promotion-black {
    color:#000
}
#ez-denominations .ez-topup-carousel-item div span.ez-promotion-inline {
    display:inline
}
#ez-denominations .ez-topup-carousel-item div span.ez-strikethrough {
    margin-left:25px;
    margin-right:25px;
    margin-top:-10px;
    margin-bottom:10px;
    border-bottom-width:0;
    border-top-width:1px;
    border-color:#33c541;
    border-style:solid
}
#ez-denominations .ez-topup-carousel-item p {
    padDing:3px 0 5px;
    line-height:16px
}
#ez-denominations-tax {
    line-height:10px;
    font-size:14px
}
#ez-order-summary {
    margin-top:30px;
    padDing:20px;
    background:#fff
}
#ez-order-summary label {
    margin-bottom:0
}
#ez-order-summary hr {
    margin:12px 0 20px 0
}
#ez-order-summary .ez-mobile-menu div {
    min-height:10px
}
.ez-available-select {
    position:relative;
    top:-24px;
    display:none;
    float:right
}
.ez-available-select span {
    font-size:16px;
    color:#005bac
}
.ez-available-select select {
    width:100%;
    height:32px;
    position:absolute;
    top:-4px;
    left:0;
    opacity:0;
    filter:alpha(opacity=0);
    cursor:pointer
}
#ez-paypal-link {
    color:#fff;
    font-weight:300;
    margin-bottom:-10px;
    display:none
}
#ez-paypal-link.ez-three-btns {
    margin-right:25%;
    float:right
}
body.ez-paybtn-var1 #ez-paypal-btn {
    display:none
}
body.ez-paybtn-var1 #ez-paypal-link {
    display:block
}
body.ez-paybtn-var1 .ez-topup-btns button.ez-three-btns.ez-float-left-btn {
    float:none!important;
    margin-right:12px
}
#ez-payment-type-btns .ez-variant {
    display:none
}
body.ez-paybtn-var2 #ez-payment-type-btns .ez-variant {
    display:block
}
body.ez-paybtn-var2 .ez-original {
    display:none
}
.ez-topup-btns {
    background:#8a9597;
    padDing:10px 0;
    text-align:center
}
.ez-topup-btns button,
.ez-topup-btns input[type="submit"],
.ez-topup-btns .ez-button {
    min-width:24%
}
.ez-topup-btns button.ez-two-btns,
.ez-topup-btns input[type="submit"].ez-two-btns,
.ez-topup-btns .ez-button.ez-two-btns {
    min-width:250px
}
.ez-topup-btns button.ez-margin-btn,
.ez-topup-btns input[type="submit"].ez-margin-btn,
.ez-topup-btns .ez-button.ez-margin-btn {
    margin-left:20px
}
.ez-topup-btns button.ez-mobile-full-width,
.ez-topup-btns input[type="submit"].ez-mobile-full-width,
.ez-topup-btns .ez-button.ez-mobile-full-width {
    min-width:32%
}
.ez-topup-btns p {
    color:#fff;
    font-size:16px;
    line-height:20px;
    text-align:center;
    margin-bottom:10px
}
body.ar .ez-topup-btns button.ez-margin-btn,
body.ar .ez-topup-btns input[type="submit"].ez-margin-btn,
body.ar .ez-topup-btns .ez-button.ez-margin-btn,
body.ur .ez-topup-btns button.ez-margin-btn,
body.ur .ez-topup-btns input[type="submit"].ez-margin-btn,
body.ur .ez-topup-btns .ez-button.ez-margin-btn {
    margin-left:0;
    margin-right:20px
}
#ez-sms-wrap textarea {
    min-height:64px
}
#ez-sms-wrap #ez-sms-textarea-wrap {
    margin-bottom:24px
}
#ez-sms-wrap #ez-sms-textarea {
    vertical-align:top
}
#ez-sms-wrap .ez-from-checkbox {
    margin-bottom:0
}
#ez-sms-wrap .ez-sms-counter {
    display:block;
    float:right;
    font-size:14px;
    line-height:24px;
    margin-right:3px
}
#ez-sms-fee {
    float:left;
    font-size:14px;
    margin-left:3px;
    line-height:24px
}
#ez-promocode {
    margin-top:6px
}
#ez-promocode .ez-form-checkbox {
    margin-bottom:0
}
#ez-promocode #ez-promocode-textbox-wrap {
    margin-top:6px;
    min-height:70px
}
.ez-promocode-voucher-notice {
    font-family:Roboto;
    font-size:14px;
    line-height:18px;
    margin-top:5px;
    margin-left:4px;
    display:inline-block
}
.ez-promocode-voucher-notice .ez-vouchers-notice-red {
    color:#ac0000
}
.ez-ild-balance {
    margin-bottom:26px
}
.ez-ild-balance .ez-balance-icon {
    width:50px;
    height:50px;
    text-align:center;
    color:#fff;
    float:left;
    margin-right:14px;
    background:#33c541;
    border-radius:4px
}
.ez-ild-balance .ez-balance-icon span {
    font-size:35px;
    line-height:50px
}
.ez-ild-balance p {
    font-size:20px;
    float:left;
    white-space:nowrap
}
.ez-ild-balance p span {
    font-size:30px;
    color:#33c541
}
#ez-ild-add-credit {
    position:relative
}
#ez-ild-add-credit span {
    width:76px;
    line-height:50px;
    position:absolute;
    left:10px;
    z-index:2;
    display:inline-block;
    font-size:24px;
    font-family:Roboto,
    sans-serif;
    direction:ltr
}
#ez-ild-add-credit input {
    padDing-left:96px;
    direction:ltr;
    font-family:Roboto,
    sans-serif
}
#ez-ild-add-credit .ez-separator {
    width:1px;
    height:30px;
    display:inline-block;
    background:#c2c2c2;
    position:absolute;
    top:10px;
    left:86px;
    z-index:2
}
body.ar #ez-ild-add-credit span {
    width:96px;
    font-size:18px
}
body.ar #ez-ild-add-credit .ez-separator {
    left:110px
}
body.ar #ez-ild-add-credit input {
    padDing-left:118px
}
#ez-ild-rates-wrap .ez-ild-rates-row {
    width:100%
}
#ez-ild-rates-wrap .ez-ild-rates-row.ez-rates-fixed {
    margin-top:4px
}
#ez-ild-rates-wrap .ez-rates-icon {
    width:54px;
    height:54px;
    float:left;
    text-align:center;
    margin:0 16px 10px 0;
    background:#001a6e;
    border-radius:50%
}
#ez-ild-rates-wrap .ez-rates-icon em {
    font-size:35px;
    line-height:54px;
    color:#fff
}
#ez-ild-rates-wrap div:first-child .ez-rates-icon {
    background:#33c541
}
#ez-ild-rates-wrap ul {
    padDing-top:7px;
    float:left
}
#ez-ild-rates-wrap ul li {
    font-weight:300
}
#ez-ild-rates-wrap ul li:first-child {
    font-size:24px;
    font-weight:normal
}
body.ar #ez-ild-rates-wrap .ez-rates-icon,
body.ur #ez-ild-rates-wrap .ez-rates-icon {
    float:right;
    margin-left:16px;
    margin-right:0
}
body.ar #ez-ild-rates-wrap ul,
body.ur #ez-ild-rates-wrap ul {
    float:right
}
#ez-ild-promocode-wrap {
    margin-top:10px
}
#ez-operators-wrap a {
    font-size:22px
}
#ez-topup-section .ez-modal-terms-btn {
    height:auto;
    position:static;
    font-size:13px;
    line-height:32px;
    margin:-8px 0 -6px 1px;
    float:left
}
#ez-topup-section .ez-promo-details {
    width:80%;
    padDing-left:5px
}
#ez-topup-section .ez-promotion {
    padDing:10px 10px 3px 8px
}
body.ar #ez-topup-section .ez-modal-terms-btn,
body.ur #ez-topup-section .ez-modal-terms-btn {
    float:right;
    margin-top:-2px
}
#ez-paypal-btn {
    font-family:Roboto,
    sans-serif
}
.ez-transaction-status .fa {
    margin-right:10px;
    font-size:27px!important
}
.ez-transaction-status span {
    float:left
}
.ez-transaction-status a {
    float:left;
    line-height:normal
}
.ez-transaction-status a.fa:hover {
    opacity:.85
}
.ez-transaction-status .fa-check-circle {
    color:#33c541
}
.ez-transaction-status .fa-times-circle {
    color:#ac0000
}
.ez-transaction-status .fa-clock-o {
    color:#001a6e
}
.ez-table-list .ez-transaction-status .fa {
    line-height:32px
}
body.ar .ez-transaction-status .fa,
body.ur .ez-transaction-status .fa {
    margin-right:0;
    margin-left:10px
}
body.ar .ez-transaction-status span,
body.ur .ez-transaction-status span {
    float:right
}
.ez-table .ez-transaction-status .ez-table-success-text {
    line-height:54px
}
.owl-carousel .owl-wrapper:after {
    content:".";
    display:block;
    clear:both;
    visibility:hidden;
    line-height:0;
    height:0
}
.owl-carousel {
    display:none;
    position:relative;
    width:100%;
    direction:ltr;
    -ms-touch-action:pan-y
}
.owl-carousel .owl-wrapper {
    display:none;
    position:relative;
    -webkit-transform:translate3d(0,
    0,
    0)
}
.owl-carousel .owl-wrapper-outer {
    overflow:hidden;
    position:relative;
    width:100%
}
.owl-carousel .owl-wrapper-outer.autoHeight {
    -webkit-transition:height 500ms ease-in-out;
    -moz-transition:height 500ms ease-in-out;
    -ms-transition:height 500ms ease-in-out;
    -o-transition:height 500ms ease-in-out;
    transition:height 500ms ease-in-out
}
.owl-carousel .owl-item {
    float:left
}
.owl-controls .owl-page,
.owl-controls .owl-buttons div {
    cursor:pointer
}
.owl-controls {
    -webkit-user-select:none;
    -khtml-user-select:none;
    -moz-user-select:none;
    -ms-user-select:none;
    user-select:none;
    -webkit-tap-highlight-color:transparent
}
.grabbing {
    cursor:e-resize
}
.owl-carousel .owl-wrapper,
.owl-carousel .owl-item {
    -webkit-backface-visibility:hidden;
    -moz-backface-visibility:hidden;
    -ms-backface-visibility:hidden;
    -webkit-transform:translate3d(0,
    0,
    0);
    -moz-transform:translate3d(0,
    0,
    0);
    -ms-transform:translate3d(0,
    0,
    0)
}
.owl-wrapper-outer {
    padDing-bottom:4px;
    margin-bottom:-4px
}
.owl-item {
    margin:0 0 0 -10px
}
.owl-item:first-child {
    margin-left:0
}
.owl-item .ez-topup-carousel-item {
    margin:0;
    float:left!important
}
.ez-topup-carousel {
    margin:0 0 0 -11px
}
.ez-topup-carousel-item {
    width:23%;
    background:#fff;
    text-align:center;
    cursor:pointer;
    margin:0 0 13px 2%;
    float:left;
    box-shadow:0,
    2px,
    5px,
    #fff;
    -webkit-box-shadow:0,
    2px,
    5px,
    -1px,
    rgba(0,
    0,
    0,
    .5);
    -moz-box-shadow:0,
    2px,
    5px,
    -1px,
    rgba(0,
    0,
    0,
    .5);
    box-shadow:0 2px 5px -1px rgba(0,
    0,
    0,
    .5)
}
.ez-topup-carousel-item.ez-fixed-width {
    width:112px
}
.ez-topup-carousel-item img,
.ez-topup-carousel-item .ez-background-img {
    vertical-align:middle;
    width:68px;
    height:34px;
    margin-top:10px
}
.ez-topup-carousel-item .ez-background-img {
    display:inline-block
}
.ez-topup-carousel-item>div {
    min-height:56px
}
.ez-topup-carousel-item>div span {
    display:block
}
.ez-topup-carousel-item span.ez-currency {
    padDing-top:5px;
    line-height:18px;
    font-size:14px;
    color:#8a9597
}
.ez-topup-carousel-item span.ez-amount {
    font-size:24px;
    margin-bottom:2px
}
.ez-topup-carousel-item p {
    padDing:7px 0;
    font-size:12px;
    font-weight:normal;
    line-height:normal;
    color:#fff;
    border-left:3px solid transparent;
    margin-bottom:0!important;
    box-sizing:content-box;
    background:#8a9597
}
.ez-topup-carousel-item.ez-operator-long p {
    height:28px
}
.ez-topup-carousel-item.ez-active p {
    background:#001a6e;
    border-left:3px solid #ff3dd9
}
body.ar .ez-topup-carousel-item,
body.ur .ez-topup-carousel-item {
    float:right
}
.ez-promotion-wrap {
    padDing-left:45px;
    background:#001a6e;
    border:1px solid #001a6e;
    clear:both;
    position:relative;
    border-radius:4px
}
.ez-promotion-wrap .fa-tags {
    position:absolute;
    top:12px;
    left:12px;
    color:#fff;
    font-size:20px
}
.ez-promotion-wrap.ez-topup-promotion {
    margin-bottom:30px
}
.ez-promotion-wrap.ez-topup-promotion img {
    display:block;
    margin:-6px auto 10px auto
}
body.ar .ez-promotion-wrap,
body.ur .ez-promotion-wrap {
    padDing-right:45px;
    padDing-left:0
}
body.ar .ez-promotion-wrap .fa-tags,
body.ur .ez-promotion-wrap .fa-tags {
    left:initial;
    right:12px
}
body.ar .ez-promotion,
body.ur .ez-promotion {
    padDing:10px 18px 13px;
    text-align:right;
    border-radius:4px;
    border-top-right-radius:0;
    border-bottom-right-radius:0
}
.ez-promotion {
    background:#fff;
    padDing:13px 18px 10px;
    text-align:left;
    border-top-right-radius:4px;
    border-bottom-right-radius:4px
}
.ez-promotion ul li {
    font-size:18px
}
.ez-promotion ul .ez-promo-title {
    color:#001a6e;
    font-size:20px;
    line-height:24px;
    margin-bottom:6px
}
.ez-promotion a {
    margin-top:-6px
}
#ez-cms-content .ez-content-left {
    width:60%;
    float:left
}
#ez-cms-content .ez-content-halfscreen {
    width:60%
}
#ez-cms-content .ez-content-right {
    width:35%;
    float:right
}
#ez-cms-content h2 {
    text-transform:none
}
#ez-cms-content h3 {
    margin-bottom:12px
}
#ez-cms-content p {
    margin-bottom:20px
}
#ez-cms-content hr {
    border:none;
    border-bottom:1px solid #999;
    margin-bottom:20px
}
#ez-cms-content ul {
    list-style-position:inside
}
#ez-cms-content ul li {
    list-style-type:disc
}
#ez-cms-content ol {
    list-style-position:inside
}
#ez-cms-content ol li {
    list-style-type:decimal
}
#ez-cms-content .ez-sitemap ul {
    list-style-position:outside;
    margin-left:22px
}
#ez-cms-content .ez-sitemap li a {
    line-height:normal;
    padDing-top:9px;
    padDing-bottom:9px;
    display:block
}
#ez-cms-content .ez-sitemap-operator-page li {
    width:50%;
    display:inline-block
}
#ez-cms-content .ez-sitemap-operator-page li a {
    display:inline-block;
    padDing-left:5px
}
#ez-cms-content .ez-keybenefit {
    color:#33c541;
    font-size:24px;
    font-weight:bold
}
.ez-localblogpost .ez-page-title h1 {
    font-size:60px;
    line-height:60px
}
#facebook-area h3 a {
    font-weight:normal;
    font-size:30px;
    text-decoration:none;
    color:#001a6e
}
#ez-follow-area,
#addthis-area,
#facebook-area,
#blog_backlinks_area {
    margin-bottom:20px
}
.ez-localblog img {
    max-width:100%
}
.ez-localblog img.ez-post-mainpic {
    width:100%
}
.ez-localblog .ez-post-date {
    margin:2px 0 4px!important;
    font-weight:bold
}
#ez-localposts-container-small h2 {
    color:#ff3dd9;
    font-weight:normal;
    font-size:32px
}
#ez-localposts-container-small h4.ez-post-title {
    font-size:22px;
    margin-right:0;
    width:58%
}
#ez-localposts-container-small .ez-post-image {
    margin-left:0;
    width:40%
}
#ez-localposts-container li {
    list-style-type:none!important;
    margin-bottom:24px!important;
    position:relative
}
#ez-localposts-container h3 {
    margin-bottom:26px
}
#ez-localposts-container .ez-post-content {
    width:67%;
    float:right;
    padDing:10px 14px;
    background:#f6f6f6
}
#ez-localposts-container .ez-post-content h4 {
    color:#33c541;
    font-weight:bold;
    font-size:20px
}
#ez-localposts-container .ez-post-content p {
    font-size:16px;
    line-height:22px;
    margin-bottom:0!important
}
#ez-localposts-container .ez-post-image {
    width:30%;
    height:144px;
    overflow:hidden;
    display:block;
    float:left
}
#ez-localposts-container .ez-post-image img {
    width:auto;
    height:100%;
    margin-left:-20%
}
.addthis_toolbox .ez-share {
    font-size:14px;
    color:#aaa
}
.addthis_toolbox a span {
    font-size:25px;
    color:#aaa;
    line-height:25px;
    float:none
}
@media(max-width:800px){.ez-localblogpost .ez-page-title h1 {
    font-size:56px;
    line-height:56px
}
}
@media(max-width:600px){.ez-localblogpost .ez-page-title h1 {
    font-size:30px;
    line-height:32px
}
}
.ez-countriescontainer .treedepth-2>li,
.ez-countriescontainer .ez-region-titles {
    list-style-type:none!important;
    float:left;
    width:33%;
    margin-bottom:20px!important
}
.ez-countriescontainer .treedepth-2>li:nth-child(4) {
    clear:both
}
.ez-countriescontainer .treedepth-2>li>a,
.ez-countriescontainer .ez-region-titles-a {
    color:inherit;
    cursor:default;
    font-weight:bold;
    margin-bottom:10px;
    display:block
}
.ez-countriescontainer .treedepth-2>li>a:hover,
.ez-countriescontainer .ez-region-titles-a:hover {
    text-decoration:none!important
}
.ez-highlight-area {
    text-align:center
}
.ez-highlight-area h1 {
    padDing-bottom:10px
}
.ez-highlight-area p {
    padDing-bottom:12px;
    font-size:18px;
    clear:both
}
.ez-highlight-area .ez-button {
    min-width:160px
}
.ez-highlight-area .ez-button:first-child {
    margin-right:16px
}
.ez-highlight-area .ez-logo {
    margin:0 auto;
    margin-bottom:20px;
    width:120px;
    height:50px;
    text-indent:-99999px;
    display:block;
    background:url(../images/logo.svg) 0 0 no-repeat;
    background-size:120px 50px
}
.ez-highlight-area h2 {
    margin-bottom:30px
}
.ez-highlight-area .ez-highlight-btns {
    margin-top:16px
}
.ez-highlight-area .ez-browser-images {
    list-style-type:none;
    width:90%;
    margin:10px auto;
    min-height:150px
}
.ez-highlight-area .ez-browser-images li {
    display:block;
    float:left;
    width:25%;
    text-align:center;
    font-size:20px
}
.ez-highlight-area .ez-browser-images img,
.ez-highlight-area .ez-browser-images span {
    margin:5px auto;
    display:block
}
body.ar .ez-highlight-area .ez-button:first-child,
body.ur .ez-highlight-area .ez-button:first-child {
    margin-right:0;
    margin-left:16px
}
.ez-customercare-panel {
    padDing:20px;
    background:#fff
}
.ez-customercare-panel img {
    width:100%;
    height:auto
}
footer {
    width:100%;
    background:#1c262b;
    position:relative;
    z-index:101;
    padDing:30px 0
}

footer #ez-footer-socials {
    text-align:center;
    position:relative;
    margin-bottom:20px
}
footer #ez-footer-socials div {
    display:inline-block;
    padDing:0 20px;
    position:relative;
    z-index:2;
    background:#1c262b
}
footer #ez-footer-socials:before {
    width:100%;
    height:1px;
    content:'';
    background:rgba(255,
    255,
    255,
    .1);
    position:absolute;
    top:21px;
    left:0;
    z-index:1
}
footer #ez-footer-socials a {
    width:44px;
    text-align:center;
    margin-left:10px
}
footer #ez-footer-socials a:first-child {
    margin-left:0
}
footer #ez-footer-socials a:hover span {
    background:#7dc6ec
}
footer #ez-footer-socials a:focus span {
    background:#53a5cf
}
footer #ez-footer-socials a span {
    width:36px;
    height:36px;
    line-height:36px;
    font-size:20px;
    color:#eaebeb;
    border-radius:50%
}
footer .ez-footer-accordion {
    position:relative;
    z-index:3
}
footer .ez-footer-accordion li {
    padDing-right:10px
}
footer ul {
    width:16.66%;
    float:left
}
footer li {
    font-size:14px;
    font-weight:300;
    position:relative
}
footer li a {
    line-height:40px;
    display:block;
    color:#fff
}
footer li.ez-menu-header {
    font-size:16px;
    font-weight:normal;
    color:#8a9597
}
footer li.ez-menu-header span {
    line-height:44px;
    font-size:26px;
    position:absolute;
    top:0;
    right:30px;
    display:none
}
footer .language-container li {
    padDing-right:0
}
footer .ez-clear-footer-menu {
    display:none
}
footer #footer-terms {
    width:100%;
    float:none;
    margin-top:-16px
}
footer #footer-terms li {
    color:#8b8b8b;
    float:left
}
footer #footer-terms li:first-child {
    padDing-left:0
}
footer #footer-terms .separator {
    padDing:0 10px;
    line-height:40px
}
footer #footer-terms a {
    color:inherit
}
body.ar footer li.ez-menu-header span,
body.ur footer li.ez-menu-header span {
    left:30px;
    right:initial
}
#ez-snippet-footersecuritylogos {
    margin-top:18px;
    padDing-top:26px;
    border-top:1px solid rgba(255,
    255,
    255,
    .1)
}
#ez-footer-cards {
    width:auto
}
#ez-footer-cards li {
    height:25px;
    margin-left:10px;
    display:inline-block
}
#ez-footer-cards li:first-child {
    margin-left:0
}
#ez-footer-cards li.ez-paypal {
    width:46px;
    background:url(../images/paypal_grey.png) 0 0 no-repeat;
    background-size:46px 25px
}
#ez-footer-cards li.ez-mcard {
    width:34px;
    background:url(../images/MC_grey.png) 0 0 no-repeat;
    background-size:34px 25px
}
#ez-footer-cards li.ez-visa {
    width:46px;
    background:url(../images/visa_grey.png) 0 0 no-repeat;
    background-size:46px 25px
}
#ez-footer-cards li.ez-diners {
    width:29px;
    background:url(../images/diners_grey.png) 0 0 no-repeat;
    background-size:29px 25px
}
#ez-footer-cards li.ez-discover {
    width:51px;
    background:url(../images/discover_grey.png) 0 0 no-repeat;
    background-size:51px 25px
}
#ez-footer-cards li.ez-cirrus {
    width:21px;
    background:url(../images/AMEX_grey.png) 0 0 no-repeat;
    background-size:21px 25px
}
#ez-footer-cards li.ez-payments,
#ez-footer-cards li.ez-maestro {
    display:none
}
#ez-footer-cards li a {
    display:block
}
#ez-footer-security {
    width:auto;
    float:right;
    text-align:right
}
#ez-footer-security li {
    color:#fff;
    font:300 16px Roboto;
    display:inline-block
}
#ez-footer-security li:first-child {
    vertical-align:top;
    margin-right:6px
}
#ez-footer-security li.ez-incapsula {
    width:36px;
    height:45px;
    margin-right:24px;
    background:url(../images/sprite.png) -170px -92px no-repeat
}
#ez-footer-security li.ez-trustwave {
    width:88px;
    height:44px;
    background:url(../images/sprite.png) -78px -93px no-repeat
}
#ez-footer-security li.ez-trusted {
    display:block
}
#ez-footer-security li a {
    display:block
}
#ez-footer-security li img {
    width:90px;
    height:auto;
    margin:-1px;
    opacity:0;
    filter:alpha(opacity=0)
}
body.ar #ez-footer-security li.ez-incapsula,
body.ur #ez-footer-security li.ez-incapsula {
    margin-right:4px;
    margin-left:20px
}
#intercom-launcher {
    right:10px!important;
    top:140px
}
#ez-cookies {
    width:314px;
    color:#fff;
    position:fixed;
    right:0;
    bottom:10px;
    z-index:1000;
    padDing:10px 0 10px 10px;
    border:2px solid #0072C6;
    background:rgba(0,
    26,
    110,
    .8)
}
#ez-cookies p {
    margin-right:50px;
    text-align:justify;
    font-size:14px;
    float:left;
    margin-bottom:0!important
}
#ez-cookies p a {
    color:#fff
}
#ez-cookie-button {
    width:44px;
    float:right;
    position:absolute;
    top:2px;
    right:0;
    bottom:0;
    padDing-top:5px;
    background:#0072C6
}
.ez-accordion {
    background-color:#fff;
    position:relative
}
.ez-accordion>p {
    display:none
}
.ez-accordion .ez-accordionitem {
    background:#fff;
    margin-top:5px;
    position:relative;
    cursor:pointer
}
.ez-accordion .ez-accordionitem .fa {
    position:absolute;
    right:20px;
    top:50%;
    font-size:18px;
    color:#001a6e;
    -webkit-transform:translate(0,
    -50%);
    -ms-transform:translate(0,
    -50%);
    transform:translate(0,
    -50%)
}
.ez-accordion .ez-accordiontitle {
    padDing:18px 44px 18px 20px;
    font-size:20px;
    line-height:26px;
    position:relative;
    margin-bottom:0!important
}
.ez-accordion .ez-accordiontitle.ez-active {
    color:#ff3dd9!important
}
.ez-accordion .ez-accordiontitle.ez-active .fa {
    color:#ff3dd9
}
.ez-accordion .ez-accordiontext {
    background:#f6f6f6;
    cursor:default;
    padDing:20px
}
.ez-speeddial-bullet {
    width:30px;
    height:30px;
    background:#001a6e;
    display:inline-block;
    line-height:30px;
    text-align:center;
    color:#fff;
    font-weight:normal;
    margin-right:6px;
    font-family:Roboto,
    sans-serif!important;
    border-radius:50%
}
#ez-ild-speeddials-add .ez-speeddial-bullet,
#ez-ild-speeddials-update .ez-speeddial-bullet {
    width:38px;
    height:38px;
    line-height:38px;
    font-size:29px;
    float:left;
    margin:2px 10px 0 0
}
body.ar #ez-ild-speeddials-add .ez-speeddial-bullet,
body.ar #ez-ild-speeddials-update .ez-speeddial-bullet,
body.ur #ez-ild-speeddials-add .ez-speeddial-bullet,
body.ur #ez-ild-speeddials-update .ez-speeddial-bullet {
    float:right;
    margin-right:0;
    margin-left:10px
}
.ez-speeddial-assign {
    font-size:18px!important;
    color:#aaa;
    text-align:center!important
}
.ez-speeddial-assign.ez-table-row-compact {
    text-align:left!important
}
#ez-call-history-wrap .ez-headline {
    float:left
}
#ez-call-history-wrap .ez-ild-balance {
    float:right;
    margin:-4px 0 0 0
}
.ez-green-fg {
    color:#33c541
}
.ez-blue-fg {
    color:#001a6e
}
.ez-second-color {
    color:#001a6e
}
.ez-orange-fg {
    color:#ec7a55
}
.ez-red-fg {
    color:#ac0000
}
#ez-send-topup {
    height:500px
}
#ez-send-topup .ez-page-title {
    margin-bottom:0
}
#ez-send-topup .ez-spacer {
    height:50px
}
#ez-send-topup .ez-airtime-wrap {
    width:700px;
    height:64px
}
#ez-send-topup .ez-airtime-wrap ul {
    width:66%;
    float:left;
    margin-right:2%
}
#ez-send-topup .ez-airtime-wrap ul input {
    font-size:26px;
    height:62px
}
#ez-send-topup .ez-airtime-wrap .ez-airtime-btn .ez-phone-prefix {
    font-size:26px
}
#ez-send-topup .ez-button {
    width:32%;
    margin-top:0!important;
    float:left
}
#ez-send-topup .ez-asterisk.ez-svg {
    width:940px;
    position:absolute;
    bottom:0;
    right:0;
    z-index:1;
    margin:0 -240px -460px
}
#ez-send-topup .ez-asterisk.ez-svg path {
    fill:#f6f6f6
}
body.ar #ez-send-topup .ez-airtime-wrap ul,
body.ur #ez-send-topup .ez-airtime-wrap ul {
    float:right;
    margin:0 0 0 2%
}
.ez-mobile-menu {
    text-align:center;
    display:none;
    padDing-top:10px;
    background:#eeedef
}
.ez-mobile-menu li {
    width:120px;
    display:inline-block;
    padDing:5px 0 4px 0;
    font-size:14px;
    margin-left:-6px;
    vertical-align:top
}
.ez-mobile-menu li div {
    padDing:7px 6px;
    cursor:pointer;
    line-height:18px;
    border:1px solid #001a6e;
    background:#f6f6f6
}
.ez-mobile-menu li:first-child {
    margin-left:0
}
.ez-mobile-menu li:first-child div {
    border-top-left-radius:4px;
    border-bottom-left-radius:4px
}
.ez-mobile-menu li.ez-last div {
    border-top-right-radius:4px;
    border-bottom-right-radius:4px
}
.ez-mobile-menu li.ez-active div {
    color:#fff;
    background:#0072C6
}
body.ar .ez-mobile-menu li,
body.ur .ez-mobile-menu li {
    margin-left:0;
    margin-right:-6px
}
body.ar .ez-mobile-menu li:first-child,
body.ur .ez-mobile-menu li:first-child {
    margin-right:0
}
body.ar .ez-mobile-menu li:first-child div,
body.ur .ez-mobile-menu li:first-child div {
    border-top-right-radius:4px;
    border-bottom-right-radius:4px
}
body.ar .ez-mobile-menu li.ez-last div,
body.ur .ez-mobile-menu li.ez-last div {
    border-top-left-radius:4px;
    border-bottom-left-radius:4px
}
body.ar .ez-mobile-menu li div,
body.ur .ez-mobile-menu li div {
    border-radius:0
}
#ez-ild-rates-page ul li {
    list-style-type:none
}
#ez-ild-rates-page #ez-ild-country-selectors {
    width:33%;
    float:left
}
#ez-ild-rates-page #ez-regional-rates-wrap {
    margin-top:30px
}
#ez-ild-rates-page #ez-ild-rates-panel {
    width:62%;
    float:right
}
#ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap {
    float:left
}
#ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap ul {
    padDing-top:25px
}
#ez-ild-rates-page #ez-ild-rates-panel .ez-ild-rates-row {
    float:left;
    padDing-top:0;
    margin-left:26px;
    width:auto
}
#ez-ild-rates-page #ez-ild-rates-panel .ez-rates-icon {
    width:120px;
    height:120px;
    float:left;
    text-align:center;
    margin:-26 18px 10px 0;
    background:#33c541;
    -webkit-border-radius:60px;
    -moz-border-radius:60px;
    border-radius:60px
}
#ez-ild-rates-page #ez-ild-rates-panel .ez-rates-icon em {
    font-size:70px;
    line-height:120px;
    color:#fff
}
#ez-ild-rates-page #ez-ild-rates-panel .ez-rates-fixed .ez-rates-icon {
    background:#33c541
}
#ez-ild-rates-page #ez-ild-rates-panel .ez-topup-btns {
    background:none;
    margin-top:20px;
    margin-left:-122px
}
#ez-ild-rates-page #ez-ild-rates-panel span.ez-rateValue {
    font-size:67px;
    font-weight:500
}
#ez-ild-rates-page #ez-ild-rates-panel span.ez-rateCurrency {
    font-weight:600;
    font-size:25px
}
#ez-ild-rates-page #ez-ild-rates-panel span.ez-ratePerMin {
    font-weight:600;
    font-size:25px
}
#ez-ild-rates-page #ez-ild-rates-panel span.ez-rateFixedText {
    display:block;
    font-size:30px;
    font-weight:600
}
#ez-ild-rates-page #ez-ild-rates-panel .ez-topup-btn {
    width:367px;
    border-radius:6px;
    margin-top:8px;
    height:52px;
    line-height:50px;
    font-size:22px
}
#ez-ild-rates-page #ez-ild-rates-panel .ez-rates-btnwrap {
    text-align:center
}
#ez-ild-amount:invalid {
    box-shadow:none
}
#ez-android-page .ez-logo {
    width:160px;
    height:67px;
    /*background:url(../images/logo.svg) 0 0 no-repeat;*/
    background-size:160px 67px
}
#ez-android-page h1 {
    color:#fff;
    margin:4px 0 16px 0;
    font:300 54px/60px"Source Sans Pro",
    sans-serif;
    padDing:0 20px
}
#ez-android-page p {
    color:#fff;
    padDing:0 20px;
    font-size:24px;
    font-weight:300;
    line-height:30px
}
#ez-android-page #ez-snippet-androidpageimage {
    width:100%;
    position:absolute;
    left:0;
    bottom:0
}
#ez-android-page #ez-app-img {
    width:70%;
    max-width:300px;
    height:auto;
    margin-bottom:50px
}
#ez-android-page .ez-footer {
    width:100%;
    padDing:20px 20px 12px 20px;
    background:#fff;
    position:fixed;
    left:0;
    bottom:0;
    z-index:2;
    box-shadow:0,
    -5px,
    6px,
    #fff;
    -webkit-box-shadow:0,
    -5px,
    6px,
    -6px,
    rgba(0,
    0,
    0,
    .5);
    -moz-box-shadow:0,
    -5px,
    6px,
    -6px,
    rgba(0,
    0,
    0,
    .5);
    box-shadow:0 -5px 6px -6px rgba(0,
    0,
    0,
    .5)
}
#ez-android-page .ez-main-cta {
    width:100%;
    max-width:400px;
    height:50px;
    line-height:48px
}
#ez-android-page .ez-website-link {
    font-weight:300;
    color:#8a9597;
    margin-top:12px;
    line-height:normal
}
@media all and (max-height: 760px) {
    #ez-android-page {
        padDing-bottom:0
    }
}
@media all and (max-width: 400px) {
    #ez-android-page {
        min-height:610px
    }
}
@media all and (max-width: 360px) {
    #ez-android-page {
        min-height:570px
    }
}
@media(max-width:330px){#ez-android-page{min-height:534px}}body.ez-mobile-app-page-redirect{background-color:#0064c3!important}body.ez-mobile-app-page-redirect header {
    background-color:#0064c3!important
}
body.ez-mobile-app-page-redirect h2 {
    color:#fff!important;
    font-family:Roboto,
    sans-serif!important;
    font-weight:100
}
body.ez-mobile-app-page-redirect div#ez-message-text {
    text-align:center
}
body.ez-mobile-app-page-redirect div#ez-message-text h2 {
    font-size:24px!important
}
body.ez-mobile-app-page-redirect div#ez-logo-wrap {
    float:none!important;
    margin-left:auto;
    margin-right:auto;
    margin-top:50%!important;
    display:block;
    text-align:center
}

body.ez-mobile-app-page-redirect div#cobrand {
    float:none!important;
    margin-left:auto;
    margin-right:auto;
    margin-top:50%!important;
    display:block;
    text-align:center
}


body.ez-mobile-app-page-redirect a#ez-logo {
    display:inline-block!important;
    width:150px!important;
    height:70px!important;
    background-size:150px 70px!important
}
body.ez-mobile-app-page-redirect #loaDingProgressG {
    width:256px;
    height:2px;
    overflow:hidden;
    background-color:#fff;
    border-radius:2px;
    margin-left:auto;
    margin-right:auto;
    margin-top:30px;
    margin-bottom:25px
}
body.ez-mobile-app-page-redirect .loaDingProgressG {
    background-color:#333;
    margin-top:0;
    margin-left:-256px;
    -moz-animation-name:bounce_loaDingProgressG;
    -moz-animation-duration:1.3s;
    -moz-animation-iteration-count:infinite;
    -moz-animation-timing-function:linear;
    -webkit-animation-name:bounce_loaDingProgressG;
    -webkit-animation-duration:1.3s;
    -webkit-animation-iteration-count:infinite;
    -webkit-animation-timing-function:linear;
    -ms-animation-name:bounce_loaDingProgressG;
    -ms-animation-duration:1.3s;
    -ms-animation-iteration-count:infinite;
    -ms-animation-timing-function:linear;
    -o-animation-name:bounce_loaDingProgressG;
    -o-animation-duration:1.3s;
    -o-animation-iteration-count:infinite;
    -o-animation-timing-function:linear;
    animation-name:bounce_loaDingProgressG;
    animation-duration:1.3s;
    animation-iteration-count:infinite;
    animation-timing-function:linear;
    width:256px;
    height:20px
}
@-moz-keyframes bounce_loaDingProgressG {
    0% {
        margin-left:-256px
    }
    100% {
        margin-left:256px
    }
}
@-webkit-keyframes bounce_loaDingProgressG {
    0% {
        margin-left:-256px
    }
    100% {
        margin-left:256px
    }
}
@-ms-keyframes bounce_loaDingProgressG {
    0% {
        margin-left:-256px
    }
    100% {
        margin-left:256px
    }
}
@-o-keyframes bounce_loaDingProgressG {
    0% {
        margin-left:-256px;
    }
    100% {
        margin-left:256px;
    }
}
@keyframes bounce_loaDingProgressG {
    0% {
        margin-left:-256px
    }
    100% {
        margin-left:256px
    }
}
.promocodeValidation {
    width:100%
}
.promocodeValidation div {
    padDing:5px 15px
}
.promocodeValidation div:nth-child(odd) {
    background-color:#eeedef
}
.promocodeValidation div:nth-child(even) {
    background-color:#fff
}
.promocodeValidation div h3 {
    color:#333;
    font-size:20px;
    font-weight:normal
}
.promocodeValidation div span {
    color:#ac0000;
    font-weight:normal;
    font-size:16px
}
.ez-promocodevalidation-heaDing {
    background-color:#b9c7c4!important;
    height:30px
}
#ez-top-notifier {
    z-index:10000;
    position:fixed;
    top:0;
    left:0;
    width:100%;
    background-color:#fafada;
    font-size:14px;
    height:44px;
    padDing-left:6%
}
#ez-top-notifier #ez-notifier-msgarea {
    line-height:44px
}
#ez-top-notifier .ez-top-notifier-icon {
    margin-right:4px
}
#ez-top-notifier .ez-top-notifier-close {
    position:fixed;
    right:4%;
    top:0
}
#ez-live-chat {
    height:50px;
    color:#fff;
    position:fixed;
    bottom:10px;
    right:0;
    z-index:900;
    padDing:9px 8px 9px 16px;
    background:#ff3dd9;
    -webkit-transition:padDing-right .2s ease;
    transition:padDing-right .2s ease;
    -moz-transition:padDing-right .2s ease;
    -o-transition:padDing-right .2s ease;
    border-top-left-radius:50px;
    border-bottom-left-radius:50px
}
#ez-live-chat:hover {
    padDing-right:24px
}
#ez-live-chat .fa-comments {
    font-size:32px;
    float:left;
    margin-right:10px
}
#ez-live-chat div {
    float:left
}
#ez-live-chat div span {
    display:block;
    font-size:12px;
    line-height:12px
}
#ez-live-chat div span:first-child {
    font-size:17px;
    line-height:17px;
    font-weight:bold
}
#ez-live-chat a {
    position:absolute;
    top:0;
    left:0;
    bottom:0;
    right:0
}
.ez-invite-social {
    text-align:center
}
.ez-invite-social p {
    text-align:left;
    line-height:normal;
    margin-bottom:6px
}
.ez-invite-social input {
    font-size:20px
}
.ez-invite-social a {
    width:23%!important;
    height:48px;
    line-height:48px;
    background:#fff;
    margin:0 0 14px 1%;
    text-align:center;
    border:1px solid #c2c2c2
}
.ez-invite-social a:first-child {
    margin-left:0
}
.ez-invite-social a span {
    font-size:30px;
    line-height:inherit
}
.ez-invite-social .fa-envelope {
    color:#33c541
}
.ez-invite-social .fa-facebook-square {
    color:#3b5998
}
.ez-invite-social .fa-google-plus {
    color:#c02d2a
}
.ez-invite-social .fa-twitter {
    color:#36a7e1
}
.ez-invite-social .fa-whatsapp {
    background-color:green
}
.ez-invite-social.ez-mobile {
    display:none
}
.ez-invite-social.ez-mobile a {
    height:50px;
    display:block;
    color:#4c4c59;
    background:#fff;
    padDing:1px 10px;
    border:none;
    border-bottom:1px solid #c2c2c2
}
.ez-invite-social.ez-mobile a:first-child {
    border-top:1px solid #c2c2c2
}
.ez-invite-social.ez-mobile a:hover {
    text-decoration:none
}
.ez-invite-social.ez-mobile span.fa {
    width:32px;
    line-height:32px;
    text-align:center;
    font-size:18px;
    color:#fff;
    border-radius:50%;
    margin-right:12px
}
.ez-invite-social.ez-mobile .fa-envelope {
    background:#33c541;
    font-size:16px
}
.ez-invite-social.ez-mobile .fa-facebook {
    background:#3b5998
}
.ez-invite-social.ez-mobile .fa-google-plus {
    background:#c02d2a
}
.ez-invite-social.ez-mobile .fa-twitter {
    background:#36a7e1
}
.ez-invite-social .fa-angle-right {
    color:#4c4c59;
    float:right;
    font-size:26px;
    line-height:inherit
}
body.ar .ez-invite-social p,
body.ur .ez-invite-social p {
    text-align:right
}
.ez-light-modal {
    width:507px
}
.ez-light-modal .simplemodal-close {
    right:3px;
    top:4px
}
.ez-light-modal .ez-modal-content {
    min-height:initial;
    font-size:15px
}
.ez-light-modal .ez-modal-footer {
    padDing:6px 0 20px 0;
    background:#fff
}
.ez-light-modal h3 {
    font-size:24px;
    line-height:34px;
    font-weight:300;
    color:#4c4c59;
    margin:16px 0 8px 0
}
.ez-light-modal button {
    width:34%;
    min-width:initial!important;
    height:initial!important;
    display:inline-block!important;
    line-height:50px!important;
    background:none;
    border:2px solid #5cb8e7!important;
    color:#5cb8e7!important;
    font-size:16px;
    font-weight:bold;
    float:none!important;
    border-radius:4px!important
}
.ez-light-modal button:first-child {
    margin-right:12%
}
.ez-light-modal button:hover {
    text-decoration:none
}
@media(max-width:540px){.ez-modal.ez-light-modal{width:100%!important}.ez-modal.ez-light-modal button {
    width:74%!important;
    margin-bottom:10px!important
}
}
#ez-page-home .ez-screen-overlay {
    display:none!important
}
#ez-page-home header,
#ez-page-home #div-header {
    background:none
}
#ez-home {
    position:relative;
    top:-84px
}
#ez-home h2 {
    font-size:44px;
    line-height:44px;
    font-weight:300;
    color:#4c4c59;
    margin-bottom:40px;
    text-align:center;
    padDing:0 30px
}
#ez-home .ez-button {
    margin-top:20px
}
#ez-home .ez-wrap {
    padDing:40px 0 50px 0
}
#ez-home .ez-wrap.ez-arrow-padDing {
    padDing-bottom:70px
}
#ez-home #ez-send-topup-wrap {
    width:100%;
    display:table;
    position:relative;
    z-index:2
}
.ez-send-topup {
     height:auto;
	width:700px;
	float:right;
	overflow:hidden;
	padding: 70px 30px;
    
    vertical-align:middle;
}
#ez-home #ez-send-topup.ez-wrap {
    padDing:0 30px
}
#ez-home #ez-send-topup h1,
#ez-home #ez-send-topup p {
    color:#fff
}
#ez-home #ez-send-topup h1 {
    font:300 76px/82px"Source Sans Pro",
    sans-serif;
    margin-bottom:0
}
#ez-home #ez-send-topup p {
    padDing-top:14px;
    font-size:24px;
    line-height:30px;
    font-weight:300
}
#ez-home #ez-send-topup #ez-topup-controller.ez-opaque {
    opacity:1;
    filter:alpha(opacity=100)
}
#ez-home #ez-send-topup .ez-spacer {
    height:40px
}
#ez-home #ez-send-topup .ez-airtime-wrap {
    margin-left:auto;
    margin-right:auto
}
#ez-home #ez-send-topup .ez-airtime-wrap ul {
    border:1px solid transparent
}
#ez-home #ez-send-topup .ez-airtime-wrap ul.ez-focus {
    border:1px solid #5cb8e7
}
#ez-home #ez-send-topup .ez-airtime-wrap .ez-airtime-btn .ez-nocountry-placeholder {
    left:56px
}
#ez-home #ez-send-topup .ez-airtime-wrap .ez-globe {
    display:block!important;
    margin-left:8px;
    font-size:34px;
    color:#5cb8e7;
    line-height:inherit
}
#ez-home #ez-send-topup .ez-airtime-wrap .fa-globe {
    /*display:none*/
}
#ez-home #ez-send-topup .ez-airtime-wrap .fa-angle-down {
    /*margin-top:0*/
}
#ez-home #ez-send-topup .ez-airtime-wrap input,
#ez-home #ez-send-topup .ez-airtime-wrap .ez-phone-prefix {
    font-weight:300
}
#ez-home #ez-send-topup .ez-airtime-btn .ez-nocountry-placeholder {
    color:#4c4c59;
    font-weight:300;
	font-size: 20px;
}
#ez-home #ez-send-topup .ez-topup-btn {
    background:#5cb8e7;
    font-size:26px;
    font-family:Roboto,
    sans-serif;
    color:#fff
}
#ez-home #ez-send-topup .ez-topup-btn:hover {
    background:#7dc6ec
}
#ez-home #ez-send-topup .ez-topup-btn:focus {
    background:#53a5cf
}
#ez-home #ez-send-topup .ez-topup-btn.ez-mobile-btn {
    width:60%;
    float:none;
    margin:0 auto;
    font-size:24px!important
}
#ez-home #ez-snippet-homepagepromotions {
    position:relative;
    z-index:4
}
body.ar #ez-home #ez-send-topup .ez-airtime-btn .ez-nocountry-placeholder,
body.ur #ez-home #ez-send-topup .ez-airtime-btn .ez-nocountry-placeholder {
    left:initial;
    right:56px
}
body.ar #ez-home #ez-send-topup .ez-airtime-btn .ez-globe,
body.ur #ez-home #ez-send-topup .ez-airtime-btn .ez-globe {
    float:right;
    margin:0 8px 0 0
}
body.ta #ez-home h2,
body.ru #ez-home h2 {
    font-size:38px
}
body.ta #ez-home #ez-send-topup h1,
body.ru #ez-home #ez-send-topup h1,
body.de #ez-home #ez-send-topup h1,
body.it #ez-home #ez-send-topup h1 {
    font-size:56px;
    line-height:62px
}
body.ta #ez-home #ez-send-topup .ez-airtime-wrap .ez-nocountry-placeholder,
body.ru #ez-home #ez-send-topup .ez-airtime-wrap .ez-nocountry-placeholder,
body.de #ez-home #ez-send-topup .ez-airtime-wrap .ez-nocountry-placeholder,
body.it #ez-home #ez-send-topup .ez-airtime-wrap .ez-nocountry-placeholder {
    font-size:22px
}
body.ta #ez-home #ez-send-topup .ez-topup-btn,
body.ru #ez-home #ez-send-topup .ez-topup-btn,
body.de #ez-home #ez-send-topup .ez-topup-btn,
body.it #ez-home #ez-send-topup .ez-topup-btn {
    font-size:22px
}
body.ta #ez-home #ez-send-topup .ez-topup-btn {
    font-size:20px
}
body.fr #ez-home #ez-send-topup .ez-topup-btn {
    font-size:20px
}
#ez-main-img-wrap {
    width:100%;
    height:580px;
    background:#fff;
    position:absolute;
    top:0;
    bottom:0
}
#ez-main-img-wrap div {
    width:100%;
    height:100%
}
#ez-main-img-wrap .ez-main-img {
    background-size:100%;
    background-position:top left;
    -webkit-background-size:cover;
    -moz-background-size:cover;
    -o-background-size:cover;
    background-size:cover
}
#ez-home-product-panel {
    position:relative;
    background:#eeedef;
    padDing-bottom:70px
}
#ez-home-product-panel .ez-arrow.ez-767-hide {
    width:100%!important;
    height:6px;
    position:absolute;
    top:0;
    z-index:100;
    background:#eeedef
}
#ez-home-product-panel .ez-arrow.ez-767-hide:before,
#ez-home-product-panel .ez-arrow.ez-767-hide:after {
    width:50%;
    content:'';
    position:absolute;
    bottom:100%;
    box-sizing:border-box
}
#ez-home-product-panel .ez-arrow.ez-767-hide:before {
    right:50%;
    border-bottom:20px solid #eeedef;
    border-right:20px solid transparent
}
#ez-home-product-panel .ez-arrow.ez-767-hide:after {
    left:50%;
    border-bottom:20px solid #eeedef;
    border-left:20px solid transparent
}
#ez-home-product-panel .ez-arrow.ez-767-show {
    width:100%!important;
    height:6px;
    position:absolute;
    top:0;
    z-index:100;
    background:#fff
}
#ez-home-product-panel .ez-arrow.ez-767-show:before,
#ez-home-product-panel .ez-arrow.ez-767-show:after {
    width:50%;
    content:'';
    position:absolute;
    bottom:100%;
    box-sizing:border-box
}
#ez-home-product-panel .ez-arrow.ez-767-show:before {
    right:50%;
    border-bottom:20px solid #fff;
    border-right:20px solid transparent
}
#ez-home-product-panel .ez-arrow.ez-767-show:after {
    left:50%;
    border-bottom:20px solid #fff;
    border-left:20px solid transparent
}
#ez-home-product-panel .ez-home-product {
    width:38%;
    min-height:200px;
    padDing:40px 20px;
    margin-right:8%;
    background:#fff;
    border:1px solid #e7e7e7;
    float:right;
    text-align:center;
    vertical-align:top;
    border-radius:4px
}
#ez-home-product-panel .ez-home-product:first-child {
    float:left;
    margin:0 0 0 8%
}
#ez-home-product-panel h2 {
    font-size:48px;
    line-height:54px
}
#ez-home-product-panel h3 {
    font:bold 22px/28px Roboto,
    sans-serif;
    color:#4c4c59;
    margin-bottom:40px;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis
}
#ez-home-product-panel li {
    display:inline-block;
    vertical-align:middle
}
#ez-home-product-panel p {
    width:560px;
    height:120px;
    display:table-cell;
    vertical-align:middle;
    font-weight:300;
    font-size:20px;
    line-height:26px
}
#ez-home-product-panel .ez-phone,
#ez-home-product-panel .ez-globe {
    font-size:40px;
    line-height:44px
}
#ez-home-product-panel .ez-dots {
    width:33px;
    border-bottom:3px dotted #c2c2c2
}
#ez-home-product-panel .ez-dots.ez-mleft {
    margin-left:-6px
}
#ez-home-product-panel .ez-dots.ez-mright {
    margin-right:-6px
}
#ez-home-product-panel .ez-map {
    width:70px;
    height:44px;
    margin-left:3px;
    opacity:.92;
    filter:alpha(opacity=92)
}
#ez-home-product-panel .ez-map img {
    width:100%;
    height:auto
}
#ez-home-product-panel .ez-separator {
    width:35%;
    height:1px;
    margin:0 auto;
    background:#c2c2c2
}
#ez-home-product-panel .ez-button {
    min-width:65%;
    font:bold 20px Roboto,
    sans-serif;
    color:#5cb8e7;
    border:2px solid #5cb8e7;
    background:#fff;
    line-height:44px;
    margin-top:0
}
#ez-home-product-panel .ez-button:hover {
    color:#fff;
    background:#5cb8e7
}
#ez-home-product-panel .ez-button:focus {
    color:#fff;
    background:#53a5cf
}
body.ru #ez-home-product-panel p br {
    display:none
}
#ez-home-trust-panel {
    text-align:center;
    position:relative
}
#ez-home-trust-panel .ez-arrow.ez-767-hide {
    width:100%!important;
    height:6px;
    position:absolute;
    top:0;
    z-index:100;
    background:#fff
}
#ez-home-trust-panel .ez-arrow.ez-767-hide:before,
#ez-home-trust-panel .ez-arrow.ez-767-hide:after {
    width:50%;
    content:'';
    position:absolute;
    bottom:100%;
    box-sizing:border-box
}
#ez-home-trust-panel .ez-arrow.ez-767-hide:before {
    right:50%;
    border-bottom:20px solid #fff;
    border-right:20px solid transparent
}
#ez-home-trust-panel .ez-arrow.ez-767-hide:after {
    left:50%;
    border-bottom:20px solid #fff;
    border-left:20px solid transparent
}
#ez-home-trust-panel .ez-arrow.ez-767-show {
    width:100%!important;
    height:6px;
    position:absolute;
    top:0;
    z-index:100;
    background:#eeedef
}
#ez-home-trust-panel .ez-arrow.ez-767-show:before,
#ez-home-trust-panel .ez-arrow.ez-767-show:after {
    width:50%;
    content:'';
    position:absolute;
    bottom:100%;
    box-sizing:border-box
}
#ez-home-trust-panel .ez-arrow.ez-767-show:before {
    right:50%;
    border-bottom:20px solid #eeedef;
    border-right:20px solid transparent
}
#ez-home-trust-panel .ez-arrow.ez-767-show:after {
    left:50%;
    border-bottom:20px solid #eeedef;
    border-left:20px solid transparent
}
#ez-home-trust-panel ul {
    width:90%;
    margin-left:5%;
    display:table;
    text-align:center
}
#ez-home-trust-panel li {
    display:table-cell
}
#ez-home-trust-panel li div {
    display:inline-block;
    font-size:54px;
    color:#5cb8e7;
    margin-right:10px;
    line-height:1em
}
#ez-home-trust-panel li .ez-steps-list {
    width:54px;
    height:52px;
    background:url(content/Uploads/Homepage/three-simple-steps.svg)
}
#ez-home-trust-panel li p {
    display:inline-block;
    font-weight:300;
    font-size:34px;
    line-height:20px;
    color:#525151;
    text-align:left;
    margin-top:2px
}
#ez-home-trust-panel li p span {
    display:block;
    font-size:20px;
    margin-top:9px
}
body.ar #ez-home-trust-panel li p,
body.ur #ez-home-trust-panel li p {
    text-align:right
}
#ez-home-support-wrap {
    position:relative
}
#ez-home-support-wrap .ez-arrow.ez-767-show {
    width:100%!important;
    height:6px;
    position:absolute;
    top:0;
    z-index:100;
    background:#fff
}
#ez-home-support-wrap .ez-arrow.ez-767-show:before,
#ez-home-support-wrap .ez-arrow.ez-767-show:after {
    width:50%;
    content:'';
    position:absolute;
    bottom:100%;
    box-sizing:border-box
}
#ez-home-support-wrap .ez-arrow.ez-767-show:before {
    right:50%;
    border-bottom:20px solid #fff;
    border-right:20px solid transparent
}
#ez-home-support-wrap .ez-arrow.ez-767-show:after {
    left:50%;
    border-bottom:20px solid #fff;
    border-left:20px solid transparent
}
#ez-home-support-wrap img {
    width:100%;
    height:auto;
    position:relative;
    z-index:2
}
#ez-home-support-wrap ul {
    width:100%;
    text-align:center;
    position:absolute;
    top:50%;
    z-index:3;
    padDing:0 30px;
    -webkit-transform:translateY(-50%);
    -ms-transform:translateY(-50%);
    transform:translateY(-50%)
}
#ez-home-support-wrap li {
    width:100%
}
#ez-home-support-wrap li i {
    color: #fff;
    font-size: 100px;
}
#ez-home-support-wrap li.ez-support-icon svg {
    width:100px;
    height:78px
}
#ez-home-support-wrap li.ez-support-icon svg path {
    fill:#fff
}
#ez-home-support-wrap li h4,
#ez-home-support-wrap li span {
    color:#fff;
    padDing-bottom:10px;
    font-weight:300;
    font-size:44px;
    line-height:50px
}
#ez-home-support-wrap li span {
    font-size:30px;
    line-height:30px
}
#ez-home-operators {
    background:#fff;
    position:relative;
    text-align:center;
    margin-top:-6px
}
#ez-home-operators .ez-arrow {
    width:100%!important;
    height:6px;
    position:absolute;
    top:0;
    z-index:100;
    background:#fff
}
#ez-home-operators .ez-arrow:before,
#ez-home-operators .ez-arrow:after {
    width:50%;
    content:'';
    position:absolute;
    bottom:100%;
    box-sizing:border-box
}
#ez-home-operators .ez-arrow:before {
    right:50%;
    border-bottom:20px solid #fff;
    border-right:20px solid transparent
}
#ez-home-operators .ez-arrow:after {
    left:50%;
    border-bottom:20px solid #fff;
    border-left:20px solid transparent
}
#ez-home-operators .ez-logo {
    height:69px;
    display:inline-block;
    vertical-align:middle;
    margin-left:40px;
    background-position:0 -71px
}
#ez-home-operators .ez-logo:first-child {
    margin-left:0
}
#ez-home-operators .ez-logo:hover {
    background-position:0 0
}
#ez-home-operators .ez-claro {
    width:68px;
    background-size:68px 140px
}
#ez-home-operators .ez-cubacel {
    width:140px;
    background-size:140px 140px
}
#ez-home-operators .ez-roshan {
    width:114px;
    background-size:114px 140px
}
#ez-home-operators .ez-bmobile {
    width:139px;
    background-size:139px 140px
}
#ez-home-operators .ez-lime {
    width:31px;
    background-size:31px 140px
}
#ez-home-operators .ez-movistar {
    width:95px;
    background-size:95px 140px
}
#ez-home-operators .ez-orange {
    width:71px;
    background-size:71px 140px
}
#ez-home-operators .ez-digicel {
    width:131px;
    background-size:131px 140px
}
body.es #ez-home-product-panel h3 {
    font-size:25px;
    line-height:25px
}
@media all and (max-width: 1250px) {
    #ez-home #ez-home-operators .ez-logo {
        margin-left:15px
    }
}
@media all and (max-width: 1100px) {
    #ez-main-img-wrap .ez-main-img {
        background-size:121%
    }
    #ez-home #ez-send-topup h1 {
        font-size:64px;
        line-height:70px
    }
    #ez-home-product-panel .ez-home-product {
        width:43%;
        margin-right:4%
    }
    #ez-home-product-panel .ez-home-product:first-child {
        margin-left:4%
    }
    #ez-home-product-panel p br {
        display:none
    }
    #ez-home-trust-panel ul {
        width:100%;
        margin-left:0
    }
}
@media all and (max-width: 960px) {
    #ez-home {
        top:-74px
    }
    #ez-home h2 {
        font-size:34px;
        line-height:40px
    }
    #ez-home #ez-send-topup h1 {
        font-size:58px;
        line-height:64px
    }
    body.ta #ez-home h2,
    body.ru #ez-home h2 {
        font-size:30px;
        line-height:36px
    }
    body.ta #ez-home #ez-send-topup h1,
    body.ru #ez-home #ez-send-topup h1,
    body.de #ez-home #ez-send-topup h1,
    body.it #ez-home #ez-send-topup h1 {
        font-size:50px;
        line-height:56px
    }
    #ez-main-img-wrap .ez-main-img {
        background-size:143%
    }
    #ez-home-product-panel h2 {
        font-size:40px;
        line-height:40px
    }
    #ez-home-product-panel h3 {
        font-size:20px;
        line-height:26px
    }
    #ez-home-product-panel p {
        font-size:18px;
        line-height:24px
    }
    #ez-home-product-panel .ez-button {
        min-width:100%;
        font-size:16px
    }
    body.ta #ez-home-product-panel p {
        font-size:16px;
        line-height:24px
    }
    body.ta #ez-home-product-panel .ez-button {
        font-size:15px
    }
    #ez-home-trust-panel li p {
        font-size:28px
    }
    #ez-home-trust-panel li p span {
        font-size:18px
    }
    #ez-home-support-wrap ul {
        top:47%
    }
    #ez-home-support-wrap li.ez-support-icon svg {
        width:80px;
        height:63px
    }
    #ez-home-support-wrap li h4 {
        font-size:36px;
        line-height:42px
    }
    #ez-home-support-wrap li span {
        font-size:28px;
        line-height:28px
    }
    body.ta #ez-home-support-wrap li h4,
    body.ru #ez-home-support-wrap li h4 {
        font-size:32px;
        line-height:38px
    }
    #ez-home-operators .ez-logo {
        height:49px;
        background-position:0 -51px
    }
    #ez-home-operators .ez-claro {
        width:48px;
        background-size:48px 100px
    }
    #ez-home-operators .ez-cubacel {
        width:101px;
        background-size:101px 100px
    }
    #ez-home-operators .ez-roshan {
        width:81px;
        background-size:81px 100px
    }
    #ez-home-operators .ez-bmobile {
        width:99px;
        background-size:99px 100px
    }
    #ez-home-operators .ez-lime {
        width:22px;
        background-size:22px 100px
    }
    #ez-home-operators .ez-movistar {
        width:68px;
        background-size:68px 100px
    }
    #ez-home-operators .ez-orange {
        width:51px;
        background-size:51px 100px
    }
    #ez-home-operators .ez-digicel {
        width:93px;
        background-size:93px 100px
    }
    body.es #ez-home-product-panel h3 {
        font-size:20px;
        line-height:20px
    }
}
@media all and (max-width: 860px) {
    #ez-main-img-wrap {
        height:500px
    }
    #ez-home #ez-send-topup {
        height:500px
    }
    #ez-home #ez-send-topup h1 {
        font-size:64px;
        line-height:70px
    }
    #ez-home #ez-send-topup p {
        display:none
    }
}
@media all and (max-width: 767px) {
    #ez-main-img-wrap {
        height:430px
    }
    #ez-main-img-wrap .ez-main-img {
        background-size:166%
    }
    #ez-home #ez-send-topup {
        height:430px
    }
    #ez-home #ez-send-topup h1 {
        font-size:58px;
        line-height:64px
    }
    #ez-home #ez-send-topup .ez-airtime-wrap {
        padDing:0 20px
    }
    #ez-home #ez-send-topup .ez-topup-btn {
        font-size:20px
    }
    body.ru #ez-home #ez-send-topup .ez-topup-btn,
    body.de #ez-home #ez-send-topup .ez-topup-btn,
    body.it #ez-home #ez-send-topup .ez-topup-btn {
        font-size:18px
    }
    body.fr #ez-home #ez-send-topup .ez-topup-btn {
        font-size:16px
    }
    body.ta #ez-home #ez-send-topup .ez-airtime-wrap .ez-nocountry-placeholder {
        font-size:18px
    }
    body.ta #ez-home #ez-send-topup .ez-topup-btn {
        font-size:15px
    }
    #ez-home-product-panel {
        background:#fff
    }
    #ez-home-product-panel .ez-home-product {
        width:76%;
        margin:0 auto!important;
        float:none!important;
        display:block;
        border:none;
        padDing:35px 10px;
        border-top:1px solid #ddd
    }
    #ez-home-product-panel .ez-button {
        min-width:70%;
        font-size:19px
    }
    #ez-home-trust-panel {
        background:#eeedef
    }
    #ez-home-trust-panel li div {
        font-size:46px;
        margin-right:6px
    }
    #ez-home-trust-panel li .ez-steps-list {
        width:47px;
        height:46px;
        background-size:47px 46px
    }
    #ez-home-trust-panel li p {
        font-size:24px;
        line-height:18px
    }
    #ez-home-trust-panel li p span {
        font-size:14px
    }
    #ez-home-support-wrap {
        height:200px
    }
    #ez-home-support-wrap img {
        display:none
    }
    #ez-home-support-wrap ul {
        top:45%
    }
    #ez-home-support-wrap li.ez-support-icon svg path {
        fill:#bfc7cf
    }
    #ez-home-support-wrap li h4 {
        color:#4c4c59;
        font-size:34px;
        line-height:34px;
        padDing-bottom:0
    }
    #ez-home-support-wrap li span {
        color:#4c4c59;
        font-size:26px;
        line-height:26px
    }
    body.ta #ez-home-support-wrap li h4,
    body.ru #ez-home-support-wrap li h4 {
        font-size:28px;
        line-height:34px;
        padDing-bottom:4px
    }
    body.ta #ez-home-support-wrap li span,
    body.ru #ez-home-support-wrap li span {
        font-size:20px
    }
    #ez-home-operators,
    #ez-home-support-img {
        display:none
    }
}
@media all and (max-width: 660px) {
    #ez-home #ez-send-topup .ez-airtime-wrap {
        padDing:0
    }
    #ez-main-img-wrap .ez-main-img {
        background-size:183%
    }
    #ez-home-trust-panel {
        text-align:center
    }
    #ez-home-trust-panel ul {
        padDing:0 20px
    }
    #ez-home-trust-panel li {
        width:100%;
        display:block;
        margin-top:24px
    }
    #ez-home-trust-panel li:first-child {
        margin-top:0
    }
    #ez-home-trust-panel li div {
        display:block;
        margin-bottom:4px;
        font-size:42px
    }
    #ez-home-trust-panel li .ez-steps-list {
        width:42px;
        height:41px;
        background-size:42px 41px;
        margin:0 auto 8px auto
    }
    #ez-home-trust-panel li p {
        display:block;
        font-size:22px;
        text-align:center!important;
        line-height:26px
    }
    #ez-home-trust-panel li p span {
        display:inline;
        font-size:inherit
    }
}
@media all and (max-width: 600px) {
    #ez-home {
        top:-56px
    }
    #ez-home h2 {
        font-size:26px;
        line-height:32px;
        margin-bottom:30px
    }
    #ez-home #ez-send-topup h1 {
        font-size:42px;
        line-height:48px
    }
    #ez-home #ez-send-topup #ez-main-img-wrap {
        height:500px
    }
    #ez-home .ez-airtime-wrap ul {
        width:88%!important;
        float:none!important;
        display:block;
        margin:0 auto 20px auto!important
    }
    #ez-home .ez-topup-btn {
        width:88%!important;
        float:none
    }
    body.ta #ez-home h2,
    body.ru #ez-home h2 {
        font-size:22px;
        line-height:28px
    }
    body.ta #ez-home #ez-send-topup h1,
    body.ru #ez-home #ez-send-topup h1,
    body.de #ez-home #ez-send-topup h1,
    body.it #ez-home #ez-send-topup h1,
    body.fr #ez-home #ez-send-topup h1 {
        font-size:38px;
        line-height:44px
    }
    body.ta #ez-home #ez-send-topup .ez-airtime-wrap .ez-nocountry-placeholder,
    body.ru #ez-home #ez-send-topup .ez-airtime-wrap .ez-nocountry-placeholder,
    body.de #ez-home #ez-send-topup .ez-airtime-wrap .ez-nocountry-placeholder,
    body.it #ez-home #ez-send-topup .ez-airtime-wrap .ez-nocountry-placeholder,
    body.fr #ez-home #ez-send-topup .ez-airtime-wrap .ez-nocountry-placeholder {
        font-size:20px
    }
    body.ta #ez-home #ez-send-topup .ez-topup-btn,
    body.ru #ez-home #ez-send-topup .ez-topup-btn,
    body.de #ez-home #ez-send-topup .ez-topup-btn,
    body.it #ez-home #ez-send-topup .ez-topup-btn,
    body.fr #ez-home #ez-send-topup .ez-topup-btn {
        font-size:20px
    }
    #ez-main-img-wrap .ez-main-img {
        background-size:104%
    }
    #ez-home-product-panel .ez-home-product {
        width:90%
    }
    #ez-home-product-panel .ez-button {
        font-size:19px
    }
    #ez-home-support-wrap ul {
        padDing-top:30px
    }
    #ez-home-support-wrap li h4 {
        font-size:26px;
        line-height:26px;
        margin-bottom:6px
    }
    #ez-home-support-wrap li span {
        font-size:18px
    }
    body.ta #ez-home-support-wrap li h4,
    body.ru #ez-home-support-wrap li h4 {
        font-size:22px;
        line-height:28px
    }
    body.ta #ez-home-support-wrap li span,
    body.ru #ez-home-support-wrap li span {
        font-size:18px
    }
    body.ta #ez-home-support-wrap ul {
        padDing-top:50px
    }
}
@media all and (max-width: 480px) {
    #ez-home #ez-send-topup h1 {
        font-size:40px;
        line-height:46px
    }
    #ez-home #ez-send-topup .ez-spacer {
        height:44px!important
    }
    #ez-home #ez-send-topup .ez-topup-btn.ez-mobile-btn {
        width:80%
    }
    #ez-home #ez-send-topup .ez-topup-btn {
        width:100%!important;
        font-size:24px
    }
    body.ta #ez-home #ez-send-topup h1,
    body.ru #ez-home #ez-send-topup h1,
    body.de #ez-home #ez-send-topup h1,
    body.it #ez-home #ez-send-topup h1 {
        font-size:30px;
        line-height:36px
    }
    #ez-main-img-wrap .ez-main-img {
        background-size:141%
    }
    #ez-home-product-panel .ez-button {
        min-width:100%;
        font-size:17px
    }
    body.ta #ez-home-product-panel .ez-button {
        font-size:13px
    }
    body.fr #ez-home-product-panel .ez-button {
        font-size:15px
    }
    body.pt #ez-home-product-panel .ez-button {
        font-size:16px
    }
    #ez-home-trust-panel .ez-arrow,
    #ez-home-product-panel .ez-arrow {
        height:0!important;
        overflow:hidden
    }
    #ez-home-trust-panel .ez-arrow.ez-show-arrow,
    #ez-home-product-panel .ez-arrow.ez-show-arrow {
        height:6px!important;
        overflow:visible
    }
}
@media all and (max-width: 340px) {
    #ez-home #ez-send-topup {
        height:430px!important;
        display:block;
        position:relative
    }
    #ez-home #ez-send-topup.ez-wrap {
        padDing:0
    }
    #ez-home #ez-send-topup h1 {
        position:absolute;
        left:20px;
        right:20px;
        bottom:120px
    }
    #ez-home #ez-topup-controller {
        margin-left:20px;
        margin-right:20px;
        margin-top:300px
    }
}
.ez-account-page button,
.ez-account-page input[type=submit],
.ez-account-page .ez-button {
    font-size:18px
}
body.ta .ez-account-page button,
body.ta .ez-account-page input[type=submit],
body.ta .ez-account-page .ez-button,
body.ru .ez-account-page button,
body.ru .ez-account-page input[type=submit],
body.ru .ez-account-page .ez-button,
body.de .ez-account-page button,
body.de .ez-account-page input[type=submit],
body.de .ez-account-page .ez-button {
    font-size:15px
}
#ez-account-title {
    width:100%;
    height:81px;
    margin-bottom:-25px;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis
}
#ez-account-title #ez-account-user:before {
    content:' '
}
#ez-overview-bottom {
    width:100%;
    float:right;
    margin-top:10px;
    padDing-bottom:10px
}
#ez-overview-bottom li {
    width:49%;
    height:130px;
    float:left;
    margin-left:2%;
    color:#fff;
    position:relative;
    background:#1a317d;
    border-radius:4px
}
#ez-overview-bottom li:first-child {
    margin-left:0
}
#ez-overview-bottom li span {
    font-size:22px;
    display:block;
    text-align:center;
    margin:20px 0 18px 0
}
#ez-overview-bottom li h2,
#ez-overview-bottom li h3 {
    font-size:64px;
    text-align:center;
    color:#fff
}
#ez-overview-bottom li h3 {
    padDing-top:4px;
    font-weight:800;
    font-size:30px
}
#ez-overview-bottom li h3 div {
    border:none;
    background:transparent
}
#ez-overview-bottom li a {
    display:block;
    position:absolute;
    top:0;
    left:0;
    right:0;
    bottom:0
}
#ez-overview-bottom li.ez-three-in-one {
    width:100%!important;
    margin-left:0;
    height:64px
}
#ez-overview-bottom .ez-asterisk {
    width:170px;
    height:140px;
    margin:0 -40px -60px 0;
    opacity:1;
    filter:alpha(opacity=100)
}
#ez-overview-bottom .ez-asterisk path {
    fill:#001a6e
}
#ez-overview-mobile-menu li div {
    padDing-left:10px;
    padDing-right:10px
}
#ez-account-menu li a {
    line-height:20px;
    padDing-top:5px;
    padDing-bottom:5px
}
#ez-account-menu span.fa-circle {
    color:#ff3dd9;
    font-size:24px;
    position:absolute;
    top:3px;
    margin-left:50px;
    display:none
}
#ez-account-menu span.fa-circle span.ez-amenu-vouchers-count {
    font-family:roboto;
    color:#fff;
    font-size:15px;
    position:relative;
    top:-3.3px;
    left:-14.3px
}
#ez-account-menu span.fa-circle span.ez-two-digit-voucher-count {
    left:-18.3px
}
#ez-account-main-action {
    width:32%;
    height:130px;
    float:left;
    position:relative
}
#ez-account-main-action .ez-icon {
    width:60px;
    height:60px;
    float:left;
    margin:0 8px 0 0;
    color:#fff;
    text-align:center;
    background:#33c541;
    border:none;
    border-radius:4px
}
#ez-account-main-action .ez-icon span {
    line-height:60px;
    font-size:50px
}
#ez-account-main-action h2 {
    margin-top:2px;
    font-size:28px;
    line-height:26px;
    color:#001a6e
}
#ez-account-main-action .ez-btn-wrap {
    position:absolute;
    bottom:0;
    left:0;
    right:0
}
#ez-account-main-action .ez-btn-wrap .ez-topup-btn {
    margin-bottom:0!important
}
#ez-account-main-action .ez-topup-btn {
    width:100%;
    height:54px;
    line-height:50px
}
#ez-account-main-action .ez-topup-btn:first-child {
    margin-bottom:22px
}
#ez-account-main-action h3 {
    font-size:18px;
    font-weight:normal;
    margin-top:7px
}
#ez-account-main-action h3 span {
    float:right
}
#ez-account-main-action p {
    font-size:15px;
    color:#001a6e
}
#ez-account-main-action p span {
    float:right
}
body.ta #ez-account-main-action h2,
body.ru #ez-account-main-action h2 {
    font-size:24px
}
body.ar #ez-account-main-action,
body.ur #ez-account-main-action {
    float:right
}
body.ar #ez-account-main-action .ez-icon,
body.ur #ez-account-main-action .ez-icon {
    float:right;
    margin:0 0 0 8px
}
body.ar #ez-overview-all,
body.ur #ez-overview-all {
    float:left
}
body.ar #ez-overview-all li,
body.ur #ez-overview-all li {
    float:right;
    margin-left:0;
    margin-right:2%
}
body.ar #ez-overview-all li:first-child,
body.ur #ez-overview-all li:first-child {
    margin-right:0
}
body.ar #ez-account-tabs>li,
body.ur #ez-account-tabs>li {
    float:right;
    margin-right:2%;
    margin-left:0!important
}
body.ar #ez-account-tabs>li:first-child,
body.ur #ez-account-tabs>li:first-child {
    margin-right:0
}
body.ar #ez-account-tabs .ez-icon-text,
body.ur #ez-account-tabs .ez-icon-text {
    margin-left:0;
    margin-right:5px
}
body.ar #ez-my-profile #ez-account-tabs p,
body.ar #ez-my-profile #ez-account-tabs h3,
body.ur #ez-my-profile #ez-account-tabs p,
body.ur #ez-my-profile #ez-account-tabs h3 {
    text-align:right
}
body.ar #ez-my-profile #ez-account-tabs .ez-my-profile-img,
body.ur #ez-my-profile #ez-account-tabs .ez-my-profile-img {
    float:right;
    margin-right:0;
    margin-left:20px
}
body.ar #ez-my-profile #FacebookLogin,
body.ur #ez-my-profile #FacebookLogin {
    text-align:right
}
#ez-overview-all {
    width:66%;
    float:right;
    direction:ltr
}
#ez-overview-all li {
    width:32%;
    height:130px;
    float:left;
    margin-left:2%;
    color:#fff;
    position:relative;
    background:#1a317d;
    border-radius:4px
}
#ez-overview-all li:first-child {
    margin-left:0
}
#ez-overview-all li span {
    font-size:18px;
    display:block;
    text-align:center;
    margin:20px 0 18px 0
}
#ez-overview-all li h2,
#ez-overview-all li h3 {
    font-size:38px;
    text-align:center;
    color:#fff
}
#ez-overview-all li h3 {
    font-size:28px
}
#ez-overview-all li a {
    display:block;
    position:absolute;
    top:0;
    left:0;
    right:0;
    bottom:0
}
#ez-account-tabs {
    width:100%;
    margin-top:30px
}
#ez-account-tabs>li {
    width:32%;
    height:370px;
    margin-left:2%;
    background:#eeedef;
    float:left;
    padDing:26px 24px;
    position:relative;
    border-bottom-left-radius:4px;
    border-bottom-right-radius:4px
}
#ez-account-tabs>li:first-child {
    margin-left:0
}
#ez-account-tabs #FacebookLogin {
    text-align:left
}
#ez-account-tabs .ez-icon {
    margin-bottom:10px
}
#ez-account-tabs .ez-icon-text {
    margin-left:5px
}
#ez-account-tabs h3 {
    font-weight:normal;
    text-align:center;
    font-size:18px;
    margin:10px 0 7px 0;
    color:#001a6e
}
#ez-account-tabs p {
    text-align:center
}
#ez-account-tabs .ez-btn-wrap {
    position:absolute;
    bottom:24px;
    left:0;
    right:0;
    text-align:center
}
#ez-account-tabs .ez-button {
    min-width:250px
}
.ez-account-tab-details {
    width:100%;
    padDing-top:8px;
    clear:both
}
.ez-account-tab-details li {
    width:50%;
    float:left;
    line-height:50px;
    font-weight:300;
    font-size:18px
}
.ez-account-tab-details li em {
    font-weight:normal;
    color:#001a6e
}
.ez-account-tab-details li a.ez-link-btn {
    margin-top:-12px;
    display:block
}
.ez-account-tab-details li .ez-button {
    width:100%;
    min-width:100%!important;
    padDing:0;
    line-height:44px
}
.ez-account-tab-details li.ez-full-width {
    width:100%
}
.ez-account-tab-details li.ez-full-width em {
    display:inline-block;
    margin-right:14px
}
.ez-account-tab-details li .ez-flag {
    display:inline-block;
    margin-right:10px
}
.ez-account-tab-details li.ez-two-lines,
.ez-account-tab-details li.ez-one-line {
    width:50%;
    padDing-right:6px;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis
}
.ez-account-tab-details li.ez-two-lines em,
.ez-account-tab-details li.ez-one-line em {
    display:block
}
.ez-account-tab-details li.ez-two-lines {
    line-height:23px
}
.ez-account-tab-details li.ez-two-lines-btn,
.ez-account-tab-details li.ez-one-line-btn {
    width:50%
}
.ez-account-tab-details li.ez-contact-name {
    width:38%;
    padDing-right:6px;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis
}
.ez-account-tab-details li.ez-amount {
    width:34%;
    padDing-left:6px;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis
}
.ez-account-tab-details li.ez-number {
    width:10%
}
.ez-account-tab-details li.ez-contact-name {
    width:50%
}
.ez-account-tab-details li.ez-contact-btn {
    width:50%
}
.ez-account-tab-details li.ez-date {
    font-size:18px;
    font-weight:bold;
    text-align:right;
    color:#001a6e
}
.ez-edit-wrap {
    width:520px
}
.ez-edit-btns-wrap button,
.ez-edit-btns-wrap .ez-button {
    width:48%
}
.ez-edit-btns-wrap .clear {
    height:10px
}
#ez-contacts-wrap>h2 {
    float:left
}
#ez-contacts-wrap .ez-create-btn {
    margin-bottom:10px
}
#ez-my-profile #ez-account-tabs>li {
    width:49%;
    height:240px;
    margin-left:2%
}
#ez-my-profile #ez-account-tabs>li:first-child {
    margin-left:0
}
#ez-my-profile #ez-account-tabs p,
#ez-my-profile #ez-account-tabs h3 {
    text-align:left;
    margin:0
}
#ez-my-profile #ez-account-tabs p.ez-margin-bottom,
#ez-my-profile #ez-account-tabs h3.ez-margin-bottom {
    margin-bottom:6px
}
#ez-my-profile #ez-account-tabs .ez-my-profile-info {
    font-size:28px;
    color:#ff3dd9;
    margin-bottom:20px;
    line-height:34px;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis
}
#ez-my-profile #ez-account-tabs #ez-facebook-link {
    display:block
}
#ez-my-profile #ez-account-tabs .ez-my-profile-img {
    width:160px;
    height:160px;
    float:left;
    margin:6px 20px 0 0;
    background:#b7c1da;
    position:relative;
    overflow:hidden;
    display:block!important;
    padDing:0!important;
    border-radius:50%
}
#ez-my-profile #ez-account-tabs .ez-my-profile-img span {
    color:#fff;
    font-size:124px;
    position:absolute;
    bottom:-12px;
    right:38px
}
#ez-my-profile #ez-account-tabs button {
    min-width:240px
}
.ez-replace-main-box {
    display:none!important;
    margin-left:0%!important
}
.ez-two-in-one {
    display:none!important
}
.ez-two-in-one hr {
    width:90%;
    text-align:center;
    margin:0 auto;
    opacity:.2;
    color:#fff
}
.ez-three-in-one {
    display:none!important;
    width:98%!important
}
.ez-three-in-one hr {
    width:90%;
    text-align:center;
    margin:0 auto;
    opacity:.2;
    color:#fff
}
#existing-user-modal {
    width:600px
}
#existing-user-modal h3 {
    font-size:60px;
    height:60px;
    margin-top:50px;
    font-family:BrauerNeuePro;
    font-weight:800
}
#existing-user-modal h2 {
    text-transform:none;
    font-family:BrauerNeuePro;
    font-weight:normal;
    font-size:30px
}
#existing-user-modal p {
    padDing-bottom:40px;
    font-size:18px
}
#existing-user-modal p span.ez-highlight {
    color:#ff3dd9
}
#existing-user-modal div.ez-modal-content {
    display:none
}
#existing-user-modal div.ez-modal-footer button {
    width:600px;
    background-color:#33c541
}
.ez-auth-pages {
    width:520px;
    margin:0 auto
}
.ez-auth-pages .ez-alert-box {
    width:100%
}
#ez-forgot-pass-intro {
    margin-bottom:24px;
    font-size:18px;
    line-height:22px
}
#ez-forgot-pass-intro span {
    color:#ff3dd9
}
#ez-captcha-image {
    width:100%;
    background:#fff;
    text-align:center;
    margin-bottom:6px;
    padDing:6px 0
}
#ez-register-rules {
    font-size:13px;
    line-height:17px;
    margin-top:14px
}
#ez-register-rules a {
    line-height:inherit
}
#ez-facebook-link {
    font-family:"lucida grande",
    tahoma,
    verdana,
    arial,
    sans-serif
}
#ez-facebook-link button {
    background-color:#3b5998
}
#FacebookLogin {
    text-align:center
}
#FacebookLogin .ez-facebook-reassure {
    font-size:14px;
    line-height:18px;
    margin-bottom:5px;
    color:#8a9597;
    font-family:Roboto,
    sans-serif
}
#ez-raf-register .ez-modal-content {
    max-height:75%!important
}
#ez-raf-register .ez-modal-header {
    text-align:inherit!important
}
#ez-raf-register .ez-modal button {
    border-radius:4px
}
#ez-raf-register-modal.ez-modal-override {
    margin-top:0!important
}
.ez-popout {
    background:#fff!important;
    background:rgba(255,
    255,
    255,
    .94)!important;
    box-shadow:2px 2px 2px #000;
    padDing:20px!important
}
.blur-filter {
    filter:blur(3px);
    -webkit-filter:blur(3px);
    -moz-filter:blur(3px);
    -o-filter:blur(3px);
    -ms-filter:blur(3px)
}
#invalid-referral-modal {
    width:600px
}
#invalid-referral-modal .ez-highlight {
    color:#ff3dd9
}
#invalid-referral-modal .ez-modal .simplemodal-close span {
    background-color:#fff
}
#invalid-referral-modal .ez-modal .ez-modal-header {
    color:#001a6e;
    background-color:#fff
}
#invalid-referral-modal .ez-modal .ez-modal-header h3 {
    font-size:58px;
    height:45px;
    margin-top:20px;
    font-weight:800
}
#invalid-referral-modal .ez-modal .ez-modal-header p {
    font-size:28px;
    height:26px;
    margin-top:10px
}
#invalid-referral-modal .ez-modal .ez-modal-content {
    padDing-bottom:40px
}
#invalid-referral-modal .ez-modal .ez-modal-content h2 {
    text-transform:none;
    font-weight:100;
    font-size:28px;
    margin-bottom:20px
}
#invalid-referral-modal .ez-modal .ez-modal-content p {
    font-family:Roboto
}
#invalid-referral-modal .ez-modal .ez-modal-content p span.fa {
    color:#33c541;
    margin-right:10px
}
#invalid-referral-modal .ez-modal .ez-modal-footer {
    padDing-top:20px;
    height:62px;
    padDing-bottom:20px;
    padDing-left:30px;
    padDing-right:30px;
    background-color:#eeedef
}
#invalid-referral-modal .ez-modal .ez-modal-footer button {
    width:100%;
    background-color:#33c541;
    font-size:28px;
    padDing:6px;
    border-radius:4px
}
#ez-raf-title {
    margin-bottom:26px
}
#ez-raf-title #ez-page-subtitle {
    color:#001a6e;
    font-size:22px;
    line-height:28px
}
#registerRAF #ez-left-side {
    width:600px;
    float:left;
    margin-right:100px
}
#registerRAF #ez-left-side img {
    width:100%;
    height:auto
}
#registerRAF #ez-left-side a {
    line-height:32px;
    margin-top:-8px
}
#registerRAF #ez-left-side a:hover {
    text-decoration:none
}
#registerRAF #ez-left-side a span {
    margin-left:5px;
    position:relative;
    top:2px
}
#registerRAF #ez-right-side {
    width:470px;
    float:left;
    margin-top:40px
}
#registerRAF #ez-right-side .title {
    font-size:30px;
    line-height:30px;
    color:#001a6e;
    margin-bottom:2px
}
#registerRAF #ez-right-side .subtitle {
    font-size:17px;
    margin-bottom:28px
}
#registerRAF .ez-trusted .ez-highlight {
    color:inherit
}
#registerRAF .ez-registration-footer {
    margin:15px 0 -30px
}
#registerRAF .ez-registration-footer .ez-trusted {
    padDing:20px 13%;
    background-color:#8a9597;
    color:#fff;
    font-size:28px;
    line-height:36px;
    font-weight:300;
    margin:0 -13%
}
#registerRAF .ez-registration-footer .ez-programme {
    padDing:30px 13% 30px 13%;
    margin:0 -13%;
    position:relative;
    background:#f6f6f6
}
#registerRAF .ez-registration-footer .ez-programme .ez-programme-title {
    font-size:32px;
    line-height:38px;
    font-weight:300;
    color:#001a6e;
    text-align:center
}
#registerRAF .ez-registration-footer .ez-programme .ez-refer-steps {
    margin-top:30px
}
#registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step,
#registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive {
    width:32%;
    float:left;
    margin-left:2%
}
#registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step .ez-number,
#registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-number {
    font-size:140px;
    font-weight:bold;
    line-height:132px;
    color:#fff;
    float:left;
    -webkit-font-smoothing:antialiased
}
#registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step .ez-title,
#registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-title {
    font-size:20px;
    margin:6px 0 10px 90px
}
#registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step .ez-content,
#registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-content {
    margin-left:90px
}
#registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step .ez-number {
    text-shadow:-1px 0 #001a6e,
    0 1px #001a6e,
    1px 0 #001a6e,
    0 -1px #001a6e
}
#registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step .ez-title {
    color:#001a6e
}
#registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive {
    margin-left:0
}
#registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-number {
    text-shadow:-1px 0 #959395,
    0 1px #959395,
    1px 0 #959395,
    0 -1px #959395
}
#registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-title {
    color:#959395;
    margin-left:70px
}
#registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-content {
    color:#959395;
    margin-left:70px
}
#registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial {
    margin-top:30px
}
#registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-title {
    text-align:center;
    font-size:32px;
    line-height:38px;
    font-weight:300;
    margin-bottom:14px;
    color:#001a6e
}
#registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial>.ez-title {
    margin-bottom:20px
}
#registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-content {
    margin-top:25px;
    padDing:0 10px
}
#registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-content .ez-commercial-box {
    float:left;
    width:25%
}
#registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-content .ez-commercial-box .ez-commercial-box-image {
    text-align:center;
    font-size:36px;
    margin-bottom:4px
}
#registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-content .ez-commercial-box span.fa {
    width:65px;
    height:65px;
    line-height:65px;
    color:#1a317d;
    border:4px solid #1a317d;
    border-radius:50%
}
#registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-content .ez-commercial-box .ez-title {
    font-size:20px;
    line-height:24px;
    font-weight:normal;
    color:#001a6e;
    border-color:#33c541
}
#registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-content .ez-commercial-box .ez-content {
    margin-top:-5px;
    text-align:center;
    font-family:Roboto,
    sans-serif;
    font-size:18px
}
#registerRAF .ez-registration-footer .ez-community {
    margin-top:30px;
    display:none
}
#registerRAF .ez-registration-footer .ez-community .ez-title {
    font-size:32px;
    height:30px
}
#registerRAF .ez-registration-footer .ez-community .ez-title span.ez-highlight {
    color:#ff3dd9
}
#registerRAF .ez-registration-footer .ez-community #facebook-area {
    margin-top:40px;
    float:left
}
#registerRAF .ez-registration-footer .ez-community #facebook-area h3 {
    display:none
}
#registerRAF .ez-registration-footer .ez-community .ez-comments {
    float:left
}
#registerRAF .ez-payment-types {
    width:130%;
    margin:60px 0 0 -15%;
    padDing:40px 0 30px 0;
    text-align:center;
    background-color:#8a9597
}
#registerRAF .ez-payment-types .ez-payment-box:first-child {
    margin-left:0
}
#registerRAF .ez-payment-types .ez-payment-box.ez-mcard {
    background-position:-109px 0
}
#registerRAF .ez-payment-types .ez-payment-box.ez-visa {
    background-position:-218px 0
}
#registerRAF .ez-payment-types .ez-payment-box.ez-diners {
    background-position:0 -54px
}
#registerRAF .ez-payment-types .ez-payment-box.ez-discover {
    background-position:-109px -54px
}
#registerRAF .ez-payment-types .ez-payment-box.ez-cirrus {
    background-position:-218px -54px;
    display:none
}
#registerRAF .ez-payment-types .ez-payment-box.ez-maestro {
    background-position:0 -108px;
    display:none
}
#registerRAF .ez-payment-types .ez-title {
    color:#fff;
    text-align:center;
    font-size:30px;
    line-height:36px;
    font-weight:300
}
#registerRAF .ez-payment-types .ez-subtitle {
    font-family:roboto;
    color:#fff;
    text-align:center;
    font-size:19px;
    margin-right:16%
}
#ez-refer-steps-image {
    margin-top:26px;
    text-align:center
}
#ez-refer-steps-image li {
    display:inline-block;
    color:#33c541;
    vertical-align:middle;
    margin-left:-15px;
    float:none
}
#ez-refer-steps-image li:first-child {
    margin-left:0
}
#ez-refer-steps-image li.ez-line {
    width:32%;
    height:3px;
    background:#33c541
}
#ez-refer-steps-image li.ez-line.ez-inactive {
    background:#e1e0e5
}
#ez-refer-steps-image li.fa-envelope {
    font-size:40px;
    position:relative;
    background:#fff;
    z-index:2
}
#ez-refer-steps-image li.fa-asterisk {
    font-size:46px
}
#ez-refer-steps-image li.fa-gift {
    font-size:50px;
    color:#b9c7c4
}
#activationRAF .ez-snippet {
    text-align:left
}
#activationRAF h4 {
    margin-top:10px
}
#activationRAF span {
    color:inherit
}
#activationRAF span.ez-highlight {
    color:#ff3dd9
}
#activationRAF .ez-button {
    width:320px
}
#activationRAF .ez-tagline {
    text-align:left;
    margin:100px -13% 0 -13%;
    padDing:20px 13%
}
body.ez-promotions-page .ez-main-wrap {
    padDing-left:0;
    padDing-right:0
}
#ez-promo-countries-wrap {
    min-height:210px
}
#ez-promo-countries-wrap .ez-list li.ez-list-item {
    width:16%;
    margin:0 0 28px 1.5%
}
#ez-promo-countries-wrap .ez-list li.ez-list-item.ez-five-items-row {
    margin-left:7%;
    clear:both
}
#ez-promo-countries-wrap .ez-list li.ez-list-item.ez-four-items-row {
    clear:none
}
#ez-no-promo-countries {
    text-align:center
}
#ez-no-promo-countries h2 {
    font-size:50px
}
#ez-no-promo-countries h3 {
    font-size:22px;
    font-family:Roboto,
    sans-serif;
    margin:8px 0 16px 0
}
#ez-no-promo-countries p {
    font-size:20px;
    font-weight:300;
    margin-bottom:0!important
}
#ez-no-promo-countries p.ez-no-margin {
    margin-bottom:0
}
.ez-promos-container {
    width:100%!important;
    clear:both;
    margin-left:0!important;
    position:relative;
    text-align:center;
    background:#8a9597;
    -webkit-box-shadow:inset 0 6px 10px -3px rgba(0,
    0,
    0,
    .3);
    -moz-box-shadow:inset 0 6px 10px -3px rgba(0,
    0,
    0,
    .3);
    box-shadow:inset 0 6px 10px -3px rgba(0,
    0,
    0,
    .3)
}
.ez-promos-container .ez-tap-close {
    position:absolute;
    top:5px;
    right:3px;
    color:#fff
}
#ez-promos {
    width:86%;
    max-width:900px;
    margin:48px 0 38px 0;
    display:inline-block
}
#ez-promos .owl-item {
    padDing:0 50px 0 10px;
    margin-left:-40px
}
#ez-promos .owl-item:first-child,
#ez-promos .owl-item.ez-carousel-item-nomargin {
    margin-left:0
}
#ez-promos .ez-promos-carousel-item {
    list-style-type:none!important;
    margin-bottom:0!important;
    margin-top:28px
}
#ez-promos .ez-promos-carousel-item:first-child {
    margin-top:0
}
#ez-promos img {
    float:left;
    margin:10px 14px 0 0
}
#ez-promos ul {
    float:left
}
#ez-promos ul li {
    list-style-type:none!important;
    line-height:18px;
    font-size:18px
}
#ez-promos ul li.ez-promo-title {
    margin-bottom:8px;
    line-height:34px;
    font-size:26px
}
#ez-promos .ez-promo-btns {
    margin-top:16px;
    float:right
}
#ez-promos .ez-promotions-terms {
    margin-right:10px
}
#ez-support-chat {
    width:28%;
    height:140px;
    background:#ff3dd9;
    position:relative;
    padDing:0 10px;
    float:left;
    display:table
}
#ez-support-chat * {
    color:#fff
}
#ez-support-chat ul {
    display:table-cell;
    vertical-align:middle
}
#ez-support-chat .ez-icon {
    border:none;
    font-size:40px
}
#ez-support-chat li {
    width:100%;
    text-align:center;
    list-style-type:none!important
}
#ez-support-chat li span {
    font-size:16px;
    line-height:26px;
    display:block
}
#ez-support-chat li span:first-child {
    margin-top:6px;
    font-size:24px
}
#ez-support-chat a {
    position:absolute;
    top:0;
    right:0;
    bottom:0;
    left:0
}
#ez-support-trust-panel {
    width:70.4%;
    height:140px;
    float:right;
    padDing:14px 0;
    background:#eeedef
}
#ez-support-trust-panel ul {
    width:100%
}
#ez-support-content {
    margin-top:20px
}
#ez-support-content p {
    padDing-right:40%
}
#ez-support-content .ez-accordion {
    background:#b9c7c4;
    padDing:26px 20px 20px 20px;
    position:relative
}
#ez-support-content .ez-accordion h2 {
    color:#fff;
    font-weight:normal;
    margin-bottom:22px
}
#ez-support-content .ez-accordion .ez-accordiontext {
    padDing-right:40%
}
#ez-country-content-wrap {
    background:#f6f6f6;
    padDing-top:10px;
    padDing-bottom:10px
}
#ez-country-content-wrap .ez-topup-btn {
    width:100%
}
.ez-lanDing-two #ez-country-content-wrap,
.ez-lanDing-three #ez-country-content-wrap {
    margin-bottom:0;
    padDing-top:0;
    margin-top:-10px;
    background:#fff
}
.ez-lanDing-two #ez-lanDing-trust-panel,
.ez-lanDing-three #ez-lanDing-trust-panel {
    background:#f6f6f6
}
.ez-lanDing-two .ez-asterisk.ez-svg,
.ez-lanDing-three .ez-asterisk.ez-svg {
    height:400px;
    width:400px;
    position:absolute;
    bottom:0;
    right:0;
    z-index:1;
    margin:0 -100px -188px
}
.ez-lanDing-two .ez-asterisk.ez-svg path,
.ez-lanDing-three .ez-asterisk.ez-svg path {
    fill:#f6f6f6
}
.ez-lanDing-two .ez-lanDing-bottom,
.ez-lanDing-three .ez-lanDing-bottom {
    margin-bottom:-30px;
    padDing-bottom:30px;
    background:#f6f6f6
}
.ez-lanDing-two .ez-lanDing-bottom .ez-asterisk.ez-svg path,
.ez-lanDing-three .ez-lanDing-bottom .ez-asterisk.ez-svg path {
    fill:#fff
}
.ez-country-content-topup {
    min-height:320px;
    position:relative
}
.ez-country-content-topup .ez-topup-btn {
    position:absolute;
    bottom:0;
    left:0
}
.ez-lanDing-three #ez-country-content-wrap {
    padDing-top:30px;
    padDing-bottom:10px
}
.ez-country-content {
    width:48%;
    float:left
}
.ez-country-content.ez-float-right {
    float:right
}
.ez-country-content strong {
    color:#33c541;
    font-size:24px
}
.ez-country-content .ez-topup-btn {
    width:100%
}
.ez-country-content-two {
    width:48%;
    margin-top:26px;
    float:left
}
.ez-country-content-two .ez-topup-btn {
    width:100%
}
#ez-lanDing-promo-text strong {
    color:#33c541;
    font-size:19px
}
#ez-lanDing-promo-text h4 {
    font-size:22px
}
#ez-lanDing-trust-panel,
#ez-lanDing-prmotion,
#ez-lanDing-promo-text {
    width:48%;
    float:right;
    margin-bottom:14px
}
#ez-lanDing-trust-panel .ez-trust-panel,
#ez-lanDing-prmotion .ez-trust-panel,
#ez-lanDing-promo-text .ez-trust-panel {
    width:100%
}
.ez-lanDing-three #ez-lanDing-trust-panel,
.ez-lanDing-three #ez-lanDing-prmotion {
    float:left
}
#ez-lanDing-trust-panel {
    padDing-top:40px;
    padDing-bottom:40px;
    background:#fff
}
#ez-lanDing-trust-panel h2 {
    text-align:center;
    padDing:0 15px;
    margin-bottom:50px
}
#ez-lanDing-prmotion {
    position:relative;
    background:#001a6e
}
#ez-lanDing-prmotion h2 {
    font-size:60px;
    line-height:56px;
    font-weight:800;
    color:#33c541;
    margin:38px 36% 38px 25px
}
#ez-lanDing-prmotion p {
    background:#0072C6;
    color:#fff;
    font-size:28px;
    margin-bottom:0!important;
    padDing:20px 25px
}
#ez-lanDing-prmotion .ez-svg {
    width:350px;
    height:350px;
    margin:0 -86px -88px;
    opacity:.1;
    filter:alpha(opacity=10)
}
#ez-suggested-articles {
    width:48%;
    float:left
}
#ez-side-text {
    width:48%;
    float:right
}
.ez-wrap-subscription {
    background-color:#f6f6f6;
    padDing-top:20px
}
#ez-subscribe-newsletter {
    width:100%;
    float:left;
    margin-bottom:10px
}
#ez-subscribe-newsletter .ez-error-desktop {
    display:block
}
#ez-subscribe-newsletter .ez-error-mobile {
    display:none
}
#ez-subscribe-newsletter h3 {
    font-family:Roboto,
    sans-serif;
    font-weight:normal;
    color:#000;
    font-size:22px;
    margin-bottom:12px
}
#ez-subscribe-newsletter h4 {
    font-size:18px;
    font-weight:normal;
    color:#000;
    margin-bottom:20px;
    margin-top:-10px;
    font-family:Roboto,
    sans-serif
}
#ez-subscribe-newsletter div {
    margin-bottom:10px
}
#ez-subscribe-newsletter div input {
    width:64%
}
#ez-subscribe-newsletter div button {
    width:33%;
    margin-left:1%
}
#ez-subscribe-newsletter span {
    font-size:22px;
    font-family:Roboto,
    sans-serif;
    display:inline-block;
    line-height:20px;
    margin-top:5px
}
#ez-migpage {
    text-align:center
}
#ez-migpage-top {
    height:437px;
    position:relative;
    z-index:2
}
#ez-migpage-top h1 {
    font-size:68px;
    text-align:left;
    line-height:60px
}
#ez-migpage-top img {
    width:450px;
    height:186px;
    display:block;
    margin:20px auto -6px auto
}
#ez-migpage-top #ez-tell-more {
    min-width:200px;
    height:54px;
    background:#fff;
    display:inline-block;
    cursor:pointer;
    margin-top:20px;
    border-radius:54px;
    box-shadow:0,
    3px,
    5px,
    #fff;
    -webkit-box-shadow:0,
    3px,
    5px,
    0,
    rgba(0,
    0,
    0,
    .3);
    -moz-box-shadow:0,
    3px,
    5px,
    0,
    rgba(0,
    0,
    0,
    .3);
    box-shadow:0 3px 5px 0 rgba(0,
    0,
    0,
    .3)
}
#ez-migpage-top #ez-tell-more .fa {
    width:44px;
    height:44px;
    color:#fff;
    font-size:26px;
    line-height:44px;
    background:#33c541;
    float:left;
    margin:5px 14px 0 5px;
    border-radius:50%
}
#ez-migpage-top #ez-tell-more h4 {
    color:#001a6e;
    font-size:22px;
    line-height:54px;
    float:left
}
#ez-migpage-middle {
    position:relative;
    z-index:1;
    margin-top:-24px;
    padDing-bottom:30px
}
#ez-migpage-middle>div:first-child {
    background:#f6f6f6;
    padDing-top:30px;
    padDing-bottom:50px
}
#ez-migpage-middle>div:first-child p {
    margin:8px 0 24px 0;
    font-size:24px
}
#ez-migpage-middle #ez-snippet-migrationmiddlesection {
    margin-top:-46px
}
#ez-migpage-middle li {
    width:270px;
    display:inline-block;
    vertical-align:top
}
#ez-migpage-middle li img {
    width:170px;
    height:auto
}
#ez-migpage-middle h4 {
    font-weight:normal;
    font-size:28px
}
#ez-migpage-bottom {
    padDing:30px 0;
    margin-bottom:-30px;
    background:#001a6e
}
#ez-migpage-bottom h2 {
    color:#fff
}
#ez-migpage-bottom h2 span {
    color:#33c541
}
#ez-migpage-bottom p {
    font-size:24px;
    color:#fff;
    margin:16px 0 34px 0
}
#ez-migpage-bottom .ez-button {
    width:370px
}
#ez-migpage-bottom img {
    width:200px;
    height:84px;
    margin:30px 0 50px 0
}
#ez-careers-movie {
    height:620px
}
#ez-carrers-nav {
    width:100%;
    height:120px;
    text-align:center;
    position:relative;
    margin-top:-120px;
    background:rgba(0,
    26,
    110,
    .85)
}
#ez-carrers-nav nav {
    width:74%;
    height:100%;
    margin-left:13%;
    display:table
}
#ez-carrers-nav nav a {
    width:25%;
    height:100%;
    font:bold 32px/20px BrauerNeuePro,
    sans-serif;
    color:#fff;
    display:table-cell;
    vertical-align:middle;
    position:relative;
    border-left:2px solid rgba(255,
    255,
    255,
    .05);
    border-right:2px solid rgba(10,
    18,
    43,
    .5);
    -webkit-transition:all .5s ease;
    transition:all .5s ease;
    -moz-transition:all .5s ease;
    -o-transition:all .5s ease
}
#ez-carrers-nav nav a:first-child {
    border-left:none
}
#ez-carrers-nav nav a.ez-no-border {
    border-right:0
}
#ez-carrers-nav nav a:hover {
    text-decoration:none;
    background:#001a6e;
    color:#6c7292
}
#ez-carrers-nav nav a:hover span {
    color:#ff3dd9
}
#ez-carrers-nav nav span {
    display:block;
    margin-bottom:10px;
    color:#6c7292
}
#ez-carrers-nav nav span.fa-map-marker {
    font-size:34px
}
#ez-carrers-nav nav span.fa-briefcase {
    margin-top:3px
}
#ez-carrers-nav nav .ez-arrow {
    display:block;
    position:absolute;
    margin-left:-15px;
    left:50%;
    bottom:-40px;
    display:none;
    width:1px;
    border:15px solid;
    border-color:transparent transparent transparent transparent;
    border-top-color:#001a6e
}
#ez-carrers-nav nav.ez-active-about .ez-about,
#ez-carrers-nav nav.ez-active-team .ez-team,
#ez-carrers-nav nav.ez-active-offices .ez-offices,
#ez-carrers-nav nav.ez-active-jobs .ez-jobs {
    cursor:default;
    text-decoration:none;
    background:#001a6e;
    color:#6c7292
}
#ez-carrers-nav nav.ez-active-about .ez-about span,
#ez-carrers-nav nav.ez-active-team .ez-team span,
#ez-carrers-nav nav.ez-active-offices .ez-offices span,
#ez-carrers-nav nav.ez-active-jobs .ez-jobs span {
    color:#ff3dd9
}
#ez-carrers-nav nav.ez-active-about .ez-about .ez-arrow,
#ez-carrers-nav nav.ez-active-team .ez-team .ez-arrow,
#ez-carrers-nav nav.ez-active-offices .ez-offices .ez-arrow,
#ez-carrers-nav nav.ez-active-jobs .ez-jobs .ez-arrow {
    display:block
}
#ez-careers-banner h1 {
    color:#fff;
    font-size:100px;
    line-height:86px;
    padDing-top:150px
}
#ez-careers-banner p {
    color:#fff;
    font-size:28px!important
}
#ez-careers-banner p span {
    color:#33c541;
    font-weight:bold
}
#ez-careers-banner hr {
    width:150px;
    height:4px;
    border:none;
    margin:30px auto;
    background:rgba(255,
    255,
    255,
    .3)
}
#ez-careers-banner img {
    width:100%;
    height:650px
}
#ez-careers-wrap {
    overflow:hidden
}
#ez-careers-wrap h2,
#ez-careers-wrap h3 {
    font-size:50px;
    color:#ff3dd9
}
#ez-careers-wrap h3 {
    font-size:32px
}
#ez-careers-wrap p {
    font-size:18px
}
#ez-careers-wrap .ez-careers-wrap {
    width:1070px;
    margin:0 auto;
    padDing:44px 0
}
#ez-careers-wrap .ez-margin-bottom {
    margin-bottom:44px
}
#ez-careers-wrap .ez-careers-btn {
    background:#ff3dd9;
    font:bold 30px BrauerNeuePro,
    sans-serif;
    color:#fff;
    padDing:0 30px;
    opacity:1;
    border-radius:40px;
    -webkit-transition:opacity .6s ease;
    transition:opacity .6s ease;
    -moz-transition:opacity .6s ease;
    -o-transition:opacity .6s ease
}
#ez-careers-wrap .ez-careers-btn:hover {
    text-decoration:none;
    opacity:.8
}
#ez-careers-wrap a.ez-careers-btn {
    line-height:70px
}
#ez-careers-wrap .ez-careers-background {
    background:#eeedef
}
#ez-careers-wrap .ez-careers-big-text {
    font-size:28px;
    line-height:32px
}
#ez-careers-wrap .ez-careers-box {
    background:#fff;
    padDing:26px;
    border:1px solid #e7e3e7;
    margin-bottom:20px;
    box-shadow:0,
    3px,
    6px,
    #fff;
    -webkit-box-shadow:0,
    3px,
    6px,
    -2px,
    rgba(0,
    0,
    0,
    .1);
    -moz-box-shadow:0,
    3px,
    6px,
    -2px,
    rgba(0,
    0,
    0,
    .1);
    box-shadow:0 3px 6px -2px rgba(0,
    0,
    0,
    .1)
}
#ez-careers-wrap .ez-careers-box.ez-last {
    margin-bottom:0
}
#ez-careers-wrap .ez-careers-quote img {
    width:140px;
    height:125px;
    float:left
}
#ez-careers-wrap .ez-careers-quote p {
    padDing-left:45px;
    margin-left:180px;
    font-size:24px;
    line-height:28px
}
#ez-careers-wrap .ez-careers-quote .fa-quote-left {
    font-size:34px;
    float:left;
    margin:0 12px 0 -45px;
    color:#b9c7c4
}
#ez-careers-wrap .ez-careers-quote.ez-quote-right img {
    float:right
}
#ez-careers-wrap .ez-careers-quote.ez-quote-right p {
    margin:0 180px 0 0
}
#ez-careers-wrap .ez-careers-signature {
    color:#b9c7c4;
    font-size:18px!important;
    display:block
}
#ez-careers-wrap .ez-careers-signature span {
    color:#33c541
}
#ez-careers-wrap .ez-careers-article li {
    width:54%;
    float:left;
    margin-bottom:50px;
    text-align:left
}
#ez-careers-wrap .ez-careers-article li:first-child {
    width:46%;
    text-align:right;
    float:right
}
#ez-careers-wrap .ez-careers-article li img {
    width:400px;
    height:258px
}
#ez-careers-wrap .ez-careers-article li h3 {
    margin-bottom:14px
}
#ez-careers-wrap .ez-careers-article li br {
    line-height:40px
}
#ez-careers-wrap .ez-careers-article.ez-left-article li {
    float:right
}
#ez-careers-wrap .ez-careers-article.ez-left-article li:first-child {
    text-align:left;
    float:left
}
#ez-careers-wrap .ez-careers-article.ez-last li {
    margin-bottom:0
}
#ez-careers-wrap .ez-asterisk-line-holder {
    position:relative
}
#ez-careers-wrap .ez-asterisk-line-holder>* {
    position:relative;
    z-index:2
}
#ez-careers-wrap .ez-asterisk-line-holder .ez-asterisk-line {
    width:1px;
    background:#c1c0c4;
    position:absolute;
    top:17px;
    left:50%;
    bottom:46px;
    z-index:1
}
#ez-careers-wrap .ez-asterisk-line-holder .ez-asterisk-line span {
    font-size:24px;
    margin:-34px 0 0 -11px;
    display:block
}
#ez-careers-pictures-wrap {
    height:364px;
    position:relative;
    overflow:hidden;
    z-index:3;
    margin:-15px 0 -10px
}
#ez-careers-pictures {
    text-align:center;
    margin-left:-664px;
    white-space:nowrap;
    position:absolute;
    top:30px;
    left:50%
}
#ez-careers-pictures li {
    width:250px;
    height:290px;
    background:#fff;
    display:inline-block;
    padDing-top:15px;
    margin-left:15px;
    position:relative;
    box-shadow:0,
    3px,
    5px,
    #fff;
    -webkit-box-shadow:0,
    3px,
    5px,
    0,
    rgba(0,
    0,
    0,
    .3);
    -moz-box-shadow:0,
    3px,
    5px,
    0,
    rgba(0,
    0,
    0,
    .3);
    box-shadow:0 3px 5px 0 rgba(0,
    0,
    0,
    .3)
}
#ez-careers-pictures li:first-child {
    margin-left:0;
    left:18px;
    z-index:1;
    -webkit-transform:rotate(8deg);
    -moz-transform:rotate(8deg);
    -ms-transform:rotate(8deg);
    -o-transform:rotate(8deg);
    -webkit-transform-origin:0 50%;
    -moz-transform-origin:0 50%;
    -o-transform-origin:top left;
    -ms-transform-origin:top left
}
#ez-careers-pictures li:nth-child(2) {
    top:-34px;
    left:20px;
    z-index:2;
    -webkit-transform:rotate(20deg);
    -moz-transform:rotate(20deg);
    -ms-transform:rotate(20deg);
    -o-transform:rotate(20deg);
    -webkit-transform-origin:0 50%;
    -moz-transform-origin:0 50%;
    -o-transform-origin:top left;
    -ms-transform-origin:top left
}
#ez-careers-pictures li:nth-child(3) {
    top:19px;
    left:-5px;
    z-index:3;
    -webkit-transform:rotate(-12deg);
    -moz-transform:rotate(-12deg);
    -ms-transform:rotate(-12deg);
    -o-transform:rotate(-12deg);
    -webkit-transform-origin:0 50%;
    -moz-transform-origin:0 50%;
    -o-transform-origin:top left;
    -ms-transform-origin:top left
}
#ez-careers-pictures li:nth-child(4) {
    top:30px;
    left:-24px;
    z-index:1;
    -webkit-transform:rotate(-5deg);
    -moz-transform:rotate(-5deg);
    -ms-transform:rotate(-5deg);
    -o-transform:rotate(-5deg);
    -webkit-transform-origin:0 50%;
    -moz-transform-origin:0 50%;
    -o-transform-origin:top left;
    -ms-transform-origin:top left
}
#ez-careers-pictures li:nth-child(5) {
    top:-25px;
    left:-65px;
    z-index:2;
    -webkit-transform:rotate(7deg);
    -moz-transform:rotate(7deg);
    -ms-transform:rotate(7deg);
    -o-transform:rotate(7deg);
    -webkit-transform-origin:0 50%;
    -moz-transform-origin:0 50%;
    -o-transform-origin:top left;
    -ms-transform-origin:top left
}
#ez-careers-pictures li img {
    width:230px;
    height:230px
}
#ez-careers-poster.ez-poster-full {
    -webkit-background-size:auto;
    -moz-background-size:auto;
    -o-background-size:auto;
    background-size:auto
}
#ez-careers-poster #ez-video-btn {
    width:100%;
    height:100%;
    font-size:60px;
    opacity:.6;
    margin-top:0;
    padDing:0 30px;
    background:transparent;
    -webkit-transition:opacity .6s ease;
    transition:opacity .6s ease;
    -moz-transition:opacity .6s ease;
    -o-transition:opacity .6s ease
}
#ez-careers-poster #ez-video-btn:hover {
    opacity:1
}
#ez-careers-poster #ez-video-btn span {
    display:block;
    margin:0 auto 10px auto;
    font-size:60px
}
#ez-careers-poster button {
    margin-top:246px
}
#ez-careers-poster button .fa {
    width:100px;
    height:100px;
    color:#fff;
    border:2px solid #fff;
    padDing-left:12px;
    padDing-top:18px;
    border-radius:50%
}
#ez-careers-poster p {
    width:100%;
    color:#fff;
    font-size:34px;
    line-height:48px;
    position:absolute;
    bottom:40px;
    padDing:0 10px
}
#ez-careers-poster div {
    width:820px;
    height:230px;
    position:relative;
    top:170px;
    left:50%;
    margin-left:-410px
}
#ez-careers-poster .ez-map-btn {
    height:42px;
    position:absolute;
    z-index:2;
    display:block
}
#ez-careers-poster .ez-map-btn.ez-dublin {
    width:94px;
    top:21px;
    left:275px
}
#ez-careers-poster .ez-map-btn.ez-barcelona {
    width:124px;
    top:68px;
    left:270px
}
#ez-careers-poster .ez-map-btn.ez-bucharest {
    width:130px;
    top:64px;
    left:426px
}
#ez-careers-poster .ez-map-btn.ez-miami {
    width:84px;
    left:132px;
    top:113px
}
#ez-careers-poster .ez-map-btn.ez-dubai {
    width:82px;
    left:516px;
    top:119px
}
#ez-careers-poster .ez-map-btn.ez-salvador {
    width:154px;
    left:5px;
    top:161px
}
#ez-careers-poster .ez-map-btn.ez-dhaka {
    width:88px;
    left:614px;
    top:127px
}
#ez-careers-bottom {
    height:240px;
    background:#001a6e;
    text-align:center;
    position:relative
}
#ez-careers-bottom a {
    margin-top:80px;
    position:relative;
    z-index:3
}
#ez-careers-bottom .ez-asterisk {
    font-size:150px;
    color:#fff;
    opacity:.2;
    filter:alpha(opacity=20);
    position:absolute;
    right:40px;
    top:10px;
    z-index:2
}
#ez-careers-bottom .ez-asterisk.ez-100 {
    font-size:90px;
    right:220px;
    top:20px
}
#ez-careers-bottom .ez-asterisk.ez-44 {
    font-size:44px;
    right:280px;
    top:140px
}
#ez-careers-bottom .ez-asterisk.ez-22 {
    font-size:22px;
    right:350px;
    top:96px
}
.ez-careers-office-map {
    margin-bottom:30px
}
.ez-careers-office-map:first-child {
    margin-top:-110px
}
.ez-careers-office-map li {
    width:100%;
    text-align:center;
    position:relative
}
.ez-careers-office-map li:first-child {
    margin-bottom:10px
}
.ez-careers-office-map li span {
    width:140px;
    height:140px;
    display:inline-block;
    background:top center no-repeat;
    background-size:140px 140px;
    position:relative;
    z-index:2;
    border-radius:50%
}
.ez-careers-office-map li hr {
    width:100%;
    position:absolute;
    top:71px;
    left:0;
    z-index:1;
    border-color:#b9c7c4
}
#ez-careers-joblist h2 {
    margin-bottom:60px;
    text-align:center
}
#ez-careers-joblist .ez-asterisk-line {
    top:128px!important
}
.ez-careers-job {
    padDing-top:20px!important;
    padDing-bottom:20px!important
}
.ez-careers-job p {
    float:right;
    font-size:22px!important;
    color:#b9c7c4
}
.ez-careers-job p:first-child {
    color:#001a6e;
    font-size:24px!important;
    float:left
}
.ez-careers-job p span {
    margin-top:6px
}
.ez-careers-job a {
    position:absolute;
    top:0;
    right:0;
    bottom:0;
    left:0
}
form#resellersForm {
    max-width:600px
}
form#resellersForm textarea {
    height:80px
}
form#resellersForm div.region li.ez-airtime-btn span,
form#resellersForm div.volume li.ez-airtime-btn span {
    left:25px;
    color:#000
}
form#resellersForm div.ez-airtime-wrap li {
    list-style-type:none
}
form#resellersForm span.ez-nocountry-placeholder-default {
    color:#b1b1c6!important
}
#ez-payment-response-page {
    width:100%
}
#ez-payment-response-page #left-side {
    max-width:520px;
    float:left;
    width:45%
}
#ez-payment-response-page #left-side #referOptionsWrap {
    display:none
}
#ez-payment-response-page #left-side #referOptionsWrap .referOptions {
    display:none
}
#ez-payment-response-page #left-side .ez-subtitle {
    line-height:normal;
    margin-bottom:16px;
    color:#001a6e
}
#ez-payment-response-page #left-side .ez-terms-panel-mobile {
    display:none;
    font-size:14px;
    padDing-left:20px;
    padDing-right:20px
}
#ez-payment-response-page #left-side .ez-summary-type {
    display:block;
    background-color:#fff;
    margin-bottom:10px
}
#ez-payment-response-page #left-side .ez-second-color {
    font-size:26px
}
#ez-payment-response-page #left-side .referButton {
    display:none;
    margin:-16px 0 20px 0
}
#ez-payment-response-page #left-side .referButton a {
    width:100%
}
#ez-payment-response-page #left-side .referButton a span {
    color:#ff3dd9;
    font-weight:bold
}
#ez-payment-response-page #right-side {
    width:49%;
    float:right;
    line-height:50px
}
#ez-payment-response-page #right-side .ez-subtitle {
    line-height:normal;
    margin-bottom:16px;
    color:#001a6e
}
#ez-payment-response-page #right-side .ez-terms {
    font-size:14px
}
#ez-payment-response-page #right-side .ez-terms-panel {
    line-height:25px;
    line-height:normal;
    display:none
}
#ez-payment-response-page #share-email {
    display:none
}
.ez-post-preview-component {}.ez-post-preview-component .ez-cmenu {
    border-bottom:none
}
.ez-post-preview-component .ez-cmenu li a {
    font-size:17px
}
.ez-post-preview-component #ez-account-menu li {}.ez-post-preview-component #ez-account-menu li a {
    line-height:45px;
    padDing-top:5px;
    padDing-bottom:5px
}
.ez-post-preview-component .ez-post-tiled {
    background-color:#fff;
    margin:0 25px 25px 0;
    border-top:9px solid #ff3dd9;
    overflow:hidden;
    position:relative
}
.ez-post-preview-component .ez-post-foreground {
    z-index:10;
    position:relative
}
.ez-post-preview-component .ez-post-coveringlink {
    display:none
}
.ez-post-preview-component .CM-news.ez-post-tiled {
    border-top:9px solid #33c541
}
.ez-post-preview-component .CM-topup.ez-post-tiled {
    border-top:9px solid #ff3dd9
}
.ez-post-preview-component .CM-effect.ez-post-tiled {
    border-top:9px solid #001a6e
}
.ez-post-preview-component h4.ez-post-title {
    text-align:center;
    padDing:20px
}
.ez-post-preview-component h4.ez-post-title a,
.ez-post-preview-component h4.ez-post-title a:hover {
    color:#001a6e;
    line-height:normal;
    text-decoration:none
}
.ez-post-preview-component a.ez-post-image {
    min-width:100%;
    max-height:100%;
    background-repeat:no-repeat!important;
    line-height:14px
}
.ez-post-preview-component a.ez-post-image img {
    width:100%
}
.ez-post-preview-component .ez-post-content {
    padDing:20px 20px 0 20px;
    font-size:16px
}
.ez-post-preview-component .ez-post-content a.ez-button {
    width:100%;
    margin:25px auto 4px auto
}
.ez-post-preview-component a.ez-localblog-linkbutton {
    display:block;
    text-align:center;
    border-radius:4px;
    height:48px;
    line-height:48px;
    color:#fff;
    width:100%;
    margin:25px auto 25px auto
}
.ez-post-preview-component .CM-news a.ez-localblog-readmore {
    background-color:#33c541
}
.ez-post-preview-component .CM-news a.ez-localblog-readmore:hover {
    background-color:#33c541
}
.ez-post-preview-component .CM-topup a.ez-localblog-readmore {
    background-color:#ff3dd9
}
.ez-post-preview-component .CM-topup a.ez-localblog-readmore:hover {
    background-color:#ff5cdf
}
.ez-post-preview-component .CM-effect a.ez-localblog-readmore {
    background-color:#001a6e
}
.ez-post-preview-component .CM-effect a.ez-localblog-readmore:hover {
    background-color:#1a317d
}
.ez-post-preview-component a.ez-localposts-more {
    background-color:#b4bec0;
    max-width:400px
}
.ez-post-preview-component .ez-post-footer {
    position:relative;
    z-index:20
}
.ez-post-preview-component .ez-post-footer .addthis_toolbox {
    text-align:right;
    margin-right:20px;
    float:right
}
.ez-post-preview-component .ez-post-footer .addthis_toolbox .fa {
    font-size:16px;
    line-height:25px;
    border-radius:13px;
    text-align:center;
    background-color:#929ea0;
    color:#fff;
    width:25px;
    margin-top:10px
}
.ez-post-preview-component .ez-post-commentslink {
    float:left;
    font-size:25px;
    text-decoration:none;
    color:#aaa;
    margin-left:20px;
    line-height:44px;
    position:relative;
    top:-3px
}
.ez-post-preview-component a.commenticon {
    font-size:15px!important
}
.ez-post-preview-component .ez-post-commentslink a {
    text-decoration:none;
    color:#929ea0;
    font-size:13px
}
.ez-post-preview-component .ez-share {
    font-size:13px!important;
    line-height:44px!important;
    text-decoration:none;
    color:#aaa
}
.ez-post-preview-component .addthis_toolbox span {
    font-size:25px;
    color:#aaa;
    line-height:inherit;
    float:left
}
.ez-post-preview-component .CM-grey .ez-post-foreground {
    height:230px;
    position:relative
}
.ez-post-preview-component svg.ez-svg.ez-background-asterisk.ez-convert-svg {
    width:100%;
    height:100%;
    left:99%;
    top:45%;
    position:absolute;
    z-index:0
}
.ez-post-preview-component .ez-post-tiled.CM-grey .ez-background-asterisk path {
    fill:#929ea0
}
.ez-post-preview-component .ez-post-tiled.CM-grey {
    height:230px;
    border-top:none;
    background-color:#b4bec0
}
.ez-post-preview-component .ez-post-tiled.CM-grey h4 {
    font-size:40px;
    line-height:40px;
    font-weight:800;
    position:absolute;
    top:50%;
    left:50%;
    margin-right:-50%;
    -webkit-transform:translate(-50%,
    -50%);
    -ms-transform:translate(-50%,
    -50%);
    transform:translate(-50%,
    -50%);
    color:#fff
}
.ez-post-preview-component .ez-post-tiled.CM-grey .ez-post-image {
    display:none
}
.ez-post-preview-component .ez-post-tiled.CM-grey .ez-post-content {
    display:none
}
.ez-post-preview-component .ez-post-tiled.CM-grey .ez-post-footer {
    display:none
}
.ez-post-preview-component .floatfix:after {
    content:"";
    display:table;
    clear:both
}
.ez-post-preview-component .ez-localblog-tilescontainer {
    background-color:#eeedef;
    margin-top:-30px;
    padDing:20px 0 0 20px
}
@media(max-width:500px){.ez-post-preview-component .ez-content-wrap {
    margin-top:-20px
}
.ez-post-preview-component h1 {
    font-size:30px;
    line-height:30px
}
.ez-post-preview-component h1 .smaller {
    font-size:26px
}
.ez-post-preview-component .ez-localblog-tilescontainer {
    padDing:0;
    margin-top:-20px
}
.ez-post-preview-component .ez-post-tiled {
    border-top:none!important;
    margin:0;
    height:114px;
    border-bottom:1px solid #ddd
}
.ez-post-preview-component .ez-post-foreground {
    padDing-top:5px
}
.ez-post-preview-component .ez-post-coveringlink {
    display:block;
    position:absolute;
    width:100%;
    height:114px
}
.ez-post-preview-component h4.ez-post-title {
    padDing:0
}
.ez-post-preview-component h4.ez-post-title a,
.ez-post-preview-component h4.ez-post-title a:hover {
    float:right;
    font-size:18px;
    line-height:18px;
    width:54%;
    font-weight:normal;
    margin-right:13px;
    text-align:left;
    text-decoration:none
}
.ez-post-preview-component a.ez-post-image {
    margin-left:1px;
    float:left;
    min-width:0;
    width:39%;
    height:99px;
    background-size:cover!important;
    background-position:center!important
}
.ez-post-preview-component a.ez-post-image img {
    display:none
}
.ez-post-preview-component .ez-post-tiled.CM-news a.ez-post-image {
    border-bottom:4px solid #33c541
}
.ez-post-preview-component .ez-post-tiled.CM-topup a.ez-post-image {
    border-bottom:4px solid #ff3dd9
}
.ez-post-preview-component .ez-post-tiled.CM-effect a.ez-post-image {
    border-bottom:4px solid #001a6e
}
.ez-post-preview-component .ez-post-content {
    display:none
}
.ez-post-preview-component .ez-post-footer {
    bottom:59px;
    right:20px;
    position:absolute;
    bottom:-3px
}
.ez-post-preview-component .ez-post-commentslink {
    font-size:16px
}
.ez-post-preview-component .addthis_toolbox {
    display:none
}
.ez-post-preview-component .ez-post-tiled.CM-grey {
    display:none
}
}
.ez-localposts-list .ez-content-wrap {
    margin-top:-20px
}
.ez-localposts-list h1 {
    font-size:30px;
    line-height:30px
}
.ez-localposts-list h1 .smaller {
    font-size:26px
}
.ez-localposts-list .ez-localblog-tilescontainer {
    padDing:0;
    margin-top:-20px
}
.ez-localposts-list .ez-post-tiled {
    position:relative;
    border-top:none!important;
    margin:0;
    height:114px;
    border-bottom:1px solid #ddd
}
.ez-localposts-list .ez-post-foreground {
    padDing-top:5px
}
.ez-localposts-list .ez-post-coveringlink {
    display:block;
    position:absolute;
    width:100%;
    height:114px
}
.ez-localposts-list h4.ez-post-title {
    float:right;
    font-size:18px;
    line-height:18px;
    width:54%;
    font-weight:normal;
    margin-right:13px;
    padDing:0;
    text-align:left
}
.ez-localposts-list h4.ez-post-title a,
.ez-localposts-list h4.ez-post-title a:hover {
    color:#001a6e;
    line-height:19px;
    text-decoration:none
}
.ez-localposts-list a.ez-post-image {
    margin-left:1px;
    float:left;
    min-width:0;
    width:39%;
    height:99px;
    background-size:cover!important;
    background-position:center!important
}
.ez-localposts-list a.ez-post-image img {
    display:none
}
.ez-localposts-list .ez-post-tiled.CM-news a.ez-post-image {
    border-bottom:4px solid #33c541
}
.ez-localposts-list .ez-post-tiled.CM-topup a.ez-post-image {
    border-bottom:4px solid #ff3dd9
}
.ez-localposts-list .ez-post-tiled.CM-effect a.ez-post-image {
    border-bottom:4px solid #001a6e
}
.ez-localposts-list .ez-post-content {
    display:none
}
.ez-localposts-list .ez-post-footer {
    bottom:59px;
    right:20px;
    position:absolute;
    bottom:-3px
}
.ez-localposts-list .ez-post-commentslink a {
    font-size:16px;
    text-decoration:none;
    color:#aaa;
    top:-3px
}
.ez-localposts-list .addthis_toolbox {
    display:none
}
.ez-localposts-list .ez-post-tiled.CM-grey {
    display:none
}
.template_right_column #ez-cms-content {
    display:table-cell
}
.template_right_column h1 {
    font-size:60px;
    line-height:52px
}
.template_right_column h1 .smaller {
    font-size:53px
}
.template_right_column .ez-page-title {
    font-size:61px
}
.template_right_column #ez-cms-content {
    width:74%
}
.template_right_column .ez-right-zone {
    float:right;
    padDing-top:25px;
    display:table-cell;
    width:250px;
    margin-left:20px
}
.template_right_column .ez-content-wrap {
    padDing-top:0
}
@media(max-width:1010px){.template_right_column .ez-right-zone {
    display:none
}
.template_right_column h1 {
    font-size:45px;
    line-height:40px
}
.template_right_column h1 .smaller {
    font-size:40px
}
}
@media(max-width:500px){.template_right_column .ez-right-zone {
    display:none
}
.template_right_column h1 {
    font-size:30px;
    line-height:26px
}
.template_right_column h1 .smaller {
    font-size:26px
}
}
span.ez-bonus-redeemed-checkout {
    display:none;
    font-family:Roboto;
    font-size:14px;
    margin-left:4px;
    margin-bottom:30px
}
span.ez-bonus-redeemed-checkout span.ez-highlight {
    color:#ac0000;
    font-weight:bold
}
#ez-vouchers-page #left-side {
    width:45%;
    float:left
}
#ez-vouchers-page #left-side img {
    width:100%
}
#ez-vouchers-page #right-side {
    width:49%;
    float:right
}
#ez-vouchers-page .ez-more-vouchers {
    margin-top:20px
}
#ez-vouchers-page .ez-receipt-icon {
    font-size:30px;
    margin-top:6px
}
#ez-vouchers-page .ez-no-vouchers-snippet p {
    line-height:22px;
    margin-bottom:14px
}
#ez-vouchers-page .ez-no-vouchers-invite h2 {
    font-weight:normal;
    line-height:24px;
    text-transform:none;
    font-size:36px;
    margin-bottom:14px
}
#ez-vouchers-page .ez-no-vouchers-invite p {
    line-height:22px;
    margin-bottom:14px
}
#ez-vouchers-page .ez-no-vouchers-invite .ez-subtitle-small {
    line-height:normal;
    margin-bottom:14px
}
#ez-vouchers-page .ez-no-vouchers-invite .ez-subtitle-small .ez-promotion-amount {
    font-weight:bold
}
#ez-vouchers-page .ez-back-btn {
    margin-top:30px
}
#ez-vouchers-page .ez-terms-panel {
    line-height:25px;
    display:none
}
#ez-vouchers-page .ez-invite-social {
    padDing:20px;
    background:#eeedef
}
#ez-vouchers-page .ez-vouchers-filter-band {
    text-align:center;
    position:relative
}
#ez-vouchers-page .ez-vouchers-filter-band h2 {
    float:left
}
#ez-vouchers-page .ez-vouchers-filter-band .ez-mobile-menu {
    padDing:0;
    display:inline-block;
    float:right;
    background:#fff
}
#ez-vouchers-page .ez-vouchers-filter-band .ez-mobile-menu li {
    width:auto;
    min-width:150px
}
#ez-vouchers-page .ez-vouchers-filter-band .ez-mobile-menu li div {
    padDing-left:10px;
    padDing-right:10px
}
#ez-vouchers-page .ez-vouchers-filter-band .ez-button {
    position:absolute;
    top:0;
    right:0
}
#ez-vouchers-page .ez-vouchers-items .ez-table,
#ez-vouchers-page .ez-vouchers-expired .ez-table {
    margin-top:10px
}
#ez-vouchers-page .ez-vouchers-items .ez-table th.ez-table-selector,
#ez-vouchers-page .ez-vouchers-expired .ez-table th.ez-table-selector {
    width:30px
}
#ez-vouchers-page .ez-vouchers-items .ez-table tr,
#ez-vouchers-page .ez-vouchers-expired .ez-table tr {
    background:#fff;
    border-bottom:1px solid #c2c2c2
}
#ez-vouchers-page .ez-vouchers-items .ez-table td,
#ez-vouchers-page .ez-vouchers-expired .ez-table td {
    padDing:0 8px
}
#ez-vouchers-page .ez-vouchers-items .ez-table .ez-table-voucher .ez-mobile,
#ez-vouchers-page .ez-vouchers-expired .ez-table .ez-table-voucher .ez-mobile {
    display:none
}
#ez-vouchers-page .ez-vouchers-items .ez-table .ez-table-voucher .ez-new-container,
#ez-vouchers-page .ez-vouchers-expired .ez-table .ez-table-voucher .ez-new-container {
    float:left;
    margin:6px 0 0 16px
}
#ez-vouchers-page .ez-vouchers-items .ez-table span,
#ez-vouchers-page .ez-vouchers-expired .ez-table span {
    text-transform:none
}
#ez-vouchers-page .ez-vouchers-items .ez-table .ez-radio-btn,
#ez-vouchers-page .ez-vouchers-expired .ez-table .ez-radio-btn {
    width:20px;
    height:20px;
    display:inline-block;
    text-align:center;
    vertical-align:middle;
    background:#8a9597;
    border-radius:50%
}
#ez-vouchers-page .ez-vouchers-items .ez-table .ez-radio-btn.ez-checked span,
#ez-vouchers-page .ez-vouchers-expired .ez-table .ez-radio-btn.ez-checked span {
    display:inline-block
}
#ez-vouchers-page .ez-vouchers-items .ez-table .ez-radio-btn span,
#ez-vouchers-page .ez-vouchers-expired .ez-table .ez-radio-btn span {
    width:8px;
    height:8px;
    background:#fff;
    display:none;
    border-radius:50%;
    vertical-align:top;
    margin-top:6px
}
#ez-vouchers-page .ez-vouchers-items .ez-table .ez-new-container,
#ez-vouchers-page .ez-vouchers-expired .ez-table .ez-new-container {
    height:44px;
    position:relative
}
#ez-vouchers-page .ez-vouchers-items .ez-table .fa-certificate,
#ez-vouchers-page .ez-vouchers-expired .ez-table .fa-certificate {
    font-size:44px;
    color:#33c541
}
#ez-vouchers-page .ez-vouchers-items .ez-table .ez-star-text,
#ez-vouchers-page .ez-vouchers-expired .ez-table .ez-star-text {
    color:#fff;
    font-size:15px;
    font-weight:300;
    position:absolute;
    left:7px;
    top:1px;
    -webkit-transform:rotate(-30deg);
    -moz-transform:rotate(-30deg);
    -ms-transform:rotate(-30deg);
    -o-transform:rotate(-30deg);
    -webkit-transform-origin:0 50%;
    -moz-transform-origin:0 50%;
    -o-transform-origin:top left;
    -ms-transform-origin:top left
}
#ez-vouchers-page .ez-vouchers-items .ez-table .ez-table-selector,
#ez-vouchers-page .ez-vouchers-expired .ez-table .ez-table-selector {
    padDing-left:0;
    text-align:center;
    font-size:24px
}
#ez-vouchers-page .ez-vouchers-items .ez-table .ez-table-selector .fa-smile-o,
#ez-vouchers-page .ez-vouchers-expired .ez-table .ez-table-selector .fa-smile-o {
    color:#33c541
}
#ez-vouchers-page .ez-vouchers-items .ez-table .ez-table-selector .fa-frown-o,
#ez-vouchers-page .ez-vouchers-expired .ez-table .ez-table-selector .fa-frown-o {
    color:#ac0000
}
#ez-vouchers-page .ez-vouchers-items .ez-table .ez-table-usewith .ez-new-container,
#ez-vouchers-page .ez-vouchers-expired .ez-table .ez-table-usewith .ez-new-container {
    float:right;
    line-height:55px
}
#ez-vouchers-page .ez-vouchers-items .ez-table td.ez-table-status,
#ez-vouchers-page .ez-vouchers-items .ez-table td.ez-table-voucher p,
#ez-vouchers-page .ez-vouchers-expired .ez-table td.ez-table-status,
#ez-vouchers-page .ez-vouchers-expired .ez-table td.ez-table-voucher p {
    color:#001a6e
}
#ez-vouchers-page .ez-vouchers-items .ez-table .ez-voucher-expires,
#ez-vouchers-page .ez-vouchers-expired .ez-table .ez-voucher-expires {
    color:#ac0000!important
}
#ez-vouchers-page .ez-vouchers-items .ez-table .ez-vouchers-promotion,
#ez-vouchers-page .ez-vouchers-expired .ez-table .ez-vouchers-promotion {
    font-size:18px;
    float:left
}
#ez-vouchers-page .ez-vouchers-items .ez-table .ez-selected,
#ez-vouchers-page .ez-vouchers-expired .ez-table .ez-selected {
    background-color:rgba(51,
    197,
    65,
    .2)
}
#ez-vouchers-page .ez-vouchers-items .ez-table .ez-new-voucher td,
#ez-vouchers-page .ez-vouchers-expired .ez-table .ez-new-voucher td {
    font-weight:bold
}
#ez-payments-wrap .ez-main-wrap {
    min-height:400px
}
#ez-payments-wrap .ez-auth-pages {
    width:540px
}
#ez-payments-wrap .ez-topup-carousel .ez-background-img,
#ez-payments-wrap .ez-topup-carousel .ez-background-img.ez-mastercard,
#ez-payments-wrap #ez-last-used .ez-background-img.ez-mastercard {
    background-position:-73px 0
}
#ez-payments-wrap .ez-topup-carousel .ez-background-img.ez-diners,
#ez-payments-wrap #ez-last-used .ez-background-img.ez-diners {
    background-position:-146px 0
}
#ez-payments-wrap .ez-topup-carousel .ez-background-img.ez-discover,
#ez-payments-wrap #ez-last-used .ez-background-img.ez-discover {
    background-position:-219px 0
}
#ez-payments-wrap .ez-topup-carousel .ez-background-img.ez-amex,
#ez-payments-wrap #ez-last-used .ez-background-img.ez-amex {
    background-position:0 -39px
}
#ez-payments-wrap .ez-topup-carousel .ez-background-img.ez-unionpay,
#ez-payments-wrap #ez-last-used .ez-background-img.ez-unionpay {
    background-position:-73px -39px
}
#ez-payments-wrap .ez-topup-carousel .ez-background-img.ez-safetypay,
#ez-payments-wrap #ez-last-used .ez-background-img.ez-safetypay {
    background-position:-146px -39px
}
#ez-payments-wrap .ez-topup-carousel .ez-background-img.ez-ideal,
#ez-payments-wrap #ez-last-used .ez-background-img.ez-ideal {
    background-position:-219px -39px
}
#ez-payments-wrap .ez-topup-carousel .ez-background-img.ez-giropay,
#ez-payments-wrap #ez-last-used .ez-background-img.ez-giropay {
    background-position:0 -78px
}
#ez-payments-wrap .ez-topup-carousel .ez-background-img.ez-inerac,
#ez-payments-wrap #ez-last-used .ez-background-img.ez-inerac {
    background-position:-73px -78px
}
#ez-payments-wrap .ez-saved-card p {
    text-align:left;
    padDing-left:6px;
    padDing-right:6px
}
#ez-payments-wrap .ez-saved-card p span {
    float:right
}
#ez-expiry-cvv li {
    width:50%;
    float:left;
    position:relative
}
#ez-expiry-cvv li#ez-card-expiry-date input {
    border-right:1px solid #c2c2c2;
    border-right-color:transparent;
    border-top-right-radius:0;
    border-bottom-right-radius:0
}
#ez-expiry-cvv li.ez-card-cvv input {
    border-top-left-radius:0;
    border-bottom-left-radius:0
}
#ez-expiry-cvv li.ez-no-expiry {
    width:100%
}
#ez-expiry-cvv li.ez-no-expiry input {
    border-top-left-radius:4px!important;
    border-bottom-left-radius:4px!important
}
#ez-card-details {
    height:186px
}
#ez-card-details>div:first-child {
    height:106px
}
#ez-card-details>div:first-child div {
    background:#fff
}
#ez-billing-details {
    margin-bottom:34px
}
#ez-billing-details div {
    position:relative
}
#ez-billing-details input[type=text],
#ez-billing-details input[type=email],
#ez-billing-details .ez-select-wrap {
    margin-top:-1px;
    border-radius:0
}
#ez-billing-details input[type=text]:focus,
#ez-billing-details input[type=text].ez-error,
#ez-billing-details input[type=text].ng-focused,
#ez-billing-details input[type=email]:focus,
#ez-billing-details input[type=email].ez-error,
#ez-billing-details input[type=email].ng-focused,
#ez-billing-details .ez-select-wrap:focus,
#ez-billing-details .ez-select-wrap.ez-error,
#ez-billing-details .ez-select-wrap.ng-focused {
    position:relative;
    z-index:3
}
#ez-billing-details #CreditCardNameOnCard {
    margin-top:0;
    border-top-left-radius:4px;
    border-top-right-radius:4px
}
#ez-billing-details #CreditCardZipCode {
    border-bottom-left-radius:4px;
    border-bottom-right-radius:4px
}
#ez-billing-details em.ez-error {
    margin:0;
    display:block;
    font-size:20px;
    line-height:48px;
    position:absolute;
    top:1px;
    bottom:1px;
    left:10px;
    z-index:3;
    background:#fff
}
body.ar #ez-billing-details em.ez-error,
body.ur #ez-billing-details em.ez-error {
    left:initial;
    right:10px
}
.ez-checkbox-wrap .okicon- {
    top:2px;
    line-height:10px
}
.ez-select-wrap .angle-downicon- {
    font-size:26px!important
}
.ez-pay-button {
    margin-top:20px
}
#ez-stored-card-details {
    height:97px
}
#ez-stored-card-details .trash-emptyicon- {
    width:44px;
    text-align:center;
    float:right;
    font-size:26px;
    margin:-10px -6px -8px 0;
    color:inherit
}
#ez-stored-card-details .trash-emptyicon-:hover {
    color:#005bac
}
body.ar #ez-stored-card-details .trash-emptyicon-,
body.ur #ez-stored-card-details .trash-emptyicon- {
    float:left;
    margin-right:0;
    margin-left:-6px
}
body.ar #ez-last-used p,
body.ur #ez-last-used p {
    text-align:right
}
#ez-last-used {
    margin-bottom:14px
}
#ez-last-used .ez-img-wrap {
    width:120px;
    height:56px;
    text-align:center;
    padDing-top:11px;
    border:1px solid #c2c2c2;
    float:left;
    margin-right:10px;
    background:#fff
}
#ez-last-used .ez-img-wrap span {
    width:68px;
    height:34px;
    display:inline-block
}
#ez-last-used p {
    font-size:26px;
    line-height:28px;
    color:#001a6e;
    direction:ltr
}
#ez-last-used a {
    margin-top:-8px
}
#ez-confirm-delete-card .ez-modal-content {
    text-align:center
}
#ez-confirm-delete-card .fa-trash-o {
    font-size:50px;
    margin-bottom:6px
}
#ez-confirm-delete-card .ez-card-details {
    color:#001a6e;
    font-size:21px
}
#ez-confirm-delete-card .ez-card-text {
    margin-top:20px
}
#ez-confirm-delete-card .ez-card-text span {
    color:#ff3dd9
}
#ez-amex-modal {
    background:#fff;
    padDing-bottom:10px;
    margin-bottom:40px;
    overflow:hidden;
    border-radius:4px
}
#ez-amex-modal iframe {
    height:780px
}
#modalInnerHeader {
    background:#eeedef;
    padDing:14px 20px;
    margin-bottom:20px
}
#modalInnerHeader .DingLogo {
    width:80px;
    height:33px;
    float:left;
    margin-top:-2px;
    background:url(../img/logo-dark.svg) 0 0 no-repeat;
    background-size:80px 33px
}
#modalInnerHeader h3 {
    font-weight:bold;
    text-align:center
}
#closeDialogLink {
    float:right;
    padDing:10px;
    margin:-11px -20px 0 0;
    line-height:normal
}
#closeDialogLink span {
    width:24px;
    height:24px;
    background:#eeedef;
    color:#8a9597;
    font-size:28px;
    text-align:center
}
#DingTip {
    font-style:italic;
    margin-bottom:18px;
    line-height:22px
}
#stageheader,
#displayAmount,
#card\.cclogoTr,
#nextstep,
#card\.expiryContainer,
#mainBack {
    display:none
}
#container {
    padDing-top:10px
}
#container li>.imgB {
    display:none
}
#container .popupMsgOPP {
    position:relative;
    top:-155px;
    margin-left:0;
    z-index:10
}
#container .popupMsg {
    display:none;
    border:1px solid #666;
    background-color:#eee;
    margin-bottom:5px;
    margin-right:10px;
    margin-left:10px;
    padDing:6px
}
.basetable {
    width:100%
}
.basetable tr td:first-child {
    display:none
}
.basetable .fieldDiv {
    position:relative
}
.basetable #cvv-button {
    width:42px;
    height:27px;
    display:block;
    position:absolute;
    top:10px;
    right:6px;
    z-index:10;
    background:url(../img/cvv.png) 0 0 no-repeat;
    background-size:42px 27px
}
.basetable .ez-two-fields {
    position:relative
}
.basetable .ez-two-fields .ez-input-icon-wrap {
    width:50%;
    float:left
}
.basetable .ez-two-fields a {
    display:none
}
.basetable input[type=text]:focus,
.basetable input[type=number]:focus,
.basetable .errorField,
.basetable .inputField.ez-error {
    position:relative;
    z-index:3
}
.basetable .ez-select-wrap span {
    max-width:88%;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis
}
.basetable .errorField {
    border-color:#ac0000
}
#card\.cardNumberTr .fieldDiv {
    height:106px
}
.ez-two-fields .fieldDiv {
    height:80px
}
#cardExpiryInput {
    border-top-right-radius:0;
    border-bottom-right-radius:0
}
#card\.cvcCode {
    border-top-left-radius:0;
    border-bottom-left-radius:0
}
#card\.billingAddress\.houseNumberOrName,
#card\.billingAddress\.street,
#card\.billingAddress\.city,
#card\.billingAddress\.stateOrProvince,
.basetable .ez-select-wrap {
    margin-top:-1px;
    border-radius:0
}
#card\.cardHolderName {
    border-bottom-left-radius:0;
    border-bottom-right-radius:0
}
#card\.billingAddress\.postalCode {
    margin-top:-1px;
    border-top-left-radius:0;
    border-top-right-radius:0
}
#mainSubmit {
    width:100%;
    margin-top:30px
}
#mainBack {
    float:right
}
#cardExpiryInput-error {
    width:50%;
    padDing-right:8px;
    position:absolute;
    top:50px
}
#card\.cvcCode-error {
    width:50%;
    float:right
}
#card\.cardHolderName-error,
#card\.billingAddress\.houseNumberOrName-error,
#card\.billingAddress\.street-error,
#card\.billingAddress\.city-error,
#card\.billingAddress\.postalCode-error {
    margin:0;
    display:block;
    font-size:20px;
    line-height:48px;
    position:absolute;
    bottom:1px;
    left:10px;
    z-index:3;
    background:#fff
}
body.ur .ez-error,
body.ar .ez-error {
    text-align:right
}
body.ur #card\.cardHolderName-error,
body.ur #card\.billingAddress\.houseNumberOrName-error,
body.ur #card\.billingAddress\.street-error,
body.ur #card\.billingAddress\.city-error,
body.ur #card\.billingAddress\.postalCode-error,
body.ar #card\.cardHolderName-error,
body.ar #card\.billingAddress\.houseNumberOrName-error,
body.ar #card\.billingAddress\.street-error,
body.ar #card\.billingAddress\.city-error,
body.ar #card\.billingAddress\.postalCode-error {
    left:initial;
    right:10px
}
body.ur #cardExpiryInput-error,
body.ar #cardExpiryInput-error {
    left:0
}
#ez-payment-response-page>h2 {
    font-size:22px;
    margin-bottom:10px
}
#ez-payment-response-page .ez-alert-box {
    display:block
}
#ez-payment-response-page #ez-guest-message {
    padDing:10px 0
}
#ez-payment-response-page #ez-guest-message div {
    color:#000
}
#ez-payment-response-page #ez-guest-message div:first-child {
    color:#ac0000;
    font-weight:bold
}
#ez-payment-response-page .ez-topup-btns {
    margin-top:30px
}
body.ar,
body.ur {
    direction:rtl;
    text-align:right
}
body.ar .basetable .fieldDiv,
body.ur .basetable .fieldDiv {
    text-align:right
}
#ez-inapp-page {
    padDing:0 20px 10px 20px
}
#ez-inapp-page * {
    color:#4d626c
}
#ez-inapp-page a {
    color:#005bac
}
#ez-inapp-page ul {
    margin-bottom:20px
}
#ez-inapp-page h4 {
    font-size:16px;
    line-height:22px;
    margin-bottom:8px
}
#ez-inapp-page h4 a {
    margin:-12px 0
}
#ez-inapp-page h4.ez-upper {
    text-transform:uppercase
}
#ez-inapp-page h4.ez-margin {
    margin-top:40px
}
#ez-inapp-page p {
    margin-bottom:20px
}
#ez-inapp-page p,
#ez-inapp-page .ez-accordiontext,
#ez-inapp-page li {
    font-size:14px!important;
    line-height:20px
}
#ez-inapp-page p a,
#ez-inapp-page .ez-accordiontext a,
#ez-inapp-page li a {
    line-height:inherit
}
#ez-inapp-page .ez-accordion:last-child .ez-accordionitem:last-child {
    border-bottom:none
}
#ez-inapp-page .ez-accordionitem {
    border-bottom:1px solid #ddd
}
#ez-inapp-page .ez-accordionitem .fa {
    font-size:22px
}
#ez-inapp-page .ez-accordionitem.ez-active .fa {
    color:#00b259
}
#ez-inapp-page .ez-accordiontitle {
    padDing:16px 30px 16px 0;
    font-size:14px;
    line-height:16px;
    font-weight:bold
}
#ez-inapp-page .ez-accordiontitle.ez-active {
    color:#00b259!important
}
#ez-inapp-page .ez-accordiontext {
    padDing:4px 30px 20px 0;
    background-color:transparent
}
@media all and (min-width: 1601px) {
    .ez-wrap {
        padDing-left:10%;
        padDing-right:10%
    }
    #ez-promo-countries-wrap {
        padDing-left:10%;
        padDing-right:10%
    }
    .ez-wrap-margin-right {
        margin-right:16%
    }
    .ez-wrap-margin-left {
        margin-left:16%
    }
}
@media all and (max-width: 1400px) {
    #ez-account-tabs .ez-button {
        min-width:110px
    }
    #registerRAF #ez-left-side {
        width:570px;
        margin-right:80px
    }
    #registerRAF #ez-right-side {
        width:400px
    }
    #ez-ild-rates-page #ez-ild-rates-panel .ez-ild-rates-row {
        margin-left:25px;
        width:auto;
        padDing-top:27px
    }
    #ez-ild-rates-page #ez-ild-rates-panel .ez-rates-icon {
        width:80px;
        height:80px;
        -webkit-border-radius:40px;
        -moz-border-radius:40px;
        border-radius:40px
    }
    #ez-ild-rates-page #ez-ild-rates-panel .ez-rates-icon em {
        font-size:46px;
        line-height:80px;
        color:#fff
    }
    #ez-ild-rates-page #ez-ild-rates-panel span.ez-rateValue {
        font-size:48px
    }
    #ez-ild-rates-page #ez-ild-rates-panel span.ez-rateFixedText {
        font-size:23px
    }
}
@media all and (max-width: 1250px) {
    #ez-right-content.ez-topup-wrap {
        width:46%
    }
    #ez-subscribe-newsletter h3 {
        font-size:20px
    }
    #ez-subscribe-newsletter h4 {
        font-size:16px;
        margin-bottom:20px
    }
    #ez-subscribe-newsletter span {
        font-size:16px
    }
    div#ez-payment-response-page #right-side p.ez-subtitle {
        font-size:20px
    }
    #registerRAF #ez-left-side {
        width:50%;
        margin-right:0
    }
    #registerRAF #ez-right-side {
        width:44%;
        float:right
    }
    #registerRAF #ez-right-side .subtitle {
        font-size:15px;
        margin-bottom:16px
    }
    #registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-content .ez-commercial-box .ez-content {
        font-size:16px
    }
    #registerRAF .ez-registration-footer .ez-community {
        margin-top:30px
    }
    #registerRAF .ez-registration-footer .ez-community .ez-title {
        font-size:28px
    }
    #ez-my-profile #ez-account-tabs>li {
        height:initial;
        min-height:214px
    }
    #ez-my-profile #ez-account-tabs .ez-my-profile-info {
        margin-bottom:36px
    }
    #ez-my-profile #ez-account-tabs .ez-my-profile-img {
        width:70px;
        height:70px;
        margin-top:0
    }
    #ez-my-profile #ez-account-tabs .ez-my-profile-img span {
        font-size:60px;
        bottom:-6px;
        right:16px
    }
    #ez-my-profile #ez-account-tabs h3.ez-margin-bottom {
        display:none
    }
}
@media all and (max-width: 1100px) {
    .ez-hideUnder1100 {
        display:none!important
    }
    #ez-cms-content .ez-content-halfscreen {
        width:100%
    }
    .ez-wrap {
        padDing-left:6%;
        padDing-right:6%
    }
    .ez-wrap-margin-right {
        margin-right:6%
    }
    .ez-wrap-margin-left {
        margin-left:6%
    }
    #ez-right-content.ez-topup-wrap {
        width:44%
    }
    header nav>ul>li>a {
        font-size:16px
    }
    header nav #ez-menu-list>li {
        padDing:0 7px
    }
    body.ta header nav>ul>li>a {
        font-size:15px
    }
    .ez-list li.ez-list-item {
        width:23%;
        margin-left:2.6%
    }
    .ez-list li.ez-list-item.ez-five-items-row {
        margin-left:2.6%;
        clear:none
    }
    .ez-list li.ez-list-item.ez-four-items-row {
        margin-left:0;
        clear:both
    }
    footer {
        margin:0 auto
    }
    footer .ez-footer-accordion ul {
        display:block;
        float:left;
        padDing:0 0 40px 0;
        width:28%;
        margin-left:4%
    }
    footer .ez-footer-accordion ul:nth-child(4) {
        clear:both
    }
    footer .language-container {
        width:25%
    }
    #ez-footer-security li:first-child {
        display:none
    }
    #ez-account-menu li {
        min-width:initial;
        float:none
    }
    #ez-account-menu span.fa-circle {
        right:-3px;
        margin-left:0
    }
    .ez-account-first-hide {
        display:none
    }
    #ez-account-title {
        height:66px
    }
    #ez-account-main-action {
        width:49%
    }
    #ez-overview-all {
        width:49%
    }
    #ez-overview-all li {
        width:49%
    }
    #ez-account-tabs>li {
        width:49%
    }
    #ez-contacts-wrap .ez-table td a {
        margin-left:15px
    }
    #ez-contacts-wrap .ez-table td .ez-button {
        min-width:110px
    }
    #ez-promo-countries-wrap .ez-list li.ez-list-item {
        width:21%;
        margin-left:1.3%
    }
    #ez-promo-countries-wrap .ez-list li.ez-list-item.ez-five-items-row {
        margin-left:1.3%;
        clear:none
    }
    #ez-promo-countries-wrap .ez-list li.ez-list-item.ez-four-items-row {
        margin-left:6%;
        clear:both
    }
    #ez-support-chat {
        width:32%
    }
    #ez-support-trust-panel {
        width:66.4%
    }
    #ez-support-content p {
        padDing-right:20%
    }
    #ez-support-content .ez-accordion .ez-accordiontext {
        padDing-right:20%
    }
    #ez-lanDing-trust-panel,
    #ez-lanDing-prmotion,
    .ez-country-content,
    .ez-country-content-two {
        width:49%
    }
    #ez-lanDing-trust-panel .ez-mobile-hide.ez-trust-h,
    #ez-lanDing-prmotion .ez-mobile-hide.ez-trust-h {
        display:none!important
    }
    #ez-lanDing-trust-panel .ez-desktop-hide,
    #ez-lanDing-prmotion .ez-desktop-hide {
        display:block!important
    }
    #ez-lanDing-prmotion h2 {
        margin-right:14%
    }
    #ez-lanDing-trust-panel h2 {
        font-size:34px
    }
    #ez-side-text {
        width:42%
    }
    #ez-suggested-articles {
        width:52%
    }
    #ez-subscribe-newsletter h3 {
        font-size:18px
    }
    #ez-subscribe-newsletter h4 {
        font-size:16px;
        margin-bottom:20px
    }
    #ez-subscribe-newsletter span {
        font-size:14px
    }
    #ez-migpage-top h1 {
        text-align:center
    }
    #ez-ild-rates-page #ez-ild-country-selectors {
        width:39%
    }
    #ez-ild-rates-page #ez-ild-rates-panel {
        width:59%;
        margin-top:30px
    }
    #ez-ild-rates-page #ez-ild-rates-panel .ez-topup-btns {
        margin-left:0
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap {
        width:100%;
        text-align:center
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap ul {
        padDing-top:8px;
        margin-left:10px
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap .ez-ild-rates-row {
        float:none;
        display:inline-block;
        margin-left:0;
        padDing-top:0
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap .ez-rates-fixed {
        margin-right:0
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap .ez-rates-icon {
        width:65px;
        height:65px;
        margin-right:0;
        margin-bottom:0
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap .ez-rates-icon em {
        line-height:65px
    }
    #ez-payment-response-page #left-side .ez-success-box {
        font-size:16px
    }
    #registerRAF #ez-right-side {
        margin-top:16px
    }
    #registerRAF #ez-right-side .title {
        font-size:28px;
        line-height:28px
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-content,
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps div.ez-step .ez-content {
        font-size:16px;
        line-height:20px
    }
    #registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-content .ez-commercial-box .ez-content {
        font-size:12px;
        line-height:18px
    }
    #registerRAF .ez-registration-footer .ez-community {
        margin-top:30px
    }
    #registerRAF .ez-registration-footer .ez-community .ez-title {
        font-size:24px
    }
    #ez-careers-wrap .ez-careers-wrap {
        width:100%;
        padDing:44px 26px
    }
    #ez-careers-wrap p {
        font-size:17px
    }
    #ez-careers-wrap .ez-careers-article li {
        width:52%
    }
    #ez-careers-wrap .ez-careers-article .ez-img {
        width:48%
    }
    #ez-careers-poster p {
        font-size:30px;
        line-height:34px
    }
    .ez-replace-main-box {
        display:block!important
    }
    .ez-two-in-one {
        display:block!important
    }
    .ez-two-in-one span {
        margin-top:5px!important;
        margin-bottom:0!important;
        font-size:18px!important;
        font-weight:normal!important
    }
    .ez-two-in-one h3 {
        font-size:20px!important;
        font-weight:bold;
        padDing-top:0!important;
        margin-bottom:5px!important
    }
}
@media all and (max-width: 960px) {
    #ez-right-content.ez-topup-wrap {
        width:44%
    }
    header {
        height:74px
    }
    header #ez-logo-wrap {
        margin-top:13px
    }
	
    header nav {
        margin-top:13px
    }
    header nav a {
        font-size:16px
    }
    header nav>ul>li>a {
        font-size:13px
    }
    header nav #ez-menu-list>li {
        padDing:0 6px
    }
    header .ez-drop-down-holder #ez-account-menu {
        max-width:130px
    }
	 header #cobrand {
        margin-top:13px
    }
    body.pt header nav>ul>li>a,
    body.ru header nav>ul>li>a,
    body.de header nav>ul>li>a {
        font-size:13px
    }
    body.ta header nav>ul>li>a {
        font-size:12px
    }
    body.ta header nav>ul>li>a {
        font-size:13px
    }
    body.ta header nav li {
        padDing:0 10px
    }
    #ez-send-topup .ez-asterisk.ez-svg {
        width:700px;
        margin:0 -190px -340px
    }
    #ez-cms-content #ez-lanDing-trust-panel h2 {
        font-size:32px
    }
    #ez-home-operators ul li {
        width:22%
    }
    #ez-home-operators ul li img.ez-digicel-logo {
        height:42px;
        margin-top:52px
    }
    #ez-home-operators ul li img.ez-cubacel-logo {
        height:32px;
        margin-top:52px
    }
    .ez-version-a #ez-send-topup h1,
    .ez-version-b #ez-send-topup h1 {
        font-size:58px;
        line-height:48px
    }
    .ez-version-a #ez-send-topup p,
    .ez-version-b #ez-send-topup p {
        font-size:28px
    }
    #ez-careers-movie {
        height:520px
    }
    #ez-careers-movie iframe {
        height:520px
    }
    #ez-careers-poster #ez-video-btn {
        font-size:40px
    }
    .ez-topup-carousel-item span.ez-amount {
        font-size:22px
    }
    #ez-support-chat li span {
        font-size:16px
    }
    #ez-migpage-middle li {
        width:31%;
        margin-left:2%
    }
    #ez-migpage-middle li:first-child {
        margin-left:0
    }
    #ez-migpage-middle br {
        display:none
    }
    #ez-vouchers-page .ez-no-vouchers-invite h2 {
        font-size:28px;
        margin-bottom:4px
    }
    #ez-vouchers-page .ez-no-vouchers-invite p {
        font-size:15px;
        line-height:20px;
        margin-bottom:14px
    }
    body.ta #registerRAF #ez-signup-with-email .fa {
        display:none
    }
    #registerRAF #ez-left-side {
        width:49%
    }
    #registerRAF #ez-right-side {
        width:47%
    }
    #registerRAF #ez-right-side .title {
        font-size:24px
    }
    #registerRAF #ez-right-side .subtitle {
        font-size:14px
    }
    #registerRAF #ez-fb-login-separator {
        margin:-8px 0 -14px 0
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-title,
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps div.ez-step .ez-title {
        font-size:18px
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-content,
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps div.ez-step .ez-content {
        font-size:12px
    }
    #registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-content .ez-commercial-box .ez-content {
        font-size:15px
    }
    #ez-payment-response-page #right-side .ez-subtitle {
        font-size:18px
    }
    .ez-invite-social a {
        height:44px;
        line-height:44px
    }
    .ez-invite-social a span {
        font-size:30px
    }
    #ez-carrers-nav {
        height:100px;
        margin-top:-100px
    }
    #ez-carrers-nav nav {
        width:100%;
        height:100%;
        margin-left:0
    }
    #ez-ild-rates-page #ez-ild-rates-panel {
        width:55%;
        margin-top:0
    }
    #ez-careers-wrap .ez-careers-article li:first-child {
        width:42%
    }
    #ez-careers-wrap .ez-careers-article li img {
        width:100%;
        height:auto
    }
    #ez-subscribe-newsletter .ez-error-desktop {
        display:none
    }
    #ez-subscribe-newsletter .ez-error-mobile {
        display:block
    }
    #ez-subscribe-newsletter h3 {
        font-size:18px
    }
    #ez-subscribe-newsletter div {
        margin-bottom:10px
    }
    #ez-subscribe-newsletter div input {
        width:100%
    }
    #ez-subscribe-newsletter div button {
        width:100%;
        margin-left:0;
        margin-top:10px
    }
    #ez-subscribe-newsletter span {
        font-size:16px
    }
    #ez-paypal-link.ez-three-btns {
        margin-right:24%
    }
}
@media all and (max-width: 860px) {
    .ez-wrap {
        padDing-left:4%;
        padDing-right:4%
    }
    .ez-wrap-margin-right {
        margin-right:4%
    }
    .ez-wrap-margin-left {
        margin-left:4%
    }
    header nav #ez-menu-list>li {
        padDing:0 3px
    }
    header nav #ez-menu-list>li>a {
        padDing-left:2px;
        padDing-right:2px
    }
    #ez-left-content,
    #ez-right-content {
        width:100%!important;
        float:none
    }
    #ez-snippet-footersecuritylogos {
        padDing-top:24px
    }
    footer {
        padDing-bottom:12px
    }
    footer #ez-footer-cards {
        width:100%;
        padDing:0 30px 20px 30px;
        text-align:center;
        float:none
    }
    footer #ez-footer-security {
        width:100%;
        text-align:center;
        float:none
    }
    footer #ez-footer-security li.ez-trusted {
        padDing-top:4px
    }
    footer #footer-terms {
        width:100%;
        padDing-bottom:0;
        text-align:center;
        margin-top:9px
    }
    footer #footer-terms li {
        display:inline-block;
        float:none
    }
    .ez-table th .fa {
        display:none
    }
    .ez-table th.ez-table-status {
        width:10%
    }
    .ez-table td {
        font-size:14px
    }
    .ez-table td .ez-button {
        min-width:initial
    }
    .ez-table td a {
        margin-left:20px
    }
    #ez-cookies-table li {
        font-size:14px
    }
    .ez-list li.ez-list-item {
        width:24%;
        margin-left:1.3%
    }
    .ez-list li.ez-list-item.ez-five-items-row {
        margin-left:1.3%
    }
    .ez-list li.ez-list-item.ez-four-items-row {
        margin-left:0;
        clear:none
    }
    #ez-promo-countries-wrap .ez-list li.ez-list-item {
        width:22%;
        margin-left:1.3%
    }
    #ez-promo-countries-wrap .ez-list li.ez-list-item.ez-five-items-row {
        margin-left:1.3%
    }
    #ez-promo-countries-wrap .ez-list li.ez-list-item.ez-four-items-row {
        margin-left:4%;
        clear:none
    }
    #ez-support-chat li span {
        font-size:14px
    }
    #ez-support-chat .ez-icon {
        margin-right:4px
    }
    #ez-support-content p {
        padDing-right:0
    }
    #ez-support-content .ez-accordion .ez-accordiontext {
        padDing-right:20px;
        text-align:left
    }
    .ez-vouchers-items .ez-vouchers-promotion,
    .ez-vouchers-expired .ez-vouchers-promotion {
        font-size:16px!important
    }
    #invalid-referral-modal {
        width:520px
    }
    #invalid-referral-modal .ez-modal .ez-modal-header h3 {
        font-size:50px
    }
    #invalid-referral-modal .ez-modal .ez-modal-header p {
        font-size:26px;
        height:24px
    }
    #invalid-referral-modal .ez-modal .ez-modal-content h2 {
        font-size:28px
    }
    #invalid-referral-modal .ez-modal .ez-modal-content p {
        font-size:16px
    }
    #invalid-referral-modal .ez-modal .ez-modal-footer button {
        font-size:26px
    }
    #registerRAF #ez-right-side .title {
        margin-bottom:10px
    }
    #registerRAF #ez-right-side .subtitle {
        display:none
    }
    #registerRAF #ez-signup-with-email,
    #registerRAF #ez-facebook-link button {
        font-size:18px
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-number,
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step .ez-number {
        font-size:100px;
        line-height:94px
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-title,
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step .ez-title {
        margin-bottom:2px;
        margin-left:60px
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-content,
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step .ez-content {
        margin-left:60px
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-title {
        margin-left:40px
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-content {
        margin-left:40px
    }
    #registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-content .ez-commercial-box {
        width:50%;
        margin-bottom:30px
    }
    #registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-content .ez-commercial-box .ez-commercial-box-image span.fa {
        width:56px;
        height:56px;
        line-height:56px
    }
    #registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-content .ez-commercial-box .ez-title {
        margin-bottom:8px
    }
    div#ez-payment-response-page #right-side p.ez-subtitle {
        font-size:22px;
        width:220px;
        line-height:25px;
        margin-left:auto;
        margin-right:auto
    }
    div#ez-payment-response-page #right-side p.ez-action-text {
        margin-top:10px
    }
    .ez-invite-social a {
        width:48%!important
    }
    .ez-invite-social a.ez-google {
        margin-left:0
    }
    #ez-lanDing-trust-panel,
    #ez-lanDing-prmotion,
    .ez-country-content,
    .ez-country-content-two,
    #ez-lanDing-promo-text {
        width:100%;
        float:none
    }
    .ez-lanDing-two .ez-country-content {
        min-height:initial
    }
    .ez-country-content-topup .ez-topup-btn {
        margin-top:10px;
        position:static
    }
    #ez-lanDing-promo-text p br {
        display:none
    }
    #ez-lanDing-trust-panel {
        border-top:0;
        border-bottom:4px solid #33c541
    }
    #ez-lanDing-trust-panel {
        padDing-top:20px
    }
    #ez-lanDing-prmotion h2 {
        margin:30px
    }
    #ez-lanDing-prmotion p {
        padDing-left:30px
    }
    #ez-suggested-articles,
    #ez-side-text {
        width:100%;
        float:none
    }
    #ez-side-text {
        margin-top:40px
    }
    #ez-localposts-container .ez-post-image {
        width:32%
    }
    #ez-localposts-container .ez-post-image img {
        margin-left:-6%
    }
    #ez-subscribe-newsletter {
        width:100%
    }
    #ez-subscribe-newsletter h3 {
        font-size:20px
    }
    #ez-subscribe-newsletter h4 {
        font-size:16px;
        margin-bottom:20px;
        margin-top:-12px
    }
    #ez-subscribe-newsletter div {
        margin-bottom:10px
    }
    #ez-subscribe-newsletter div input {
        width:100%
    }
    #ez-subscribe-newsletter div button {
        width:100%;
        margin-left:0;
        margin-top:10px
    }
    #ez-subscribe-newsletter span {
        font-size:16px
    }
}
@media all and (max-width: 800px) {
    .ez-background-asterisk {
        width:720px;
        height:720px;
        margin:0 -170px -350px
    }
    div#ez-payment-response-page div#right-side div.ez-link {
        white-space:normal
    }
    div#ez-payment-response-page div#right-side div.ez-link input {
        width:100%;
        margin-left:-.2%
    }
    div#ez-payment-response-page div#right-side div.ez-link a {
        width:100%;
        border-bottom-left-radius:4px;
        border-top-left-radius:4px;
        -webkit-border-bottom-left-radius:4px;
        -webkit-border-top-left-radius:4px;
        -moz-border-bottom-left-radius:4px;
        -moz-border-top-left-radius:4px
    }
    #ez-careers-movie {
        height:420px
    }
    #ez-careers-movie iframe {
        height:420px
    }
    #ez-careers-wrap .ez-careers-big-text {
        font-size:22px;
        line-height:26px
    }
    #ez-careers-wrap .ez-careers-quote p {
        font-size:21px;
        line-height:24px
    }
    #ez-careers-wrap .ez-careers-quote p br,
    #ez-careers-wrap .ez-careers-big-text br {
        display:none
    }
    #ez-careers-wrap .ez-careers-big-text {
        text-align:left
    }
}
@media all and (max-width: 767px) {
    h1 {
        font-size:40px;
        line-height:46px
    }
    h2,
    #registerRAF .ez-registration-footer .ez-programme .ez-programme-title,
    #registerRAF .ez-registration-footer .ez-whyDing #ez-snippet-registration-whychooseDing .ez-Ding-commercial>.ez-title {
        font-size:30px;
        line-height:36px
    }
    h3,
    #registerRAF .ez-registration-footer .ez-trusted {
        font-size:26px;
        line-height:32px
    }
    button,
    input[type=submit],
    .ez-button,
    #ez-ild-add-credit span {
        font-size:20px
    }
    .ez-main-wrap {
        padDing-top:20px
    }
    .ez-767-show {
        display:block!important
    }
    .ez-767-hide {
        display:none!important
    }
    .ez-headline {
        margin-bottom:10px
    }
    #ez-left-content,
    #ez-right-content {
        width:100%!important;
        float:none
    }
    #ez-right-content {
        padDing-bottom:20px
    }
    #ez-right-content #ez-asterisks-rain {
        display:none
    }
    .ez-edit-wrap .ez-topup-carousel {
        margin-left:-11px!important
    }
    .ez-edit-btns-wrap button,
    .ez-edit-btns-wrap .ez-button {
        margin-left:0
    }
    .ez-edit-btns-wrap button.ez-full-edit-btn,
    .ez-edit-btns-wrap .ez-button.ez-full-edit-btn {
        width:100%
    }
    .ez-edit-btns-wrap button.ez-767-float-left,
    .ez-edit-btns-wrap .ez-button.ez-767-float-left {
        float:left!important
    }
    .ez-accordion .ez-accordiontext {
        font-size:17px
    }
    .ez-big-modal {
        width:90%!important;
        left:5%!important
    }
    #ez-tc-modal-wrap td,
    #ez-tc-modal-wrap td:first-child {
        width:50%
    }
    body.ru .ez-modal button,
    body.de .ez-modal button,
    body.fr .ez-modal button,
    body.it .ez-modal button {
        font-size:16px
    }
    body.ta .ez-modal button {
        font-size:15px
    }
    .ez-tagline h3 {
        font-size:26px
    }
    .ez-topup-carousel-item {
        width:112px
    }
    .ez-topup-carousel-item span.ez-amount {
        font-size:26px
    }
    header nav {
        margin-top:17px
    }
    header nav>ul>li>a {
        font-size:16px!important
    }
    header nav .ez-nopadDing {
        padDing-right:0
    }
    header nav #ez-account-link-mobile {
        padDing-left:0!important
    }
    header nav #ez-menu-main-items {
        background:none;
        border:none;
        margin-left:0
    }
    header nav #ez-menu-main-items:hover .ez-separator {
        opacity:1;
        filter:alpha(opacity=100)
    }
    header nav #ez-menu-main-items>li:hover {
        background:transparent
    }
    header nav #ez-menu-main-items>li:hover a {
        border-bottom:1px solid #fff
    }
    header nav #ez-menu-main-items a {
        font-weight:300
    }
    body.ar header #ez-menu-main-items li,
    body.ar #div-header #ez-menu-main-items li,
    body.ur header #ez-menu-main-items li,
    body.ur #div-header #ez-menu-main-items li {
        float:right
    }
    body.ar header #ez-menu-main-items .ez-nopadDing,
    body.ar #div-header #ez-menu-main-items .ez-nopadDing,
    body.ur header #ez-menu-main-items .ez-nopadDing,
    body.ur #div-header #ez-menu-main-items .ez-nopadDing {
        padDing-left:0;
        padDing-right:6px
    }
    body.ar header #ez-menu-main-items #ez-account-link-mobile,
    body.ar #div-header #ez-menu-main-items #ez-account-link-mobile,
    body.ur header #ez-menu-main-items #ez-account-link-mobile,
    body.ur #div-header #ez-menu-main-items #ez-account-link-mobile {
        padDing-left:6px!important;
        padDing-right:0
    }
    footer .language-container {
        width:30%
    }
    .ez-table tr {
        border-bottom:1px solid #fff
    }
    .ez-table td {
        line-height:22px;
        font-size:16px;
        padDing:8px 0 8px 14px;
        max-width:215px;
        overflow:hidden;
        text-overflow:ellipsis
    }
    .ez-table td a {
        line-height:56px
    }
    .ez-table td span:first-child,
    .ez-table td .ez-table-bold {
        font-size:18px;
        font-weight:bold
    }
    .ez-table td.ez-table-status,
    .ez-table td.ez-table-resend {
        width:50px;
        text-align:center
    }
    .ez-table td.ez-table-resend {
        padDing-left:0
    }
    .ez-table td.ez-table-status,
    .ez-table td.ez-table-resend {
        background:#e7e6e8
    }
    .ez-table td .ez-flag {
        margin-top:13px;
        margin-bottom:0;
        float:left
    }
    .ez-table tr:nth-child(odd) .ez-table-status,
    .ez-table tr:nth-child(odd) .ez-table-resend {
        background:#efefef!important
    }
    .ez-table .ez-table-status .fa {
        float:none;
        margin-right:0
    }
    #ez-cookies-table li {
        width:100%!important;
        padDing-right:0!important;
        float:none;
        margin-bottom:6px;
        font-size:17px;
        line-height:22px
    }
    #ez-cookies-table li.ez-bold {
        display:none
    }
    #ez-cookies-table li:nth-child(4n+1) {
        font-weight:bold;
        font-size:19px
    }
    #ez-cookies-table li:nth-child(4n+2) {
        font-size:19px
    }
    #ez-cookies-table li:nth-child(4n+4) {
        margin-bottom:26px;
        text-align:justify
    }
    .ez-cmenu {
        border-bottom:0
    }
    .ez-cmenu li {
        width:100%;
        text-align:left;
        padDing:0 14px;
        display:none;
        border-left:0;
        border-bottom:1px solid #c6ccce
    }
    .ez-cmenu li:hover {
        background:#b4bec0
    }
    .ez-cmenu li a {
        padDing:0!important;
        line-height:44px!important
    }
    .ez-cmenu li a br {
        display:none
    }
    .ez-cmenu li.ez-active {
        font-weight:bold;
        background:#b4bec0
    }
    .ez-cmenu li#ez-cmenu-mobile-btn {
        display:block;
        border-bottom:5px solid #33c541
    }
    .ez-cmenu li#ez-cmenu-mobile-btn br {
        display:none
    }
    .ez-list li.ez-list-item {
        width:31%;
        margin-left:3.3%
    }
    .ez-list li.ez-list-item.ez-five-items-row {
        margin-left:3.3%
    }
    .ez-list li.ez-list-item.ez-four-items-row {
        margin-left:3.3%
    }
    .ez-list li.ez-list-item.ez-three-items-row {
        margin-left:0
    }
    #ez-send-topup {
        height:400px
    }
    #ez-send-topup .ez-airtime-wrap {
        width:100%
    }
    #ez-send-topup .ez-asterisk.ez-svg {
        width:640px;
        margin:0 -160px -310px
    }
    .ez-topup-carousel {
        margin:0 0 30px 0
    }
    #ez-denominations-tax {
        margin-top:-20px
    }
    #ez-account-menu span.fa-circle {
        right:-4px;
        margin-top:-40px;
        top:initial
    }
    #ez-account-main-action {
        width:60%
    }
    #ez-account-title {
        height:56px
    }
    #ez-overview-all {
        width:36%
    }
    #ez-overview-all li {
        width:100%
    }
    #ez-account-tabs>li {
        padDing:26px 14px
    }
    #ez-account-tabs>li .ez-icon-text {
        margin-left:0
    }
    #ez-account-tabs .ez-account-tab-details li {
        font-size:16px
    }
    #ez-account-tabs .ez-account-tab-details li.ez-recent-name {
        width:62%
    }
    #ez-account-tabs .ez-account-tab-details li.ez-recent-btn {
        width:38%
    }
    #ez-account-tabs .ez-account-tab-details li.ez-promotion-flag {
        display:none
    }
    #ez-account-tabs .ez-account-tab-details li.ez-promotion-name {
        width:38%
    }
    #ez-account-tabs .ez-account-tab-details li.ez-promotion-btns {
        width:62%
    }
    #ez-account-tabs .ez-icon {
        display:none
    }
    #ez-contacts-wrap .ez-table td {
        line-height:22px
    }
    #ez-contacts-wrap .ez-table td:first-child {
        border-bottom:0
    }
    #ez-contacts-wrap .ez-table td:first-child a {
        margin-left:15px
    }
    #ez-contacts-wrap .ez-table td:first-child .ez-button {
        min-width:110px
    }
    #ez-contacts-wrap .ez-table td.ez-table-actions {
        float:right;
        padDing:8px 14px
    }
    #ez-contacts-wrap .ez-table td.ez-table-actions a {
        margin-left:0
    }
    #ez-my-profile #ez-account-tabs>li {
        width:100%;
        min-height:180px;
        margin-left:0;
        background:transparent;
        padDing-top:0;
        padDing-bottom:0;
        display:block!important;
        border-top:none
    }
    #ez-my-profile #ez-account-tabs h3 {
        text-align:left;
        margin:4px 0 -2px 0;
        font-size:24px
    }
    #ez-my-profile #ez-account-tabs h3:first-child {
        margin-top:0
    }
    #ez-my-profile #ez-account-tabs .ez-my-profile-info {
        font-size:21px;
        line-height:normal;
        margin-bottom:22px
    }
    #ez-my-profile #ez-account-tabs .ez-my-profile-img {
        width:60px;
        height:60px
    }
    #ez-my-profile #ez-account-tabs .ez-my-profile-img span {
        font-size:50px
    }
    #ez-vouchers-page .ez-vouchers-filter-band h2 {
        float:none
    }
    #ez-vouchers-page .ez-vouchers-filter-band .ez-mobile-menu {
        float:none;
        margin-top:10px
    }
    #ez-vouchers-page .ez-invite-social {
        background:transparent;
        padDing:0
    }
    #ez-vouchers-page .ez-no-vouchers-invite p {
        font-size:17px;
        line-height:21px
    }
    #ez-vouchers-page .ez-no-vouchers #left-side,
    #ez-vouchers-page .ez-no-vouchers #right-side {
        width:100%
    }
    #ez-vouchers-page .ez-no-vouchers #left-side div.ez-invite-mobile,
    #ez-vouchers-page .ez-no-vouchers #right-side div.ez-invite-mobile {
        margin-top:20px;
        display:block
    }
    #ez-vouchers-page .ez-vouchers-items .ez-table td,
    #ez-vouchers-page .ez-vouchers-expired .ez-table td {
        padDing-top:6px;
        padDing-bottom:6px
    }
    #ez-vouchers-page .ez-vouchers-items .ez-table .ez-table-selector,
    #ez-vouchers-page .ez-vouchers-expired .ez-table .ez-table-selector {
        display:none
    }
    #ez-vouchers-page .ez-vouchers-items .ez-table .ez-table-voucher span,
    #ez-vouchers-page .ez-vouchers-expired .ez-table .ez-table-voucher span {
        float:none
    }
    #ez-vouchers-page .ez-vouchers-items .ez-table .ez-new-voucher p,
    #ez-vouchers-page .ez-vouchers-expired .ez-table .ez-new-voucher p {
        font-weight:normal
    }
    #use-voucher-modal .ez-second-block {
        width:85%
    }
    body.ar #use-voucher-modal button {
        font-size:15px
    }
    body.ta #registerRAF #ez-signup-with-email,
    body.ta #registerRAF #ez-facebook-link button,
    body.de #registerRAF #ez-signup-with-email,
    body.de #registerRAF #ez-facebook-link button {
        font-size:16px
    }
    #registerRAF .ez-facebook-reassure {
        display:none
    }
    #registerRAF #ez-signup-with-email,
    #registerRAF #ez-facebook-link button {
        margin-top:0;
        font-size:17px
    }
    #registerRAF #ez-facebook-link {
        width:49%;
        float:left
    }
    #registerRAF #ez-fb-login-separator {
        display:none
    }
    #registerRAF #ez-register-wrap {
        width:49%;
        float:right
    }
    #registerRAF #ez-left-side {
        width:100%;
        float:none;
        margin:0
    }
    #registerRAF #ez-right-side {
        width:100%;
        float:none;
        margin-top:10px
    }
    #registerRAF #ez-right-side .title {
        font-size:30px;
        margin-bottom:0
    }
    #registerRAF #ez-right-side .subtitle {
        display:block;
        font-size:20px;
        line-height:28px;
        margin-bottom:10px
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps {
        width:100%;
        padDing-left:50px;
        margin-top:19px
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive,
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step {
        width:70%;
        float:none;
        padDing-top:23px;
        clear:both
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive:first-child,
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step:first-child {
        padDing-top:0
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive:first-child .ez-number,
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step:first-child .ez-number {
        margin-left:0
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-number,
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step .ez-number {
        font-size:120px;
        margin-left:-5px
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-title,
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step .ez-title {
        margin-left:70px
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-content,
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step .ez-content {
        font-size:15px;
        margin-left:70px
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-number {
        width:78px;
        text-align:center
    }
    #registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial {
        margin-top:30px
    }
    #registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-title {
        font-size:18px;
        line-height:22px
    }
    #registerRAF .ez-registration-footer .ez-community {
        margin-top:30px
    }
    #registerRAF .ez-registration-footer .ez-community .ez-title {
        font-size:22px
    }
    #registerRAF .ez-payment-types {
        padDing-bottom:18px
    }
    #registerRAF .ez-payment-types .ez-payment-box {
        margin:0 0 12px 4px
    }
    #registerRAF .ez-payment-types .ez-title {
        font-size:24px;
        line-height:30px
    }
    #ez-refer-steps-image {
        width:42px;
        position:absolute;
        top:55px
    }
    #ez-refer-steps-image li {
        display:list-item;
        margin:0
    }
    #ez-refer-steps-image li.ez-line {
        width:3px;
        height:113px;
        display:block;
        margin:0 auto
    }
    #ez-refer-steps-image li.fa-asterisk {
        margin:-5px 0
    }
    #ez-refer-steps-image li.fa-gift {
        margin-top:-12px
    }
    #ez-payment-response-page #left-side {
        width:100%
    }
    #ez-payment-response-page #left-side .referButton,
    #ez-payment-response-page #left-side #referOptionsWrap {
        display:block
    }
    #ez-payment-response-page #left-side .ez-terms {
        display:block;
        font-size:17px
    }
    #ez-payment-response-page #right-side {
        display:none
    }
    .ez-invite-social {
        display:none
    }
    .ez-invite-social.ez-mobile {
        display:block
    }
    .ez-invite-social.ez-mobile a {
        width:100%!important;
        margin:0;
        text-align:left
    }
    #ez-promo-countries-wrap .ez-list li.ez-list-item {
        width:29%;
        margin-left:2.8%
    }
    #ez-promo-countries-wrap .ez-list li.ez-list-item.ez-five-items-row {
        margin-left:2.8%
    }
    #ez-promo-countries-wrap .ez-list li.ez-list-item.ez-four-items-row {
        margin-left:2.8%
    }
    #ez-promo-countries-wrap .ez-list li.ez-list-item.ez-three-items-row {
        margin-left:4%
    }
    #ez-promos .ez-promo-btns {
        padDing-top:20px;
        float:none!important;
        clear:both;
        text-align:center
    }
    #ez-cms-content p {
        font-size:17px
    }
    #ez-support-chat {
        width:41%
    }
    #ez-support-trust-panel {
        width:57.4%
    }
    #ez-support-trust-panel li {
        width:49%
    }
    #ez-support-trust-panel li:first-child {
        display:none
    }
    #ez-lanDing-prmotion p {
        font-size:26px
    }
    #ez-subscribe-newsletter {
        width:100%
    }
    #ez-subscribe-newsletter h3 {
        font-size:20px
    }
    #ez-subscribe-newsletter h4 {
        font-size:16px;
        margin-bottom:20px
    }
    #ez-subscribe-newsletter div {
        margin-bottom:10px
    }
    #ez-subscribe-newsletter div input {
        width:100%
    }
    #ez-subscribe-newsletter div button {
        width:100%;
        margin-left:0;
        margin-top:10px
    }
    #ez-subscribe-newsletter span {
        font-size:16px
    }
    #ez-call-history-wrap .ez-headline {
        float:none
    }
    #ez-call-history-wrap .ez-ild-balance {
        float:none;
        margin:-14px 0 20px 0
    }
    #ez-ild-rates-page #ez-ild-country-selectors {
        float:none;
        width:100%
    }
    #ez-ild-rates-page #ez-ild-rates-panel {
        float:none;
        width:auto
    }
    #ez-ild-rates-page #ez-ild-rates-panel .ez-topup-btns {
        margin-left:0
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap {
        width:100%;
        text-align:center
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap ul {
        padDing-top:8px;
        margin-left:10px
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap .ez-ild-rates-row {
        float:none;
        display:inline-block;
        margin-left:0;
        padDing-top:0
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap .ez-rates-fixed {
        margin-right:0
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap .ez-rates-icon {
        width:65px;
        height:65px;
        margin-right:0;
        margin-bottom:0
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap .ez-rates-icon em {
        line-height:65px
    }
    #ez-careers-wrap .ez-careers-article li {
        width:100%!important;
        float:none;
        text-align:left;
        margin:0 0 40px 0
    }
    #ez-careers-wrap .ez-careers-article li:first-child {
        margin-bottom:20px;
        text-align:center!important
    }
    #ez-careers-wrap .ez-careers-article li h3 {
        margin-bottom:8px
    }
    #ez-careers-wrap .ez-careers-article li img {
        width:400px;
        height:258px
    }
    .ez-careers-job p {
        float:none!important;
        margin-top:4px;
        font-size:19px!important
    }
    .ez-careers-job p:first-child {
        margin-top:0
    }
    .ez-three-in-one span.main {
        margin-top:5px!important;
        margin-bottom:-10px!important;
        font-size:16px!important;
        font-weight:normal!important
    }
    .ez-three-in-one h3.main {
        font-size:18px!important;
        padDing-top:0!important;
        margin-bottom:0!important;
        margin-top:-5px!important;
        font-weight:bold!important
    }
    .ez-three-in-one span.others {
        margin-top:0!important;
        margin-bottom:-8px!important;
        font-size:16px!important;
        font-weight:normal!important
    }
    .ez-three-in-one h3.others {
        font-size:16px!important;
        padDing-top:0!important;
        margin-bottom:-10px!important;
        font-weight:bold!important
    }
}
@media all and (max-width: 660px) {
    #ez-send-topup .ez-airtime-wrap ul input,
    #ez-send-topup .ez-airtime-wrap ul .ez-phone-prefix {
        font-size:24px!important
    }
    body.ta #registerRAF #ez-signup-with-email,
    body.ta #registerRAF #ez-facebook-link button,
    body.de #registerRAF #ez-signup-with-email,
    body.de #registerRAF #ez-facebook-link button {
        font-size:18px
    }
    body.ta #registerRAF #ez-signup-with-email .fa,
    body.ta #registerRAF #ez-facebook-link button .fa,
    body.de #registerRAF #ez-signup-with-email .fa,
    body.de #registerRAF #ez-facebook-link button .fa {
        display:block
    }
    #registerRAF #ez-right-side .title {
        font-size:26px
    }
    #registerRAF #ez-right-side .subtitle {
        font-size:16px
    }
    #registerRAF #ez-right-side {
        margin-bottom:34px
    }
    #registerRAF .ez-facebook-reassure {
        display:block
    }
    #registerRAF #ez-signup-with-email,
    #registerRAF #ez-facebook-link button {
        margin-top:16px;
        font-size:18px
    }
    #registerRAF #ez-facebook-link {
        width:100%;
        float:none
    }
    #registerRAF #ez-register-wrap {
        width:100%;
        float:none
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive,
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step {
        width:90%
    }
    #ez-payment-response-page #left-side .referOptions {
        display:block
    }
    #ez-payment-response-page #left-side .referOptions .ez-action-text {
        display:none
    }
    #ez-payment-response-page #left-side .referOptions .ez-send-methods {
        margin-left:20px;
        margin-right:20px
    }
    #ez-payment-response-page #left-side .referOptions .ez-send-methods a {
        display:block;
        border-color:#eeedef!important;
        border-style:solid!important;
        border-top:1px;
        border-left:0;
        border-right:0;
        border-bottom:0;
        font-size:18px;
        font-family:Roboto
    }
    #ez-payment-response-page #left-side .referOptions .ez-send-methods a span {
        margin-right:10px;
        font-size:30px;
        color:#2f4f4f;
        width:30px
    }
    #ez-payment-response-page #left-side .referOptions .ez-send-methods .ez-last {
        border-bottom:1px
    }
    #ez-payment-response-page #left-side .ez-terms {
        display:block;
        font-size:14px;
        margin-bottom:10px
    }
    #ez-payment-response-page div#right-side {
        display:none
    }
    div#existing-user-modal {
        width:500px
    }
    div#existing-user-modal h3 {
        font-size:56px;
        height:56px
    }
    div#existing-user-modal h2 {
        font-size:26px
    }
    div#existing-user-modal p {
        font-size:16px
    }
    div#existing-user-modal div.ez-modal-footer button {
        width:500px
    }
    #ez-paypal-link.ez-three-btns {
        float:none;
        margin-right:0!important
    }
}
@media all and (max-width: 600px) {
    h1 {
        font-size:36px;
        line-height:42px
    }
    label,
    .ez-form-checkbox label {
        font-size:16px
    }
    input[type=text],
    input[type=password],
    input[type=email],
    input[type=tel],
    input[type=number],
    input[type=submit],
    button,
    .ez-button,
    .ez-airtime-wrap,
    .ez-select-wrap {
        height:44px
    }
    .ez-airtime-wrap ul li input {
        height:42px
    }
    .ez-select-wrap .ez-custom-select {
        top:43px
    }
    .ez-select-wrap .ez-custom-select.ez-top {
        bottom:43px
    }
    .ez-disabled-scroll {
        overflow-y:initial;
        position:initial
    }
    .ez-main-wrap {
        padDing-top:20px;
        min-height:initial
    }
    .ez-page-title {
        margin-bottom:20px
    }
    .ez-cmenu {
        margin-bottom:20px
    }
    .ez-checkbox-wrap {
        margin-top:-4px
    }
    #ez-billing-details em.ezeotu-error,
    .ez-select-wrap {
        line-height:42px
    }
    button,
    input[type=submit],
    .ez-button,
    #ez-ild-add-credit span {
        line-height:44px
    }
    .ez-button.ez-topup-btn {
        height:50px;
        line-height:50px
    }
    .ez-select-wrap .ez-select-info span {
        font-size:22px
    }
    .owl-item {
        margin-left:-16px
    }
    .ez-mobile-hide {
        display:none!important
    }
    .ez-desktop-hide {
        display:block!important
    }
    .ez-mobile-full-width {
        width:100%!important
    }
    em.ez-error,
    div.ez-error,
    span.field-validation-error,
    em.ez-good,
    em.ezeotu-error {
        font-size:13px
    }
    .ez-auth-pages {
        width:100%!important;
        padDing-left:4%;
        padDing-right:4%
    }
    .ez-edit-wrap,
    .ez-content-left-info {
        width:100%
    }
    .ez-edit-wrap .ez-topup-carousel {
        margin-left:0!important
    }
    .ez-background-asterisk {
        width:600px;
        height:600px;
        margin:0 -140px -290px
    }
    .ez-card-cvv a {
        top:9px
    }
    .ez-form-row {
        min-height:108px
    }
    .ez-form-row.ez-airtime-row {
        min-height:74px
    }
    .ez-form-row.ez-airtime-row.ez-has-toggle {
        min-height:144px
    }
    a.ez-forgot-pass {
        font-size:16px
    }
    .ez-cool-top-border {
        border-top-width:2px
    }
    .ez-cool-bottom-border {
        border-bottom-width:2px
    }
    .ez-accordion .ez-accordionitem .fa {
        right:10px
    }
    .ez-accordion .ez-accordiontitle {
        padDing:16px 44px 16px 12px;
        font-size:17px;
        line-height:23px
    }
    .simplemodal-container.ez-big-modal {
        width:100%!important;
        left:0!important;
        margin-top:0!important;
        position:absolute!important
    }
    .simplemodal-container.ez-big-modal.ez-big-modal-top {
        top:0!important
    }
    .simplemodal-container.ez-big-modal .ez-modal,
    .simplemodal-container.ez-big-modal .ez-modal-header {
        border-radius:0
    }
    .simplemodal-container.ez-big-modal .ez-modal-content {
        max-height:100%!important
    }
    body.ru .ez-modal button,
    body.ta .ez-modal button,
    body.de .ez-modal button {
        width:100%;
        font-size:16px
    }
    #ez-forgot-pass-intro {
        font-size:16px;
        line-height:20px
    }
    #ez-careers-movie {
        height:320px
    }
    #ez-careers-movie iframe {
        height:320px
    }
    header {
        height:56px
    }
    header #ez-logo-wrap {
        margin-top:16px
    }
    header #ez-logo-wrap a {
        width:58px;
        height:25px;
        background-size:58px 25px
    }
    header nav {
        margin-top:6px
    }
	#cobrand {
        
		width:58px;
        height:25px;
        background-size:58px 25px
    }
    #ez-live-chat {
        padDing-right:0;
        -webkit-transition:none;
        transition:none;
        -moz-transition:none;
        -o-transition:none
    }
    #ez-live-chat div {
        display:none
    }
    #ez-live-chat:hover {
        padDing-right:0
    }
    .ez-header-notification .ez-header-notification-content p,
    .ez-header-notification-red .ez-header-notification-content p {
        text-align:center;
        font-size:16px;
        padDing-right:12px;
        line-height:18px;
        position:relative;
        border-right:1px solid rgba(255,
        255,
        255,
        .2)
    }
    .ez-header-notification .ez-header-notification-content .ez-mobile-link,
    .ez-header-notification-red .ez-header-notification-content .ez-mobile-link {
        display:block;
        position:absolute;
        top:0;
        left:0;
        bottom:0;
        right:0;
        margin:0
    }
    .ez-header-notification .ez-header-notification-content .ez-notification-icon,
    .ez-header-notification-red .ez-header-notification-content .ez-notification-icon {
        display:none
    }
    .ez-list li.ez-list-item {
        width:100%!important;
        margin:0 0 8px 0!important;
        box-shadow:0,
        2px,
        4px,
        #fff;
        -webkit-box-shadow:0,
        2px,
        4px,
        -2px,
        rgba(0,
        0,
        0,
        .5);
        -moz-box-shadow:0,
        2px,
        4px,
        -2px,
        rgba(0,
        0,
        0,
        .5);
        box-shadow:0 2px 4px -2px rgba(0,
        0,
        0,
        .5)
    }
    .ez-list li.ez-list-item .ez-item-amount-wrap {
        margin:15px 20px 0 0
    }
    .ez-list li.ez-list-item .ez-item-amount-wrap span {
        font-size:16px;
        font-weight:bold
    }
    .ez-list li.ez-list-item>div:first-child {
        height:60px
    }
    .ez-list li.ez-list-item .ez-flag-big {
        margin-top:15px
    }
    .ez-list li.ez-list-item .ez-list-item-arrow {
        bottom:-21px
    }
    .ez-list li.ez-list-item p {
        width:56%;
        text-align:left;
        color:#4c4c59;
        font-size:16px;
        float:left;
        margin:13px 0 0 10px;
        border-left:0!important;
        background:transparent!important
    }
    .ez-list li.ez-list-item p a {
        color:#4c4c59
    }
    footer {
        padDing-bottom:10px
    }
    footer .ez-wrap {
        padDing:0
    }
    footer #ez-footer-socials div {
        padDing:0
    }
    footer #ez-footer-socials a {
        margin-left:0
    }
    footer #ez-footer-socials a span {
        width:32px;
        height:32px;
        line-height:32px;
        font-size:18px
    }
    footer .ez-footer-accordion ul {
        width:100%;
        margin:0;
        float:none;
        padDing-bottom:5px
    }
    footer .ez-footer-accordion li {
        display:none;
        padDing:0 30px;
        background:#414c53
    }
    footer .ez-footer-accordion li.ez-menu-header {
        display:block!important;
        line-height:44px;
        cursor:pointer;
        background:none
    }
    footer .ez-footer-accordion li.ez-menu-header:hover,
    footer .ez-footer-accordion li.ez-menu-header.ez-active {
        background:#414c53
    }
    footer .ez-footer-accordion li.ez-menu-header span {
        display:block
    }
    footer .language-container {
        width:100%
    }
    footer .language-container .ez-custom-select {
        padDing-bottom:0
    }
    footer .language-container li {
        display:block;
        background:none;
        padDing:0 20px
    }
    footer .language-container li.ez-menu-header {
        display:none!important
    }
    footer .language-container .fa-angle-up,
    footer .language-container .fa-angle-down {
        color:#8a9597;
        font-size:26px!important
    }
    footer #footer-terms li {
        width:100%;
        text-align:center
    }
    footer #footer-terms .separator {
        display:none
    }
    .ez-airtime-toggle {
        height:42px;
        position:static;
        line-height:42px;
        margin-bottom:24px;
        text-align:center;
        background:#fff;
        border:1px solid #c2c2c2;
        border-radius:4px
    }
    .ez-airtime-toggle input[type=radio] {
        position:absolute
    }
    .ez-airtime-toggle label {
        width:49%;
        font-size:20px;
        margin:0
    }
    .ez-airtime-toggle label:last-child {
        margin-left:-4px;
        border-left:1px solid #c2c2c2
    }
    .ez-airtime-toggle .ez-separator {
        height:26px;
        top:7px;
        display:none
    }
    #ez-send-topup {
        height:330px
    }
    #ez-send-topup .ez-spacer {
        height:40px
    }
    #ez-send-topup .ez-airtime-wrap {
        height:50px
    }
    #ez-send-topup .ez-airtime-wrap ul {
        width:60%
    }
    #ez-send-topup .ez-airtime-wrap ul input,
    #ez-send-topup .ez-airtime-wrap ul .ez-phone-prefix {
        font-size:21px!important
    }
    #ez-send-topup .ez-airtime-wrap ul input {
        padDing:0 5px;
        height:48px
    }
    #ez-send-topup .ez-airtime-wrap ul .ez-phone-prefix {
        margin-right:1px;
        margin-left:1px
    }
    #ez-send-topup .ez-home-asterisk {
        width:480px;
        margin:0 -120px -230px
    }
    #ez-send-topup .ez-button {
        width:38%;
        font-size:20px;
        padDing:0 6px
    }
    #use-voucher-modal .ez-second-block {
        width:100%
    }
    body.ar #use-voucher-modal button {
        font-size:14px
    }
    .ez-topup-btns {
        padDing:12px 10px
    }
    .ez-topup-btns button,
    .ez-topup-btns input[type="submit"] {
        line-height:20px
    }
    .ez-topup-btns button,
    .ez-topup-btns input[type="submit"],
    .ez-topup-btns .ez-button {
        font-size:18px
    }
    .ez-topup-btns button.ez-two-btns,
    .ez-topup-btns input[type="submit"].ez-two-btns,
    .ez-topup-btns .ez-button.ez-two-btns {
        width:45%;
        min-width:initial
    }
    .ez-topup-btns button.ez-three-btns,
    .ez-topup-btns input[type="submit"].ez-three-btns,
    .ez-topup-btns .ez-button.ez-three-btns {
        padDing:0 5px;
        width:32%!important
    }
    .ez-topup-btns button.ez-three-btns.ez-float-left-btn,
    .ez-topup-btns input[type="submit"].ez-three-btns.ez-float-left-btn,
    .ez-topup-btns .ez-button.ez-three-btns.ez-float-left-btn {
        float:left
    }
    .ez-topup-btns button.ez-three-btns.ez-float-right-btn,
    .ez-topup-btns input[type="submit"].ez-three-btns.ez-float-right-btn,
    .ez-topup-btns .ez-button.ez-three-btns.ez-float-right-btn {
        float:right
    }
    .ez-topup-btns button.ez-three-btns.ez-margin-btn,
    .ez-topup-btns input[type="submit"].ez-three-btns.ez-margin-btn,
    .ez-topup-btns .ez-button.ez-three-btns.ez-margin-btn {
        margin-left:0!important
    }
    .ez-topup-btns button.ez-margin-btn,
    .ez-topup-btns input[type="submit"].ez-margin-btn,
    .ez-topup-btns .ez-button.ez-margin-btn {
        margin-left:3%
    }
    body.ar .ez-topup-btns button.ez-three-btns,
    body.ar .ez-topup-btns input[type="submit"].ez-three-btns,
    body.ar .ez-topup-btns .ez-button.ez-three-btns,
    body.ur .ez-topup-btns button.ez-three-btns,
    body.ur .ez-topup-btns input[type="submit"].ez-three-btns,
    body.ur .ez-topup-btns .ez-button.ez-three-btns {
        float:right
    }
    body.ar .ez-topup-btns button.ez-three-btns.ez-float-right-btn,
    body.ar .ez-topup-btns input[type="submit"].ez-three-btns.ez-float-right-btn,
    body.ar .ez-topup-btns .ez-button.ez-three-btns.ez-float-right-btn,
    body.ur .ez-topup-btns button.ez-three-btns.ez-float-right-btn,
    body.ur .ez-topup-btns input[type="submit"].ez-three-btns.ez-float-right-btn,
    body.ur .ez-topup-btns .ez-button.ez-three-btns.ez-float-right-btn {
        float:left
    }
    body.ar .ez-topup-btns button.ez-margin-btn,
    body.ar .ez-topup-btns input[type="submit"].ez-margin-btn,
    body.ar .ez-topup-btns .ez-button.ez-margin-btn,
    body.ur .ez-topup-btns button.ez-margin-btn,
    body.ur .ez-topup-btns input[type="submit"].ez-margin-btn,
    body.ur .ez-topup-btns .ez-button.ez-margin-btn {
        margin-right:2%;
        margin-left:0
    }
    .ez-highlight-area .ez-button {
        min-width:44%
    }
    #ez-account-main-action {
        width:100%;
        height:initial;
        padDing:0 14px 10px 14px
    }
    #ez-account-main-action .ez-icon {
        width:54px;
        height:54px;
        margin:0 20px 10px 0;
        float:left
    }
    #ez-account-main-action .ez-icon span {
        line-height:54px;
        font-size:40px
    }
    #ez-account-main-action .ez-btn-wrap {
        position:static
    }
    #ez-account-main-action .ez-topup-btn {
        margin-bottom:14px
    }
    #ez-account-main-action .ez-topup-btn:first-child {
        margin-bottom:14px
    }
    #ez-account-main-action .ez-single-btn {
        width:100%!important
    }
    #ez-account-tabs {
        margin-top:0
    }
    #ez-account-tabs h3 {
        margin-top:10px
    }
    #ez-account-tabs p {
        margin-bottom:10px
    }
    #ez-account-tabs .ez-cool-top-border {
        border-top-width:0
    }
    #ez-account-tabs>li {
        width:100%;
        height:initial;
        padDing:0;
        margin:0 0 2px 0;
        background:#fff
    }
    #ez-account-tabs>li.ez-account-first-hide {
        display:block
    }
    #ez-account-tabs>li:first-child>div {
        display:block
    }
    #ez-account-tabs>li .ez-account-tab-details {
        margin-top:4px
    }
    #ez-account-tabs>li .ez-account-tab-details li {
        font-size:17px
    }
    #ez-account-tabs>li>ul {
        cursor:pointer;
        padDing:7px 12px;
        background:#eeedef
    }
    #ez-account-tabs>li>div {
        display:none;
        padDing:10px 0
    }
    #ez-account-tabs>li .ez-icon {
        width:44px;
        height:44px;
        line-height:44px
    }
    #ez-account-tabs>li .ez-icon-text {
        float:left
    }
    #ez-account-tabs>li .fa-angle-down,
    #ez-account-tabs>li .fa-angle-up {
        float:right;
        font-size:24px;
        color:#001a6e
    }
    #ez-account-tabs>li#ez-overview-promotions h2 {
        font-size:35px
    }
    #ez-account-tabs>li#ez-overview-promotions .ez-account-tab-details li a.ez-link-btn {
        margin-bottom:-5px
    }
    #ez-account-tabs .ez-btn-wrap {
        position:static;
        margin:40px 0 10px 0
    }
    #ez-promo-countries li.ez-promos-container {
        float:left;
        margin-top:12px
    }
    #ez-promos .ez-promos-carousel-item {
        display:none
    }
    #ez-promos .ez-promos-carousel-item:first-child {
        display:block
    }
    #ez-promos .ez-promos-carousel-item img {
        display:none
    }
    #ez-promos .ez-promos-carousel-item .ez-promotion {
        text-align:center
    }
    #ez-promos ul {
        float:none!important
    }
    #ez-promos .ez-promo-title {
        font-size:26px;
        line-height:24px!important
    }
    #ez-promos .ez-promo-btns {
        padDing-top:0
    }
    #ez-promos .ez-promo-btns .ez-desktop-hide {
        display:inline!important
    }
    #ez-support-chat {
        width:100%;
        height:initial;
        display:block;
        padDing-top:20px;
        padDing-bottom:16px;
        text-align:center
    }
    #ez-support-chat ul {
        display:initial
    }
    #ez-support-chat li span {
        font-size:18px
    }
    #ez-support-trust-panel {
        display:none
    }
    #ez-support-content {
        margin-top:4px
    }
    #ez-vouchers-page .ez-vouchers-items .ez-table,
    #ez-vouchers-page .ez-vouchers-expired .ez-table {}#ez-vouchers-page .ez-table-usewith a {
        width:50%;
        display:table-cell;
        vertical-align:middle;
        padDing:8px
    }
    #ez-lanDing-prmotion h2 {
        font-size:50px;
        line-height:48px
    }
    #ez-localposts-container .ez-post-image {
        width:32%;
        height:100px
    }
    #ez-localposts-container .ez-post-content {
        width:64%;
        height:100px;
        padDing:0;
        overflow:hidden
    }
    #ez-localposts-container .ez-post-content p {
        line-height:24px
    }
    #ez-localposts-container .ez-localblog-readmore a {
        width:100%;
        height:100%;
        position:absolute;
        top:0;
        left:0;
        opacity:0;
        filter:alpha(opacity=0)
    }
    #ez-cvv-tip {
        width:80%
    }
    #ez-migpage-top {
        height:initial
    }
    #ez-migpage-top h1 {
        width:100%;
        text-align:left;
        font-size:60px;
        line-height:52px
    }
    #ez-migpage-top h1 br {
        display:none
    }
    #ez-migpage-top img {
        width:330px;
        height:auto;
        margin-top:26px
    }
    #ez-migpage-middle .ez-desktop-hide {
        margin-bottom:20px
    }
    #ez-migpage-middle .ez-desktop-hide img {
        width:28%;
        display:inline-block;
        margin-left:4%
    }
    #ez-migpage-middle .ez-desktop-hide img:first-child {
        margin-left:0
    }
    #ez-migpage-middle li {
        width:100%;
        margin-bottom:14px
    }
    #ez-migpage-middle li img {
        display:none
    }
    #ez-payment-response-page #left-side {
        width:100%
    }
    #ez-raf-title #ez-page-subtitle {
        font-size:19px;
        line-height:23px
    }
    #invalid-referral-modal {
        width:440px
    }
    #invalid-referral-modal .ez-modal .ez-modal-header {
        padDing-bottom:34px
    }
    #invalid-referral-modal .ez-modal .ez-modal-header h3 {
        font-size:40px
    }
    #invalid-referral-modal .ez-modal .ez-modal-header p {
        font-size:22px;
        height:18px
    }
    #invalid-referral-modal .ez-modal .ez-modal-content h2 {
        font-size:26px
    }
    #invalid-referral-modal .ez-modal .ez-modal-content p {
        font-size:14px
    }
    #invalid-referral-modal .ez-modal .ez-modal-footer button {
        font-size:24px;
        line-height:50px;
        height:62px
    }
    #registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-content .ez-commercial-box {
        width:100%
    }
    #registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-content .ez-commercial-box .ez-content {
        font-size:16px
    }
    #registerRAF .ez-payment-types .ez-payment-box {
        width:74px;
        height:42px;
        background:url(../images/sprite.png) 0 0 no-repeat
    }
    #registerRAF .ez-payment-types .ez-payment-box.ez-mcard {
        background-position:-78px 0
    }
    #registerRAF .ez-payment-types .ez-payment-box.ez-visa {
        background-position:-156px 0
    }
    #registerRAF .ez-payment-types .ez-payment-box.ez-diners {
        background-position:0 -46px
    }
    #registerRAF .ez-payment-types .ez-payment-box.ez-discover {
        background-position:-78px -46px
    }
    #ez-careers-wrap .ez-careers-btn {
        font-size:22px
    }
    #ez-careers-wrap a.ez-careers-btn {
        line-height:60px
    }
    #ez-careers-wrap .ez-careers-quote {
        text-align:center
    }
    #ez-careers-wrap .ez-careers-quote img {
        float:none!important
    }
    #ez-careers-wrap .ez-careers-quote p {
        text-align:left;
        margin:20px 0 0 0!important;
        padDing-left:0
    }
    #ez-careers-wrap .ez-careers-quote p .fa-quote-left {
        display:none
    }
    #ez-carrers-nav {
        height:78px;
        margin-top:0;
        position:fixed;
        left:0;
        bottom:0;
        z-index:100;
        background:#001a6e
    }
    #ez-carrers-nav nav a {
        font-size:20px
    }
    #ez-carrers-nav nav span {
        font-size:30px;
        margin-bottom:2px
    }
    #ez-carrers-nav nav span.fa-map-marker {
        font-size:34px
    }
    #ez-careers-banner {
        height:370px
    }
    #ez-careers-banner h1 {
        font-size:72px;
        line-height:64px;
        padDing-top:60px
    }
    #ez-careers-banner p {
        font-size:26px!important;
        line-height:30px
    }
    #ez-careers-banner hr {
        margin:20px auto
    }
    #ez-careers-poster {
        height:280px
    }
    #ez-careers-poster button {
        margin-top:110px
    }
    #ez-careers-poster button .fa {
        width:40px;
        height:40px;
        padDing-right:5px;
        padDing-top:8px
    }
    #ez-careers-poster #ez-video-btn {
        font-size:26px;
        line-height:28px;
        opacity:.8
    }
    #ez-careers-poster #ez-video-btn span {
        font-size:21px
    }
    .ez-careers-office-map:first-child {
        margin-top:0
    }
    #ez-careers-bottom {
        height:140px
    }
    #ez-careers-bottom a {
        margin-top:40px
    }
    #ez-careers-bottom .ez-asterisk {
        display:none
    }
    .ez-careers-job {
        padDing:15px!important
    }
    .ez-careers-job p:first-child {
        font-size:21px!important
    }
    #ez-operators-wrap a {
        font-size:18px
    }
    div#existing-user-modal {
        width:420px
    }
    div#existing-user-modal h3 {
        font-size:56px;
        height:56px
    }
    div#existing-user-modal h2 {
        font-size:26px
    }
    div#existing-user-modal p {
        margin-top:18px;
        font-size:16px
    }
    div#existing-user-modal div.ez-modal-footer button {
        width:420px
    }
}
@media all and (max-width: 480px) {
    h1 {
        font-size:32px;
        line-height:38px
    }
    h2,
    #registerRAF .ez-registration-footer .ez-programme .ez-programme-title,
    #registerRAF .ez-registration-footer .ez-whyDing #ez-snippet-registration-whychooseDing .ez-Ding-commercial>.ez-title {
        font-size:25px;
        line-height:30px
    }
    h3,
    #registerRAF .ez-registration-footer .ez-trusted {
        font-size:22px;
        line-height:26px
    }
    input[type=text],
    input[type=password],
    input[type=email],
    input[type=tel],
    input[type=number],
    .ez-select-wrap,
    .ez-checkbox-wrap,
    textarea {
        font-size:20px
    }
    button,
    input[type=submit],
    .ez-button,
    #ez-ild-add-credit span {
        font-size:18px
    }
    body.ta button,
    body.ta input[type=submit],
    body.ta .ez-button,
    body.ru button,
    body.ru input[type=submit],
    body.ru .ez-button,
    body.de button,
    body.de input[type=submit],
    body.de .ez-button {
        font-size:16px!important
    }
    body.ta button .fa,
    body.ta input[type=submit] .fa,
    body.ta .ez-button .fa,
    body.ru button .fa,
    body.ru input[type=submit] .fa,
    body.ru .ez-button .fa,
    body.de button .fa,
    body.de input[type=submit] .fa,
    body.de .ez-button .fa {
        display:none!important
    }
    #ez-payment-response-page>h2 {
        font-size:20px!important
    }
    .ez-480-show {
        display:block!important
    }
    .ez-480-hide {
        display:none!important
    }
    .ez-main-wrap {
        padDing-top:20px
    }
    .ez-page-title {
        margin-bottom:16px
    }
    .ez-page-title #ez-page-subtitle {
        margin-top:-10px
    }
    .ez-page-title #ez-page-subtitle a {
        font-size:16px
    }
    .ez-background-asterisk {
        width:400px;
        height:400px;
        margin:0 -90px -190px
    }
    .ez-accordion .ez-accordiontitle {
        font-size:16px;
        line-height:22px
    }
    .ez-accordion .ez-accordiontext {
        font-size:16px
    }
    .owl-item {
        margin-left:-17px
    }
    .ez-airtime-wrap .ez-airtime-btn .ez-phone-prefix {
        margin-right:2px;
        margin-left:2px;
        font-size:20px
    }
    .ez-airtime-wrap input {
        padDing:0 5px
    }
    .ez-show-password {
        font-size:18px;
        right:6px
    }
    .ez-header-notification .ez-header-notification-content p,
    .ez-header-notification-red .ez-header-notification-content p {
        font-size:15px;
        line-height:16px
    }
    .ez-dplaceholder-wrap p {
        font-size:20px
    }
    .ez-auth-pages .fa-facebook-square,
    .ez-auth-pages .fa-envelope {
        font-size:20px;
        margin-left:0
    }
    .ez-auth-pages .fa-facebook-square {
        font-size:24px
    }
    .simplemodal-container {
        width:90%!important;
        left:5%!important
    }
    #ez-spinner-wrap {
        width:90%;
        left:5%;
        margin-left:0
    }
    .ez-modal-progress {
        width:100%
    }
    .ez-modal-progress .ez-modal-footer h3 {
        font-size:27px
    }
    .ez-modal .ez-modal-header h3 {
        font-size:18px;
        line-height:24px
    }
    .ez-modal .simplemodal-close {
        top:3px
    }
    .ez-modal .ez-modal-content {
        padDing:10px;
        font-size:16px;
        line-height:20px
    }
    .ez-modal button {
        width:100%
    }
    body.fr .ez-modal button,
    body.it .ez-modal button {
        font-size:18px
    }
    .ez-highlight-area .ez-button {
        min-width:100%;
        float:none
    }
    .ez-highlight-area .ez-button:first-child {
        margin-bottom:14px
    }
    .ez-details-wrap {
        padDing:20px 6px
    }
    .ez-table-list li {
        max-width:55%;
        font-size:16px;
        margin-bottom:0;
        line-height:22px
    }
    .ez-table-list li.ez-last-cell {
        font-weight:bold;
        font-size:15px
    }
    .ez-table-list li.ez-summary {
        font-size:18px
    }
    .ez-table-list li.ez-mobile-details {
        display:none
    }
    .ez-table-list li.ez-row-margin {
        margin-bottom:12px
    }
    .ez-table-list li .ez-transaction-status .fa-clock-o,
    .ez-table-list li .ez-transaction-status .fa-times-circle {
        line-height:26px
    }
    .ez-table-list.ez-table-list-summary li {
        font-size:18px
    }
    body.ar .ez-table-list li,
    body.ur .ez-table-list li {
        font-size:18px
    }
    .ez-summary-margin {
        height:14px
    }
    #ez-lanDing-trust-panel h2 {
        font-size:23px!important
    }
    #ez-cms-content .ez-trust-panel li .ez-trust-icon {
        width:44px;
        height:44px
    }
    .ez-cmenu li#ez-cmenu-mobile-btn {
        font-size:17px
    }
    .ez-table td.ez-table-amount {
        text-align:right;
        padDing:0 10px 0 0
    }
    #use-voucher-modal h3 .fa {
        display:none
    }
    #use-voucher-modal .ez-second-block {
        font-size:15px
    }
    body.ar #use-voucher-modal button {
        font-size:17px
    }
    .ez-airtime-toggle label {
        font-size:17px
    }
    #ez-send-topup {
        height:290px
    }
    #ez-send-topup .ez-asterisk.ez-svg {
        width:380px;
        margin:0 -98px -180px
    }
    #ez-send-topup .ez-airtime-wrap ul {
        display:none
    }
    #ez-send-topup .ez-topup-btn {
        width:100%;
        font-size:24px
    }
    #ez-fb-login-separator {
        display:none
    }
    #ez-payment-wrap {
        margin-top:-10px
    }
    #ez-billing-details em.ezeotu-error,
    #ez-billing-details em.ez-error {
        font-size:18px
    }
    body.de #ez-billing-details em.ezeotu-error,
    body.de #ez-billing-details em.ez-error,
    body.ta #ez-billing-details em.ezeotu-error,
    body.ta #ez-billing-details em.ez-error {
        font-size:15px
    }
    #ez-cvv-tip {
        width:92%
    }
    #ez-cvv-tip #ez-close-cvv-tip {
        right:-10px
    }
    .ez-topup-btns button.ez-three-btns,
    .ez-topup-btns input[type="submit"].ez-three-btns,
    .ez-topup-btns .ez-button.ez-three-btns {
        font-size:16px
    }
    .ez-topup-btns p {
        font-size:14px;
        line-height:18px
    }
    body.de .ez-topup-btns button.ez-three-btns,
    body.de .ez-topup-btns input[type="submit"].ez-three-btns,
    body.de .ez-topup-btns .ez-button.ez-three-btns,
    body.pt .ez-topup-btns button.ez-three-btns,
    body.pt .ez-topup-btns input[type="submit"].ez-three-btns,
    body.pt .ez-topup-btns .ez-button.ez-three-btns,
    body.ru .ez-topup-btns button.ez-three-btns,
    body.ru .ez-topup-btns input[type="submit"].ez-three-btns,
    body.ru .ez-topup-btns .ez-button.ez-three-btns {
        font-size:14px
    }
    #ez-sms-wrap .ez-sms-counter {
        font-size:16px
    }
    #ez-sms-fee {
        font-size:16px
    }
    #ez-order-summary {
        margin-top:20px;
        padDing:20px 10px
    }
    #ez-order-summary .ez-mobile-menu {
        margin:-12px 0 4px 0;
        display:block;
        background:#fff
    }
    #ez-promocode {
        margin-bottom:-10px
    }
    .ez-promotion-wrap {
        padDing-left:40px
    }
    .ez-promotion-wrap .fa-tags {
        font-size:16px
    }
    .ez-promotion {
        padDing:7px 10px 0
    }
    .ez-promotion ul li {
        font-size:16px;
        line-height:18px
    }
    .ez-promotion a.ez-modal-terms-btn {
        font-size:16px
    }
    #ez-promos .ez-promotion {
        padDing-bottom:7px
    }
    #ez-promos ul li {
        font-size:17px
    }
    #ez-promos .ez-promo-title {
        font-size:24px!important;
        line-height:22px!important
    }
    #ez-ild-rates-wrap .ez-rates-icon {
        width:40px;
        height:40px;
        margin-right:4px;
        border-radius:50%
    }
    #ez-ild-rates-wrap .ez-rates-icon em {
        font-size:30px;
        line-height:40px
    }
    #ez-ild-rates-wrap ul {
        padDing-top:2px
    }
    #ez-ild-rates-wrap ul li {
        font-size:16px;
        line-height:20px
    }
    #ez-ild-rates-wrap ul li:first-child {
        font-size:18px
    }
    #ez-account-tabs>li .ez-icon-text {
        margin-left:0
    }
    #ez-account-tabs>li .ez-btn-wrap *,
    #ez-account-tabs>li .ez-button,
    #ez-account-tabs>li #ez-facebook-link button {
        width:100%
    }
    #ez-contacts-wrap h2 {
        float:none;
        margin-bottom:14px
    }
    #ez-contacts-wrap .ez-create-btn {
        float:none;
        margin:0 0 20px 0
    }
    #ez-contacts-wrap .ez-table .ez-flag {
        margin-left:0
    }
    #ez-contacts-wrap .ez-table td.ez-table-actions {
        padDing:10px
    }
    #ez-contacts-wrap .ez-table .ez-button {
        min-width:70px!important
    }
    #FacebookLogin .ez-facebook-reassure {
        font-size:12px;
        line-height:16px
    }
    #ez-cms-content p {
        font-size:16px
    }
    #ez-last-used .ez-img-wrap {
        width:100px;
        height:50px;
        padDing-top:8px
    }
    #ez-last-used p {
        font-size:20px;
        line-height:26px
    }
    #ez-support-content .ez-accordion {
        padDing-left:5px;
        padDing-right:5px
    }
    #ez-migpage-top h1 {
        font-size:50px;
        line-height:42px
    }
    #ez-migpage-top img {
        width:280px
    }
    #ez-migpage-bottom .ez-button {
        width:100%
    }
    #ez-lanDing-prmotion h2 {
        font-size:46px!important;
        line-height:44px;
        margin:30px 20px
    }
    #ez-lanDing-prmotion p {
        font-size:22px;
        padDing-left:20px
    }
    #ez-subscribe-newsletter {
        width:100%
    }
    #ez-subscribe-newsletter h3 {
        font-size:20px
    }
    #ez-subscribe-newsletter h4 {
        font-size:16px;
        margin-bottom:20px
    }
    #ez-subscribe-newsletter div {
        margin-bottom:10px
    }
    #ez-subscribe-newsletter div input {
        width:100%
    }
    #ez-subscribe-newsletter div button {
        width:100%;
        margin-left:0;
        margin-top:10px
    }
    #ez-subscribe-newsletter span {
        font-size:16px
    }
    #ez-ild-rates-page #ez-ild-country-selectors {
        float:none;
        width:100%
    }
    #ez-ild-rates-page #ez-ild-rates-panel {
        float:none;
        width:auto
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap {
        text-align:left
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap ul {
        padDing-top:12px;
        margin-left:10px
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap .ez-ild-rates-row {
        margin-left:10%
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap .ez-topup-btns {
        margin-left:0
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap span.ez-rateValue {
        font-size:38px
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap span.ez-rateCurrency {
        font-size:17px
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap span.ez-ratePerMin {
        font-size:17px
    }
    #ez-ild-rates-page #ez-ild-rates-panel #ez-ild-rates-wrap .ez-rateFixedText {
        font-size:19px
    }
    #ez-android-page {
        padDing-top:10px;
        background-size:initial
    }
    #ez-android-page .ez-logo {
        width:120px;
        height:50px;
        background-size:120px 50px
    }
    #ez-android-page h1 {
        font-size:40px;
        line-height:44px;
        margin:4px 0 8px 0
    }
    #ez-android-page p {
        font-size:17px;
        line-height:23px;
        padDing:0 10px
    }
    #ez-payment-response-page #left-side .ez-subtitle {
        font-size:16px
    }
    #ez-vouchers-page .ez-vouchers-items .ez-table .ez-table-voucher p,
    #ez-vouchers-page .ez-vouchers-expired .ez-table .ez-table-voucher p {
        font-size:12px!important
    }
    #ez-vouchers-page .ez-back-btn .ez-button {
        width:100%
    }
    #ez-vouchers-page .ez-more-vouchers {
        width:100%
    }
    #invalid-referral-modal {
        width:100%
    }
    #invalid-referral-modal .ez-modal .ez-modal-header {
        padDing-bottom:34px
    }
    #invalid-referral-modal .ez-modal .ez-modal-header h3 {
        font-size:31px
    }
    #invalid-referral-modal .ez-modal .ez-modal-header p {
        font-size:16px;
        height:14px
    }
    #invalid-referral-modal .ez-modal .ez-modal-content {
        padDing-bottom:24px
    }
    #invalid-referral-modal .ez-modal .ez-modal-content h2 {
        font-size:24px!important
    }
    #invalid-referral-modal .ez-modal .ez-modal-content p {
        font-size:12px
    }
    #invalid-referral-modal .ez-modal .ez-modal-footer {
        padDing-bottom:16px
    }
    #invalid-referral-modal .ez-modal .ez-modal-footer button {
        font-size:20px;
        line-height:44px;
        height:58px
    }
    #ez-raf-title #ez-page-subtitle {
        font-size:17px;
        line-height:21px
    }
    #activationRAF .ez-button {
        width:100%
    }
    #registerRAF #ez-left-side a {
        font-size:16px
    }
    #registerRAF #ez-right-side .title {
        font-size:22px;
        margin-bottom:10px
    }
    #registerRAF #ez-right-side .subtitle {
        display:none
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive,
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step {
        width:86%;
        margin-left:0
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-number,
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step .ez-number {
        width:14px;
        font-size:26px;
        line-height:24px;
        padDing-right:8px;
        margin-left:0
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-title,
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step .ez-title {
        margin:0 0 5px 0
    }
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step-inactive .ez-content,
    #registerRAF .ez-registration-footer .ez-programme .ez-refer-steps .ez-step .ez-content {
        font-size:14px;
        margin-left:0
    }
    #registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-title {
        font-size:16px;
        line-height:20px
    }
    #registerRAF .ez-registration-footer .ez-whyDing .ez-Ding-commercial .ez-content .ez-commercial-box .ez-content {
        font-size:14px
    }
    #registerRAF .ez-payment-types .ez-payment-box {
        width:50px;
        height:28px
    }
    #registerRAF .ez-payment-types .ez-payment-box.ez-paypal {
        background-position:0 -144px
    }
    #registerRAF .ez-payment-types .ez-payment-box.ez-mcard {
        background-position:-54px -144px
    }
    #registerRAF .ez-payment-types .ez-payment-box.ez-visa {
        background-position:-108px -144px
    }
    #registerRAF .ez-payment-types .ez-payment-box.ez-diners {
        background-position:-162px -144px
    }
    #registerRAF .ez-payment-types .ez-payment-box.ez-discover {
        background-position:0 -176px
    }
    #registerRAF .ez-payment-types .ez-title {
        font-size:20px;
        line-height:26px
    }
    #ez-refer-steps-image {
        top:58px
    }
    #ez-refer-steps-image li.ez-line {
        height:80px
    }
    #ez-refer-steps-image li.fa-envelope {
        height:22px;
        font-size:26px;
        background-color:transparent
    }
    #ez-refer-steps-image li.fa-asterisk {
        font-size:32px
    }
    #ez-refer-steps-image li.fa-gift {
        font-size:36px
    }
    body.ta #ez-refer-steps-image {
        top:95px
    }
    #ez-careers-wrap p {
        font-size:15px
    }
    #ez-careers-wrap .ez-careers-wrap {
        padDing:30px 14px
    }
    #ez-careers-wrap .ez-margin-bottom {
        margin-bottom:30px
    }
    #ez-careers-wrap .ez-careers-big-text,
    #ez-careers-wrap .ez-careers-quote p {
        font-size:18px;
        line-height:24px
    }
    #ez-careers-wrap .ez-careers-quote.ez-quote-background {
        padDing:18px
    }
    #ez-careers-wrap .ez-careers-signature {
        font-size:16px!important
    }
    #ez-careers-wrap .ez-careers-article li h3 {
        font-size:28px
    }
    #ez-careers-wrap .ez-careers-article li img {
        width:100%;
        height:auto
    }
    #ez-carrers-nav {
        height:70px
    }
    #ez-carrers-nav nav a {
        font-size:18px
    }
    #ez-careers-banner {
        height:330px
    }
    #ez-careers-banner h1 {
        font-size:52px;
        line-height:46px;
        padDing-top:50px
    }
    #ez-careers-banner p {
        font-size:24px!important
    }
    #ez-careers-banner hr {
        margin:20px auto
    }
    #ez-careers-poster p {
        font-size:20px;
        line-height:26px;
        bottom:10px;
        text-align:left
    }
    #ez-careers-poster p br {
        display:none
    }
    .ez-careers-office-map {
        margin-bottom:18px
    }
    .ez-careers-office-map li:first-child {
        margin-bottom:4px
    }
    .ez-careers-office-map li span {
        width:100px;
        height:100px;
        background-size:100px 100px
    }
    .ez-careers-office-map li hr {
        top:51px
    }
    #ez-careers-joblist h2 {
        margin-bottom:50px
    }
    #ez-careers-joblist .ez-asterisk-line {
        top:100px!important
    }
    #ez-overview-bottom li h3 div {
        line-height:30px;
        margin-top:-18px
    }
    .ez-alert-box-message {
        font-size:16px;
        line-height:20px
    }
    .ez-alert-box-message h2 {
        font-size:20px!important
    }
    .ez-operator-detected {
        font-size:28px
    }
    div#existing-user-modal {
        width:100%
    }
    div#existing-user-modal h3 {
        font-size:46px
    }
    div#existing-user-modal h2 {
        font-size:20px!important
    }
    div#existing-user-modal p {
        margin-top:18px;
        font-size:14px
    }
    div#existing-user-modal div.ez-modal-footer button {
        width:100%
    }
}
@media all and (max-width: 379px) {
    h1 {
        font-size:28px;
        line-height:34px
    }
    .owl-item {
        margin-left:-22px
    }
    .ez-380-show {
        display:block!important
    }
    .ez-380-hide {
        display:none!important
    }
    .ez-airtime-wrap .ez-airtime-btn .ez-nocountry-placeholder {
        font-size:20px
    }
    body.ta .ez-airtime-wrap .ez-airtime-btn .ez-nocountry-placeholder {
        font-size:16px
    }
    header nav li {
        padDing:0 6px
    }
    header #ez-logo-wrap {
        margin-left:-3px
    }
    footer #ez-footer-socials a {
        margin-left:-6px
    }
    .ez-modal .ez-modal-content {
        font-size:15px;
        line-height:19px
    }
    .ez-table td {
        font-size:15px
    }
    .ez-table td span:first-child,
    .ez-table td .ez-table-bold {
        font-size:15px;
        font-weight:normal
    }
    .ez-table td.ez-table-status,
    .ez-table td.ez-table-resend {
        width:44px
    }
    #ez-cookies {
        width:100%;
        left:0;
        right:1px;
        bottom:0
    }
    #ez-cookies p {
        text-align:left
    }
    #ez-billing-details em.ez-error {
        font-size:16px
    }
    #invalid-referral-modal .ez-modal .ez-modal-header {
        padDing-bottom:26px
    }
    #invalid-referral-modal .ez-modal .ez-modal-header h3 {
        font-size:24px
    }
    #invalid-referral-modal .ez-modal .ez-modal-header p {
        font-size:13px;
        height:10px
    }
    #invalid-referral-modal .ez-modal .ez-modal-content {
        padDing-bottom:24px
    }
    #invalid-referral-modal .ez-modal .ez-modal-content h2 {
        font-size:24px!important
    }
    #invalid-referral-modal .ez-modal .ez-modal-content p {
        font-size:12px
    }
    #invalid-referral-modal .ez-modal .ez-modal-footer {
        padDing-bottom:6px
    }
    #invalid-referral-modal .ez-modal .ez-modal-footer button {
        font-size:15px;
        line-height:34px;
        height:48px
    }
    #ez-vouchers-page .ez-table tr {
        height:60px
    }
    #ez-vouchers-page .ez-mobile-menu {
        width:100%
    }
    #ez-vouchers-page .ez-mobile-menu li {
        width:50%!important;
        min-width:initial!important
    }
    #ez-vouchers-page .ez-table-voucher {
        padDing-left:6px;
        line-height:16px
    }
    #ez-vouchers-page .ez-table-usewith {
        width:120px!important
    }
    #ez-vouchers-page .ez-table-usewith a {
        font-size:17px
    }
    body.de #ez-vouchers-page .ez-mobile-menu li,
    body.ta #ez-vouchers-page .ez-mobile-menu li {
        font-size:12px
    }
    body.de #ez-vouchers-page .ez-mobile-menu li {
        font-size:11px
    }
    body.ar #ez-vouchers-page .ez-vouchers-filter-band .ez-mobile-menu li div,
    body.hi #ez-vouchers-page .ez-vouchers-filter-band .ez-mobile-menu li div,
    body.ru #ez-vouchers-page .ez-vouchers-filter-band .ez-mobile-menu li div {
        padDing-left:6px;
        padDing-right:6px
    }
    .ez-account-page .ez-mobile-menu li {
        width:33%!important;
        font-size:14px
    }
    .ez-account-page .ez-mobile-menu li:first-child {
        width:34%
    }
    #ez-account-main-action .ez-topup-btn .ez-asterisk {
        font-size:12px
    }
    #ez-account-main-action p.ez-desktop-hide {
        font-size:14px
    }
    #ez-account-tabs>li .ez-icon {
        width:38px;
        height:38px;
        line-height:38px;
        font-size:24px
    }
    #ez-contacts-wrap .ez-table .ez-button {
        padDing:0 10px
    }
    body.ru #ez-signup-with-email {
        font-size:15px
    }
    body.ta #ez-account-main-action h3.ez-desktop-hide {
        font-size:17px
    }
    body.ta #ez-account-tabs .ez-icon-text {
        font-size:15px
    }
    #ez-promos .ez-promotion {
        padDing-left:10px;
        padDing-right:10px
    }
    #ez-promos .ez-promo-btns button {
        padDing:0 10px
    }
    #ez-lanDing-prmotion h2 {
        font-size:42px!important;
        line-height:40px
    }
    #ez-lanDing-prmotion p {
        font-size:18px
    }
    #ez-localposts-container .ez-post-image img {
        margin-left:-20%
    }
    #ez-localposts-container .ez-post-content h4 {
        font-size:19px;
        line-height:22px
    }
    #ez-localposts-container .ez-post-content p {
        line-height:18px
    }
    div#existing-user-modal h3 {
        font-size:38px
    }
    div#existing-user-modal h2 {
        font-size:20px!important
    }
    div#existing-user-modal p {
        margin-top:18px;
        font-size:14px
    }
    #ez-android-page h1 {
        font-size:35px;
        line-height:39px
    }
    #ez-android-page .ez-website-link {
        font-size:17px
    }
    body.ez-paybtn-var1 #ez-new-card-btn.ez-two-btns {
        width:100%
    }
    body.ez-paybtn-var1 .ez-topup-btns button.ez-three-btns {
        width:49%!important;
        margin-right:0!important
    }
}
@media only screen and (-webkit-min-device-pixel-ratio: 2),
only screen and (min--moz-device-pixel-ratio: 2),
only screen and (-o-min-device-pixel-ratio: 2/1),
only screen and (min-device-pixel-ratio: 2),
only screen and (min-resolution: 192dpi),
only screen and (min-resolution: 2dppx) {}@media screen and (-webkit-min-device-pixel-ratio: 0) {
    nav {
        overflow:auto
    }
    nav::-webkit-scrollbar {
        display:none
    }
}
.ng-hide {
    display:none!important
}


.ez-send-topup a:hover{text-decoration: none;}

.optlogo{
    width:100%;
    height:65px;
}