.spinner,
.tp-loader.spinner0,
#fancybox-loading div {
    border-left: 3px solid rgba(245,106,125,.15);
    border-right: 3px solid rgba(245,106,125,.15);
    border-bottom: 3px solid rgba(245,106,125,.15);
    border-top: 3px solid rgba(245,106,125,.8);
}
a {
    color: #f56a7d;
}
.nocolor:hover {
    color: #f56a7d
}
.post-title a:hover {
    color: #f56a7d
}
.main-title.text-center:after {
    color: #f56a7d;
}
ul.circled li:before {
    color: #f56a7d;
}
.meta a:hover,
.more:hover {
    color: #f56a7d
}
footer a:hover {
    color: #f56a7d !important
}
.btn,
.vanilla-form label.custom-select span {
    background: #f56a7d;
}
.btn:hover,
.btn:focus,
.btn:active,
.btn.active,
.pagination ul > li > a:hover,
.pagination ul > li > a:focus,
.pagination ul > .active > a,
.pagination ul > .active > span {
    background: #e3586b;
}
.navbar .navbar-nav > li > a:focus,
.navbar .nav > li > a:hover,
.navbar .nav > li.current > a {
    color: #f56a7d !important;
}
.navbar .dropdown-menu li a:hover,
.navbar .dropdown-menu li a.active {
    color: #f56a7d !important;
}
.navbar .nav .open > a,
.navbar .nav .open > a:hover,
.navbar .nav .open > a:focus {
    color: #f56a7d;
}
.navbar .dropdown-menu > li > a:hover,
.navbar .dropdown-menu > li > a:focus,
.navbar .dropdown-submenu:hover > a,
.navbar .dropdown-submenu:focus > a,
.navbar .dropdown-menu > .active > a,
.navbar .dropdown-menu > .active > a:hover,
.navbar .dropdown-menu > .active > a:focus {
    color: #f56a7d
}
.navbar .top-bar a:hover {
	color: #f56a7d;
}
.yamm .yamm-content a:hover {
    color: #f56a7d
}
.steps .icon {
    color: #f56a7d;
}
.steps .steps-item .number {
    background: #f56a7d;
}
.steps .steps-item:hover {
    border-color: #f56a7d
}
.feature .icon {
    color: #f56a7d;
}
.icon-large {
    color: #f56a7d;
}
.owl-item .item:hover .box {
    box-shadow: 0 2px 0 #f56a7d
}
.post-types .date-wrapper {
    background: #f56a7d;
}
.post-types .post .format-wrapper {
    background: #f56a7d;
}
.sidebox a:hover {
    color: #f56a7d
}
.widget .post-list h5 a:hover {
    color: #f56a7d
}
.widget .post-list .meta em a:hover {
    color: #f56a7d
}
footer ul.tag-list li a:hover {
    background: #f56a7d !important
}
.testimonials2 .quote:hover {
    border: 2px solid #f56a7d
}
.isotope-filter ul li a:hover,
.isotope-filter ul li a.is-checked {
    color: #f56a7d
}
.isotope-filter ul li:after {
    color: #f56a7d;
}
.price {
    color: #f56a7d;
}
.progress-list li em {
    color: #f56a7d;
}
.progress.plain .bar {
    background: #f56a7d;
}
.bordered .progress.plain {
    border: 1px solid #f56a7d;
}
.bordered .progress.plain .bar {
    background: #f56a7d
}
.tabs-top.bordered .tab a {
    color: #f56a7d;
    border: 2px solid #f56a7d;
}
.tabs-top .tab a:hover,
.tabs-top .tab.active a {
    color: #f56a7d;
    box-shadow: 0 2px 0 #f56a7d;
}
.tabs-top.bordered .tab a:hover,
.tabs-top.bordered .tab.active a {
    border: 2px solid #f56a7d;
    background: #f56a7d;
}
.tabs-bottom .tab:hover .icon.box,
.tabs-bottom .tab.active .icon.box {
    box-shadow: 0 2px 0 #f56a7d
}
.panel-group .panel-active a,
.panel-group .panel-title > a:hover {
    color: #f56a7d
}
.panel-group .panel-heading .panel-title:hover,
.panel-group .panel-active .panel-heading .panel-title {
    box-shadow: 0 2px 0 #f56a7d;
    color: #f56a7d;
}
.bordered .panel-heading .panel-title {
    color: #f56a7d;
    border: 2px solid #f56a7d;
}
.bordered .panel-heading .panel-title:hover {
    background: #f56a7d;
    border: 2px solid #f56a7d;
}
.bordered .panel-title > a {
    color: #f56a7d
}
.bordered .panel-title:hover,
.bordered .panel-active .panel-heading .panel-title,
.bordered .panel-active .panel-heading .panel-title:hover {
    border: 2px solid #f56a7d;
    background: #f56a7d;
}
.tooltip-inner {
    background-color: #f56a7d;
}
.tooltip.top .tooltip-arrow,
.tooltip.top-left .tooltip-arrow,
.tooltip.top-right .tooltip-arrow {
    border-top-color: #f56a7d
}
.tooltip.right .tooltip-arrow {
    border-right-color: #f56a7d
}
.tooltip.left .tooltip-arrow {
    border-left-color: #f56a7d
}
.tooltip.bottom .tooltip-arrow,
.tooltip.bottom-left .tooltip-arrow,
.tooltip.bottom-right .tooltip-arrow {
    border-bottom-color: #f56a7d
}
.vanilla-form input[type="radio"]:focus + span,
.vanilla-form input[type="checkbox"]:focus + span,
.vanilla-form input[type="radio"]:active + span,
.vanilla-form input[type="checkbox"]:active + span {
    border-color: #f56a7d
}
.vanilla-form input[type="radio"] + span::after {
    background-color: #f56a7d;
    border-color: #f56a7d;
}
.vanilla-form input[type="radio"]:checked + span,
.vanilla-form input[type="checkbox"]:checked + span {
    border: 2px solid #f56a7d
}
.vanilla-form input[type="checkbox"] + span::after {
    border: 0 solid #f56a7d;
}
#comments .info h2 a:hover {
    color: #f56a7d
}
#comments a.reply-link:hover {
    color: #f56a7d
}
#contact-info-button:hover,
#btn-close-modal:hover {
    color: #f56a7d
}
@media (max-width: 991px) { 
	.navbar .navbar-nav .open .dropdown-menu > li > a:hover,
	.navbar .navbar-nav .open .dropdown-menu > li > a:focus {
	    color: #f56a7d !important
	}
}
@media (max-width: 767px) { 
	.isotope-filter ul li a:hover,
	.isotope-filter ul li a.is-checked {
	    box-shadow: 0 2px 0 #f56a7d !important
	}
}