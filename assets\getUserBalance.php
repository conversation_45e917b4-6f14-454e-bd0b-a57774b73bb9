<?php
// Include the database connection information
include 'mydata.php';

// Check if the username is provided
if (isset($_POST['username'])) {
    // Sanitize and get the username
    $username = mysqli_real_escape_string($conn, $_POST['username']);

    // Query to get user's balance
    $query = "SELECT balance FROM reseller WHERE username = '$username'";

    // Execute the query
    $result = mysqli_query($conn, $query);

    // Check if the query was successful
    if ($result) {
        // Check if the username exists
        if (mysqli_num_rows($result) > 0) {
            // Fetch the balance
            $row = mysqli_fetch_assoc($result);
            $balance = $row['balance'];

            // Return the balance as JSON
            echo json_encode(['success' => true, 'balance' => $balance]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Username not found']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Query failed: ' . mysqli_error($conn)]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Username not provided']);
}

// Close the database connection
mysqli_close($conn);
?>
