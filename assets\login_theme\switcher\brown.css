.spinner,
.tp-loader.spinner0,
#fancybox-loading div {
    border-left: 3px solid rgba(147,124,107,.15);
    border-right: 3px solid rgba(147,124,107,.15);
    border-bottom: 3px solid rgba(147,124,107,.15);
    border-top: 3px solid rgba(147,124,107,.8);
}
a {
    color: #937c6b;
}
.nocolor:hover {
    color: #937c6b
}
.post-title a:hover {
    color: #937c6b
}
.main-title.text-center:after {
    color: #937c6b;
}
ul.circled li:before {
    color: #937c6b;
}
.meta a:hover,
.more:hover {
    color: #937c6b
}
footer a:hover {
    color: #937c6b !important
}
.btn,
.vanilla-form label.custom-select span {
    background: #937c6b;
}
.btn:hover,
.btn:focus,
.btn:active,
.btn.active,
.pagination ul > li > a:hover,
.pagination ul > li > a:focus,
.pagination ul > .active > a,
.pagination ul > .active > span {
    background: #7c6655;
}
.navbar .navbar-nav > li > a:focus,
.navbar .nav > li > a:hover,
.navbar .nav > li.current > a {
    color: #937c6b !important;
}
.navbar .dropdown-menu li a:hover,
.navbar .dropdown-menu li a.active {
    color: #937c6b !important;
}
.navbar .nav .open > a,
.navbar .nav .open > a:hover,
.navbar .nav .open > a:focus {
    color: #937c6b;
}
.navbar .dropdown-menu > li > a:hover,
.navbar .dropdown-menu > li > a:focus,
.navbar .dropdown-submenu:hover > a,
.navbar .dropdown-submenu:focus > a,
.navbar .dropdown-menu > .active > a,
.navbar .dropdown-menu > .active > a:hover,
.navbar .dropdown-menu > .active > a:focus {
    color: #937c6b
}
.navbar .top-bar a:hover {
	color: #937c6b;
}
.yamm .yamm-content a:hover {
    color: #937c6b
}
.steps .icon {
    color: #937c6b;
}
.steps .steps-item .number {
    background: #937c6b;
}
.steps .steps-item:hover {
    border-color: #937c6b
}
.feature .icon {
    color: #937c6b;
}
.icon-large {
    color: #937c6b;
}
.owl-item .item:hover .box {
    box-shadow: 0 2px 0 #937c6b
}
.post-types .date-wrapper {
    background: #937c6b;
}
.post-types .post .format-wrapper {
    background: #937c6b;
}
.sidebox a:hover {
    color: #937c6b
}
.widget .post-list h5 a:hover {
    color: #937c6b
}
.widget .post-list .meta em a:hover {
    color: #937c6b
}
footer ul.tag-list li a:hover {
    background: #937c6b !important
}
.testimonials2 .quote:hover {
    border: 2px solid #937c6b
}
.isotope-filter ul li a:hover,
.isotope-filter ul li a.is-checked {
    color: #937c6b
}
.isotope-filter ul li:after {
    color: #937c6b;
}
.price {
    color: #937c6b;
}
.progress-list li em {
    color: #937c6b;
}
.progress.plain .bar {
    background: #937c6b;
}
.bordered .progress.plain {
    border: 1px solid #937c6b;
}
.bordered .progress.plain .bar {
    background: #937c6b
}
.tabs-top.bordered .tab a {
    color: #937c6b;
    border: 2px solid #937c6b;
}
.tabs-top .tab a:hover,
.tabs-top .tab.active a {
    color: #937c6b;
    box-shadow: 0 2px 0 #937c6b;
}
.tabs-top.bordered .tab a:hover,
.tabs-top.bordered .tab.active a {
    border: 2px solid #937c6b;
    background: #937c6b;
}
.tabs-bottom .tab:hover .icon.box,
.tabs-bottom .tab.active .icon.box {
    box-shadow: 0 2px 0 #937c6b
}
.panel-group .panel-active a,
.panel-group .panel-title > a:hover {
    color: #937c6b
}
.panel-group .panel-heading .panel-title:hover,
.panel-group .panel-active .panel-heading .panel-title {
    box-shadow: 0 2px 0 #937c6b;
    color: #937c6b;
}
.bordered .panel-heading .panel-title {
    color: #937c6b;
    border: 2px solid #937c6b;
}
.bordered .panel-heading .panel-title:hover {
    background: #937c6b;
    border: 2px solid #937c6b;
}
.bordered .panel-title > a {
    color: #937c6b
}
.bordered .panel-title:hover,
.bordered .panel-active .panel-heading .panel-title,
.bordered .panel-active .panel-heading .panel-title:hover {
    border: 2px solid #937c6b;
    background: #937c6b;
}
.tooltip-inner {
    background-color: #937c6b;
}
.tooltip.top .tooltip-arrow,
.tooltip.top-left .tooltip-arrow,
.tooltip.top-right .tooltip-arrow {
    border-top-color: #937c6b
}
.tooltip.right .tooltip-arrow {
    border-right-color: #937c6b
}
.tooltip.left .tooltip-arrow {
    border-left-color: #937c6b
}
.tooltip.bottom .tooltip-arrow,
.tooltip.bottom-left .tooltip-arrow,
.tooltip.bottom-right .tooltip-arrow {
    border-bottom-color: #937c6b
}
.vanilla-form input[type="radio"]:focus + span,
.vanilla-form input[type="checkbox"]:focus + span,
.vanilla-form input[type="radio"]:active + span,
.vanilla-form input[type="checkbox"]:active + span {
    border-color: #937c6b
}
.vanilla-form input[type="radio"] + span::after {
    background-color: #937c6b;
    border-color: #937c6b;
}
.vanilla-form input[type="radio"]:checked + span,
.vanilla-form input[type="checkbox"]:checked + span {
    border: 2px solid #937c6b
}
.vanilla-form input[type="checkbox"] + span::after {
    border: 0 solid #937c6b;
}
#comments .info h2 a:hover {
    color: #937c6b
}
#comments a.reply-link:hover {
    color: #937c6b
}
#contact-info-button:hover,
#btn-close-modal:hover {
    color: #937c6b
}
@media (max-width: 991px) { 
	.navbar .navbar-nav .open .dropdown-menu > li > a:hover,
	.navbar .navbar-nav .open .dropdown-menu > li > a:focus {
	    color: #937c6b !important
	}
}
@media (max-width: 767px) { 
	.isotope-filter ul li a:hover,
	.isotope-filter ul li a.is-checked {
	    box-shadow: 0 2px 0 #937c6b !important
	}
}