<?php
function sendflexi($title,$body,$id=""){
define('API_ACCESS_KEY','AAAA8Ou5jsY:APA91bG_ezafBI6D3vTrwDH0sqFrOWIBDu2tkb3iq1ZMDccKL4QuDpki-0cahZZZZL0pCfx2sEyTqWXvgWassy-iilqxObickbQ1VPO2fJw1iroHYEiAiwqooiklrmaK57h56b2gLQck');
if(empty($id)){$id='199999999999';}
$dt = new DateTime('now', new DateTimezone('Asia/Dhaka')); 
 $dt->modify('+3 minutes'); 


	$exp=$dt->format('m/d/Y H:i:s');

$domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);

 
     
$dbhost         = "localhost"; 
$dbuser         = "adminbdc_pay"; 
$dbpassword     = "adminbdc_pay"; 
$dbname         = "adminbdc_pay";


$mascon = ($GLOBALS["___mysqli_ston"] = mysqli_connect("$dbhost", "$dbuser", "$dbpassword")); 
((bool)mysqli_query( $mascon , "USE " . $dbname))  ;


	$sql_master = mysqli_query($GLOBALS["___mysqli_ston"], "SELECT `lic` FROM `domain_access` where `domain_name` ='$domain' and status=1"); 
                    $total=mysqli_num_rows($sql_master); 
                  $rowops=mysqli_fetch_array($sql_master);
             
             
                $applic= $rowops['lic']; 



 $fcmUrl = 'https://fcm.googleapis.com/fcm/send';
 $token='token';

     $notification = [
            'title' =>$title,
            'body' => $body,
            'time_to_live' => '300', 
              //'icon' =>'myIcon', 
           // 'sound' => 'mySound'
        ];
       $extraNotificationData = ["message" => $notification,"id" => $id,"exp" => $exp,"moredata" =>'dd'];

        $fcmNotification = [
            //'registration_ids' => $tokenList, //multple token array
            'to' => '/topics/'.$applic.'',
            'notification' => $notification,
            'data' => $extraNotificationData
        ];

        $headers = [
            'Authorization: key=' . API_ACCESS_KEY,
            'Content-Type: application/json'
        ];


        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL,$fcmUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fcmNotification));
        $result = curl_exec($ch);
        curl_close($ch);
return $result;

}



/*
iRecharge 
 */
 
class Recharge extends CI_Model 
{
	
	function __construct(){
        parent::__construct(); 
        date_default_timezone_set('Asia/Dhaka');
	
	
    }
	
	 private function date_time(){
        //$timezone_offset = +6; // BD central time (gmt+6) for me
		//date_default_timezone_set("Asia/Dhaka");
        return $create_date = date('Y-m-d H:i:s');
     }
	 
	
	
		   
	public function recharge($service,$number,$amount,$type="",$appsuser="",$pincode="",$pincodetype="",$country="",$opid="",$flremark="",$name="",$nidcode="",$senderno="",$pcode="",$appversion="",$drive="") {
	    
	    
	    if(!empty($drive) && $service=='16384'){
	        
	        
	    
				$Qu6=true;
				
		$amount = $this->db->get_where('net_package',array('id' =>$drive))->row()->price;
		
		$comm_f = $this->db->get_where('net_package',array('id' =>$drive))->row()->comm;
		 $charge_f=$this->db->get_where('net_package',array('id' =>$drive))->row()->charge;    
	        
	        
	    }
	    
	    if(!empty($drive) && $service=='64'){
			$apiuserq = "Select * from drive_package where id='$drive' and status='1' order by id asc limit 1"; 
				$queryrtsload=$this->db->query($apiuserq);
				
				$Qu=$queryrtsload->num_rows();
		$amount = $this->db->get_where('drive_package',array('id' =>$drive))->row()->price;
		
		$comm_f = $this->db->get_where('drive_package',array('id' =>$drive))->row()->comm;
		 $charge_f=$this->db->get_where('drive_package',array('id' =>$drive))->row()->charge;
		
			
				
	    }else if(empty($appversion) && $service=='64'){
	        
	     	$apiuserq = "Select * from drive_package where price='$amount' and status='1' order by id asc limit 1";    
	     	
	     		$queryrtsload=$this->db->query($apiuserq);
	        	$Qu=$queryrtsload->num_rows();
	    }
		
			
			
			
			  
			   $balance_system = $this->db->get_where('security_option',array('id' =>1))->row()->balance_system;
			   
			   $drive_system = $this->db->get_where('security_option',array('id' =>1))->row()->drive;
			   
		if(($service!=64 && $service!=16384) && ($balance_system==1)){
		  $source='bank';  
		}
		
		if(($service==64 or $service==16384) && $Qu> 0 && $drive_system==1) {
               
             $source='drive';
            }
		
		
		if(empty($source))$source='main';
          
          
       

			$member = $this->session->userdata('member_session');
			 
			 if(!empty($appsuser)) {
			 	$uid = $appsuser;
			 }else {
			 	$uid = $member->id;
			 }
			 	$recharge = $this->db->get_where('reseller',array('id' =>$uid))->row()->recharge;
			 	
		$parent = $this->db->get_where('reseller',array('id' =>$uid))->row()->p_id;
		
		if($source=='bank'){
		    
		    	$prebalance = $this->db->get_where('reseller',array('id' =>$uid))->row()->bank_balance;
		$mybalance = $this->db->get_where('reseller',array('id' =>$uid))->row()->bank_balance;
		}else if($source=='drive'){
		    
		    	$prebalance = $this->db->get_where('reseller',array('id' =>$uid))->row()->drive_bal;
		$mybalance = $this->db->get_where('reseller',array('id' =>$uid))->row()->drive_bal;
		}else{
		$prebalance = $this->db->get_where('reseller',array('id' =>$uid))->row()->balance;
		$mybalance = $this->db->get_where('reseller',array('id' =>$uid))->row()->balance;
		}
		if(empty($source) or $source=='main'){
	  $balancelimit = $this->db->get_where('reseller',array('id' =>$uid))->row()->balance_limit;
        } else{$balancelimit =0;}
			
			
			 
			$create_date=date('Y-m-d H:i:s');
			$idate=date('Y-m-d');
			$update_date=date('Y-m-d H:i:s');
			$ip= $_SERVER['REMOTE_ADDR'];
			 
			 
			// service list
		$sqlmod="SELECT * from module where serviceid='$service' and status=1"; 
		$queryser = $this->db->query($sqlmod);

					foreach ($queryser->result() as $rowmod)
					{
						$service_auto_limit=$rowmod->auto_limit; 
						$requr_pin=$rowmod->requr_pin;
						$requr_nid=$rowmod->nid; 
						$requr_name=$rowmod->name; 
						$requr_sender_number=$rowmod->sender_number; 
						$minamount=$rowmod->min_amount; 
						$maxamount=$rowmod->max_amount; 
					//	$maxamount='5000';
						$minlength=$rowmod->min_length; 
						$maxlength=$rowmod->max_length; 
						$servicetitle=$rowmod->title; 
					}
			// service list finish
			
			
		$mobile=trim($number);
		if($service==64 or $service==16384){ 
		$firstThreeDigit=substr($mobile,0,3); 
		}else { 
		$firstThreeDigit=substr($mobile,0,2); 
		}  
		
			if(!empty($pcode)){	
			    
			    if($pcode=="GP"){ 
		$firstThreeDigit="017";
		}
		
		 if($pcode=="SK"){ 
		$firstThreeDigit="113";
		}
		
		if($pcode=="RB"){ 
		$firstThreeDigit="018";
		}
			    
		if($pcode=="BL"){ 
		$firstThreeDigit="019";
		}	    
		
		
		if($pcode=="AT"){ 
		$firstThreeDigit="016";
		}
		if($pcode=="TT"){ 
		$firstThreeDigit="015";
		}
	
	if($pcode=="AR"){ 
		$firstThreeDigit="016";
		}
		
if($pcode=="TE"){ 
		$firstThreeDigit="015";
		}
		
			    
			}
		
		$numberlenth=strlen($number); 
		
		// only service status
		$myacc_tarf = $this->db->get_where('reseller',array('id' =>$uid))->row()->tarif;
	if($service==64 or $service==512){ 
		
		if($service==64){ 
		
		$country_opstatus=$this->db->get_where('country_op',array('oprefix' =>$firstThreeDigit))->row()->status;
		}
		if($service==512){ 
		
		$country_opstatus=$this->db->get_where('country_op',array('oprefix' =>$opid))->row()->status;
		}
		if($country_opstatus==0) {	
		$irs_data = array(
						   'irsts' => 'error',
						   'irsmessage'=>'Oparetor Off at this time'
						   
						 );
		}else {
			
		}
		
		}		
		// only service status
		
		
		
		
		
		
					 
						 
					
		
		
		
		
		
		
		if($service==512) {
			
$sql_rate="SELECT * from price where service='512' and country='$country' and oparetor='$opid'";
			
		}else {
		
		$sql_rate="SELECT * from price where service='$service' and prefix='$firstThreeDigit' and type='$type'"; 
		}
		 
		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{
						 $mrservice=$raterow->service; 
						 $prefix=$raterow->prefix; 
						 $rate=1; 
						 $comm=$raterow->comm; 
						 $charge=$raterow->charge; 
						 $prcode=$raterow->pcode;
					}						 
					
					if(empty($prcode))$prcode=$pcode;	 
      $amus=floor($amount);
      
      
      
       if(!empty($drive) && $service=='64'){
			$sql_ratef = "Select * from drive_package where id='$drive' and status='1' order by id asc limit 1"; 
			

		$Qu2=true;
			
	    }else if(empty($appversion) && $service=='64'){
      
		$sql_ratef="SELECT * from drive_package where volume='$prcode' and price='$amus' and status='1'"; 
			$Qu2=true;
	    }
		 
		 if($Qu6){
		     $comm='0';
		  $charge='0';
		     
		 }
		 
		 if($Qu2){
		 
		$queryratef = $this->db->query($sql_ratef);
		  foreach ($queryratef->result() as $raterowf)
					{
					   
					    
						
						 $comm_f=$raterowf->comm; 
						 $charge_f=$raterowf->charge; 
						
					}
					
		 }
					
if(!empty($comm_f))$comm='0';
if(!empty($charge_f))$charge='0';					
      
      
						 $ratesrate=$amount*$rate; 
						 $comm_rate=$amount*$comm/100; 
      	 $comm_rate=$comm_f+$comm_rate;
						 $charge_rate=$amount*$charge/100; 
						 $final_amount=$ratesrate+$charge_rate-$comm_rate; 
					if($service=='524288'){
					if($amount<=400){
    $ch='5';
    
    
} else if ($amount<=1500 && $amount>=401){
     $ch='10';
  
    
} else if ($amount<=5000 && $amount>=1501){
     $ch='15';
  
    
}else{
    // $ch=($amount*0.01);
   $ch='25';
    
}
$final_amount=($final_amount+$ch);					
	$charge_rate=($charge_rate+$ch);				}				
					
			$intrvalt = $this->db->get_where('security_option',array('id' =>1))->row()->req_Interval*60;
			$bkash_auto = $this->db->get_where('security_option',array('id' =>1))->row()->bkash_auto;
			
			$reseller_dayli_limit = $this->db->get_where('security_option',array('id' =>1))->row()->reseller_dayli_limit;
			
			$min=$this->mit->checkMobile($number,$service); 
			
			$numberchksuc = $this->db->get_where('sendflexi',array('service' =>$service,'phone' =>$number))->row()->status;



			$sqllmt="select balance,idate from sendflexi where userid='$uid' and idate='$idate'";
			$querylmt = $this->db->query($sqllmt);
					  foreach ($querylmt->result() as $row24)
					{
						
						$limitdate=$row24->userid; 
						$limitbl=$row24->balance; 
						$agrebal+=$limitbl; 
					}
					
						 // parent 
		 $parent1 = $this->db->get_where('reseller',array('id' =>$parent))->row()->p_id;
		 $parent2 = $this->db->get_where('reseller',array('id' =>$parent1))->row()->p_id;
		 $parent3 = $this->db->get_where('reseller',array('id' =>$parent2))->row()->p_id;
		 $parent4 = $this->db->get_where('reseller',array('id' =>$parent3))->row()->p_id;
		 $parent5 = $this->db->get_where('reseller',array('id' =>$parent4))->row()->p_id;
		 $parent6 = $this->db->get_where('reseller',array('id' =>$parent5))->row()->p_id;
		 $parent7 = $this->db->get_where('reseller',array('id' =>$parent6))->row()->p_id;
		 // parent 
		 
		 if($service==64 or $service==16384 or $service==512) {
		 
		$mobile=trim($number); 
		$firstThreeDigit=substr($mobile,0,3); 
	if(!empty($pcode)){	
			    
			    if($pcode=="GP"){ 
		$firstThreeDigit="017";
		}
		 if($pcode=="SK"){ 
		$firstThreeDigit="113";
		}
		
		if($pcode=="RB"){ 
		$firstThreeDigit="018";
		}
			    
		if($pcode=="BL"){ 
		$firstThreeDigit="019";
		}	    
		
		
		if($pcode=="AT"){ 
		$firstThreeDigit="016";
		}
		if($pcode=="TT"){ 
		$firstThreeDigit="015";
		}
		
		if($pcode=="AR"){ 
		$firstThreeDigit="016";
		}
		
if($pcode=="TE"){ 
		$firstThreeDigit="015";
		}	    
			}
		if($firstThreeDigit=="017"){ 
		$operator="gp";
		}if($firstThreeDigit=="013"){ 
		$operator="gp";
		}if($firstThreeDigit=="019"){ 
		$operator="blink";
		}if($firstThreeDigit=="014"){ 
		$operator="blink";
		}if($firstThreeDigit=="018"){ 
		$operator="robi"; 
		}if($firstThreeDigit=="016"){ 
		$operator="airtel"; 
		}if($firstThreeDigit=="015"){ 
		$operator="teletalk"; 
		}if($firstThreeDigit=="011"){ 
		$operator="CC"; 
		}
		
		
		
		
		if($firstThreeDigit=="017"){ 
		$optn="GP";
		}if($firstThreeDigit=="013"){ 
		$optn="GP";
		}if($firstThreeDigit=="019"){ 
		$optn="BL";
		}if($firstThreeDigit=="014"){ 
		$optn="BL";
		}if($firstThreeDigit=="018"){ 
		$optn="RB"; 
		}if($firstThreeDigit=="016"){ 
		$optn="AT"; 
		}if($firstThreeDigit=="015"){ 
		$optn="TT"; 
		}if($firstThreeDigit=="011"){ 
		$optn="CC"; 
		}
		if($firstThreeDigit=="113"){ 
		$optn="SK"; 
		}
		
		
		
		
		
			}else {
				
			$operator=$prcode; 
				
			}
		
		 $sql_route = "SELECT * FROM `routing` WHERE active='1' and status='0' and service='$service' and (pcode='$prcode' or pcode='all') order by priority asc"; 
		 $queryroute = $this->db->query($sql_route);
		 foreach ($queryroute->result() as $rowroute)
					{
						$route_id      =$rowroute->id; 
						$route_pcode   =$rowroute->pcode; 
						$priority      =$rowroute->priority; 
						$route_service =$rowroute->service; 
						$routes        =$rowroute->route; 
						
					}
					
					
				$sql_api="SELECT * FROM `api_set` WHERE id='$routes' and status=1"; 

				$queryapifl = $this->db->query($sql_api);
				foreach ($queryapifl->result() as $row_api)
					{

				$api_id=$row_api->id; 
				$provider=$row_api->provider; 
				$flapi_userid=$row_api->userid; 
				$flapi_key=$row_api->api_key; 

				$api_url=$row_api->url; 
				$apirespons=$row_api->response; 
					}
					
				if($amount<=$bkash_auto) {
				if($routes=="manual") {
				$local="5";
				$statusup="5";
				$apidisable = "5";
				$local="0";
				$statusup="0";
				}
				}else {
				$statusup="5";
				$local="5";
				$apidisable = "5";
			
				}
				if($amount<=$service_auto_limit) {
				$local="0";
				$statusup="0";
				}else {
				$statusup="5";
				$local="5";
				$apidisable = "5";
				
			
				
				}
				
      $da = $this->db->get_where('company',array('id' =>1))->row()->zip;
       $regu = $this->db->get_where('company',array('id' =>1))->row()->city;
       
       
       if($regu==1 && $service==16384){
       	$statusup="5";
				$local="5";
				$apidisable = "5";    
           
       }
       
       
       
       
      if($da==1){
          
           if(!empty($drive)){
			$apiuserq = "Select * from drive_package where id='$drive' and status='1' order by id asc limit 1"; 
			
	$queryrtsload=$this->db->query($apiuserq);
		
		$Qu3=$queryrtsload->num_rows();	
	    }else if(empty($appversion)){
          
      	$apiuserq = "Select * from drive_package where price='$amount' order by id asc limit 1"; 
      	
      		$queryrtsload=$this->db->query($apiuserq);
      			$Qu3=$queryrtsload->num_rows();	
		
	    }
		
		
		
			
			if($Qu3> 0) {


			$statusup="5";
				$local="5";
				$apidisable = "5";
			





			}
      }
      
      
      
      
	$apiuserb = "Select * from block_list where service = '$service' and amount='$amount' and SUBSTR(pcode, 1, 2)='$prcode'"; 
		
			$queryrtsloadb=$this->db->query($apiuserb);
			
			if($queryrtsloadb->num_rows()> 0) {
			$block="1";
			
			
			}           
      
      
      
      
				if($pincode=="api") {
				$rendomid=$country;
				}else {
				$rendomid = uniqid();
				}
		
					$availablebal=$mybalance-$balancelimit;
					//$topuser=$this->mit->countBalance($uid,$final_amount); 	
					//$blanc_chk=$this->mit->reverse_treeBalanceChk($uid,$amount,$service,$prcode); 
				if(empty($final_amount))$final_amount=$amount;
					if($block!=1){
					if($availablebal>=$final_amount && is_numeric($amount) && ($final_amount > 0)){
			
						
				if($amount>=$minamount && $amount<=$maxamount) { 
       
				if($numberlenth>=$minlength && $numberlenth<=$maxlength) {
				
				if($min>$intrvalt || $min==0){
					
				if($agrebal<$reseller_dayli_limit) {
					
				if($numberchksuc!=3 or $numberchksuc!=1) {
					
				

$blanc_chk=$this->mit->reverse_treeBalanceChk($uid,$amount,$service,$prcode);
if($service=='64'){

	$apiuser = "Select * from security_option where id='1' and ((SUBSTR(GP, 1, 2)='$prcode' or SUBSTR(BL, 1, 2)='$prcode' or SUBSTR(RB, 1, 2)='$prcode' or SUBSTR(AT, 1, 2)='$prcode' or SUBSTR(TT, 1, 2)='$prcode' or SUBSTR(SK, 1, 2)='$prcode')) order by id asc limit 1"; 
		
			$queryrtsload=$this->db->query($apiuser);
			
			if($queryrtsload->num_rows()== 0) {
			    $blanc_chk=FALSE;
			    
			}
}


if($blanc_chk=="TRUE") { 
    if($recharge==1){

							if ($service==512) { 
							$number=$phonecode.$number;
							}else {
							$number=$number;
							}

	
	
					if($pincodetype=="yes") {
							
					$utrye = 'user';
					
					if($requr_pin==0) {  
					$chking=true; 
					}else if($pincode=="ets") {
					$chking=true; 
					$flremark == 'etross';
					}else if($pincode=="api") {
					$chking=true; 
					$flremark == 'api';
					}else { 
					    
					$chking=$this->mit->otpchk($pincode,$utrye,$uid); 
					
					    
					} 
					if($flremark=='offline')$chking=true; 
					
					
					if($chking) {
						
						
				
					
					if($service==524288){
				$number=substr($number, 2);
					}
					
					$sou=$source;
					
					if(empty($sou)){$sou='main';}
						$sql="INSERT INTO `sendflexi` (`id`, `sid`, `route`, `route_id`, `userid`, `admin_id`, `lft`, `rgt`, `ip`, `p_id`, `phone`, `sender_no`, `name`, `nid`, `type`, `balance`, `cost`, `submitted_date`, `idate`, `service`, `remark`, `send_time_stamp`, `parent_cost`, `status`, `operator`, `pcode`, `prebalance`, `local`, `refund`, `result`, `tarif_id`, `api`, `level`, `level2`, `level3`, `level4`, `level5`,`commision`,`send_number`) VALUES (NULL, '$rendomid', '$routes', '$route_id', '$uid', '$admin_id', '$lft', '$rgt', '$ip', '$parent', '$number', '$senderno', '$name', '$nidcode', '$type', '$amount', '$final_amount', '$create_date', '$idate', '$service', '$flremark', '$update_date', '$parent_cost', '$statusup', '$operator', '$prcode', '$prebalance', '$local', '', '$sou', '$myacc_tarf', '0', '$parent', '$parent1', '$parent2', '$parent3', '$parent4','$comm_f','$drive')";
				
				$sendinsert = $this->db->query($sql);
				
				$idlast = $this->db->insert_id();
				
				if($sendinsert) {
				    
				    if($statusup==5){
				        
				      	$subjet = "Your Waiting number ";
				
				$messages = "your Waiting number ".$number. " and amount ".$amount;
				
				$this->send_email($subjet,$messages);  
				        
				      function curl_url($url)
{
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, $url);

    $data = curl_exec($ch);
    curl_close($ch);

    return $data;
}

	       	 $contact_no1 =$this->db->get_where('company',array('id' =>1))->row()->contact_no1;
		 
		
$details="Waiting no: $number $prcode Amount: $amount";
			 
$domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);
$url="http://$domain/offline/sms?pa=5463243&to=$contact_no1&message=".urlencode($details).""; 

    $data=curl_url($url); 		
					

					
			

  
				        
				    }
				    
				    
				    
				    
				
				if($type=='1'){$ty=""; }
				if($type=='2'){$ty=""; }



if (strpos($amount, ".") !== false) {

$parts = explode('.', $amount);
$amounty=$parts[0];

       
    }else{
$amounty=$amount;
}
if(!empty($optn)){
$body=''.$optn.''.$ty.'*'.$number.'*'.$amounty.'';

}else{
	if($type=='1'){$ty=""; }
				if($type=='2'){$ty=""; }

$prco=substr($prcode, 0, 2);

$body=''.$prco.''.$ty.'*'.$number.'*'.$amounty.'';
$optn=$prco;

}


if($service=='128' OR $service=='256' OR $service=='131072'){
     if($service=='131072')$prco='ND';
    
    
    
    if($type=='1'){$ty=""; }
				if($type=='2'){
				    
				    $ty="P"; 
				    $typp="C";  
				    
				}
    
if($type=='3'){
				    
				    $ty="P"; 
				    $typp="S";  
				    
				}
    
    
    	if($type=='4'){
				    
				    $ty=""; 
				    $typp="B";  
				    
				}
    
    $body=''.$prco.''.$ty.'*'.$number.'*'.$amounty.'*'.$prco.''.$typp;
    

    
    
    
}

 $modem = $this->db->get_where('security_option',array('id' =>1))->row()->modem;
if($modem==0){
if($service=='65536'){
			
   if($amounty=='50')$tr='1'; 
   if($amounty=='100')$tr='2'; 
   if($amounty=='200')$tr='3'; 
   if($amounty=='500')$tr='4'; 
   if($amounty=='1000')$tr='5'; 
   
$body='INT*TR 0088'.$number.'*TR '.$tr.'*01690208';

$query = $this->db->query("SELECT * FROM `sendflexi` WHERE `status` = '0' AND `service`='65536'");
 $n=$query->num_rows();
 
if($n=='1' or $statusup!=5){
    //$resp=sendflexi("flexiload","$body",'0');
    
    $sqlre="insert into `response` (`body`,`id_order`) values('$body','$idlast')";
  $sendinsertre = $this->db->query($sqlre);  
    
}else{
$sqlre="insert into `response` (`body`,`id_order`) values('$body','$idlast')";
$sendinsertre = $this->db->query($sqlre);}				
}else{
    	if($statusup!=5){
    	   // $resp=sendflexi("flexiload","$body",'0');
    	   
    $sqlre="insert into `response` (`body`,`id_order`) values('$body','$idlast')";
$sendinsertre = $this->db->query($sqlre);
    	}		
//$optn=$optn;
		
			
}
				if($statusup==5){
$resp="Waiting";

    $sqlre="insert into `response` (`msg`,`body`,`id_order`) values('$resp','$body','$idlast')";
  $sendinsertre = $this->db->query($sqlre);  
    
}
				
				$idlastr = $this->db->insert_id();
			
				
				
}	
					$this->mit->treeBalanceUpdate($uid,$number,$amount,$service,$rendomid,$prcode,$idlast,"minus",$final_amount,$sou); 
			
				//$this->mit->reverse_treeBalanceUpdate($uid,$number,$amount,$service,$rendomid,$prcode,$idlast,"minus"); 
				
				//if($service==128){
				$this->send_emailnew($number,$amount);
				//}
				if($route_service==$service)  { 
				
				if($apidisable!=5 ) {
				    
				    if($provider==11){
				    
				    if($pcode=="SK"){ $op='3';  $type="3";}else{
			$op=substr($firstThreeDigit, -1);
				 }
			$pdata = array(
				  "number" => $number,
				  "service" => 'MRC',
				  "amount" => $amount,
				  "number_type" => $type,
				  "refid" => $rendomid,
				  "access_id" => $flapi_userid,
				  "access_pass" => $flapi_key,
				   "operator" => $op
				);
				
				
			
					$url_send="http://$api_url/myportal/api/rechargeapi/recharge_api_thirdparty.php";
				
		$api_status=$this->mdb->sendPostData($url_send,$pdata);
				
					$apists=json_decode($api_status);
				$responsests = $apists->RECHARGE_STATUS;
				    
				        
				    }else{
				$postdata = array(
				  "number" => $number,
				  "service" => $service,
				  "amount" => $amount,
				  "type" => $type,
				  "id" => $rendomid,
				  "user" => $flapi_userid,
				  "key" => $flapi_key,
				  "operator" => $pcode
				);
				
			

				$url_send ="http://".$api_url."/sendapi/request";
				//$postdata = json_encode($data);
				
				  $header=array(
    'api-key: '.$flapi_key.'',
    'api-user: '.$flapi_userid.''
);
				$api_status=$this->mdb->sendPostData($url_send,$postdata,$header);
				
				
				$apists=json_decode($api_status);
				$responsests = $apists->status;
				    }
				    
				    
				    
			
				 
				if($responsests==1 or $responsests=='RECEIVED') 
                        { 
                             
                        $apiupd = "Update sendflexi SET status='4', apiurl='$api_url', apiresponse='$api_status' where sid='$rendomid'";
						
						$this->db->query($apiupd);
						
                        } else{	 $apiupd = "Update sendflexi SET apiurl='$api_url', apiresponse='$api_status' where sid='$rendomid'";
				 
				 	$this->db->query($apiupd);}
						
						
				
				    
				    
				    
				    
				    
			//end	    
				}
				
				}else{
				    
				    
				     $modem = $this->db->get_where('security_option',array('id' =>1))->row()->modem;
  if($modem==0){$this->mdb->real_time($idlast);}
				    
				    
				}
				
				$irs_data = array(
						   'irsts' => 'success',
						   'irsmessage'     => $servicetitle.'- '.$tcom.' Request Submit Successfully'
						   
						 );
						 
						 
				}else {
					
					
						$irs_data = array(
						   'irsts' => 'error',
						   'irsmessage'     => ' Request was Not Receive'
						   
						 );
					
				}
						}else {
						$irs_data = array(
						   'irsts' => 'error',
						   'irsmessage'     => 'Wrong pin'
						   
						 );
					}
							
							
						}else {
					
						$irs_data = array(
						   'irsts' => 'success',
						   'irsmessage'     => 'verify'
						   
						 );
						}
					
				
			}else{	$irs_data = array(
						   'irsts' => 'error',
						   'irsmessage'     => 'You Have no permission'
						   
						 );}	}else { /// product Not pound
				
				$irs_data = array(
						   'irsts' => 'error',
						   'irsmessage'     => 'Dear User Your Product Not Found'
						   
						 );
						 
				}
				
				}else { /// number lenth min max
				
				$irs_data = array(
						   'irsts' => 'error',
						   'irsmessage'     => 'Dear User Your Number Already Request'
						   
						 );
						 
				}
				
				}else { /// number lenth min max
				
				$irs_data = array(
						   'irsts' => 'error',
						   'irsmessage'     => 'Dear User Your Daily Limit Over'
						   
						 );
						 
				}
					
					
				}else { /// intval time chk lenth min max
					
				$remain=$intrvalt-$min;
					
				$irs_data = array(
						   'irsts' => 'error',
						   'irsmessage'     => 'Please Request This Mobile '.$number.' after '.$remain.' Seconds'
						   
						 );
					
				}
				
				
					
				}else { /// number lenth min max
					
				$irs_data = array(
						   'irsts' => 'error',
						   'irsmessage'     => 'Minimum Number: '.$minlength. ' And Maximum Number: '. $maxlength
						   
						 );
					
				}
				}else { /// amount min max
				$irs_data = array(
						   'irsts' => 'error',
						   'irsmessage'     =>  'Minimum Amount:' .$minamount.'  And Maximum Amount: '.$maxamount
						   
						 );
					
				}
						


				

					
				}else { /// salf balance chk
				
				$irs_data = array(
						   'irsts' => 'error',
						   'irsmessage'     => 'Insufficient balance'
						   
						 );
					
				}
					
	}else { /// salf balance chk
				
				$irs_data = array(
						   'irsts' => 'error',
						   'irsmessage'     => 'This Amount can not recharge now'
						   
						 );
					
				}
		
		
		$this->session->set_userdata('irslog',$irs_data);
		
		}
		
		public function rechargeService($service,$number,$amount,$uid,$pcode,$type="",$pincode="",$pincodetype="", $name="",$area="",$note="",$opn) {
		  $balance_system = $this->db->get_where('security_option',array('id' =>1))->row()->balance_system;
			   
			   $drive_system = $this->db->get_where('security_option',array('id' =>1))->row()->drive;
			   
		if(($service!=64 or $service!=16384) && $balance_system==1){
		  $source='bank';  
		}
		
		if(($service==64 or $service==16384) && $Qu> 0 && $drive_system==1) {
               
             $source='drive';
            }
		
		
		if(empty($source))$source='main';
		
		$rendomid = uniqid();
		
		$create_date=date('Y-m-d H:i:s');
		$idate=date('Y-m-d');
		$update_date=date('Y-m-d H:i:s');
		$ip=$_SERVER['REMOTE_ADDR'];
		
		$parent = $this->db->get_where('reseller',array('id' =>$uid))->row()->p_id;
		
		if($source=='bank'){
		    
		    	$prebalance = $this->db->get_where('reseller',array('id' =>$uid))->row()->bank_balance;
		$mybalance = $this->db->get_where('reseller',array('id' =>$uid))->row()->bank_balance;
		}else{
		$prebalance = $this->db->get_where('reseller',array('id' =>$uid))->row()->balance;
		$mybalance = $this->db->get_where('reseller',array('id' =>$uid))->row()->balance;
		}
		
		$balancelimit = $this->db->get_where('reseller',array('id' =>$uid))->row()->balance_limit;
	
			 
		// service list
		$sqlmod="SELECT * from module where serviceid='$service' and status=1"; 
		$queryser = $this->db->query($sqlmod);

					foreach ($queryser->result() as $rowmod)
					{
						$service_auto_limit=$rowmod->auto_limit; 
						$requr_pin=$rowmod->requr_pin;
						$requr_nid=$rowmod->nid; 
						$requr_name=$rowmod->name; 
						$requr_sender_number=$rowmod->sender_number; 
						$minamount=$rowmod->min_amount; 
						$maxamount=$rowmod->max_amount; 
						
						$minlength=$rowmod->min_length; 
						$maxlength=$rowmod->max_length; 
						$servicetitle=$rowmod->title; 
					}
			// service list finish
			
				// only service status
		$myacc_tarf = $this->db->get_where('reseller',array('id' =>$uid))->row()->tarif;
		
		
		
		
			
		$sql_ratef="SELECT * from com_price where service='$service' and prefix='$firstThreeDigit' and type='$type' and price='$amount'"; 
	
		 
		$queryratef = $this->db->query($sql_ratef);
		  foreach ($queryratef->result() as $raterowf)
					{
						
						 $comm_f=$raterowf->comm; 
						 $charge_f=$raterowf->charge; 
						
					}						 
						 
					
			
		
		$sql_rate="SELECT * from price where service='$service' and pcode='$pcode'"; 
		 
		$queryrate = $this->db->query($sql_rate);
		  foreach ($queryrate->result() as $raterow)
					{
						 $mrservice=$raterow->service; 
						 $prefix=$raterow->prefix; 
						 $rate=$raterow->rate; 
						 $comm=$raterow->comm; 
						 $charge=$raterow->charge; 
						 $prcode=$raterow->pcode;
					}	
					
					
					
					
						 
						 $ratesrate=$amount*$rate; 
						 $comm_rate=$amount*$comm/100; 
						 $charge_rate=$amount*$charge/100; 
						 $final_amount=$ratesrate+$charge_rate-$comm_rate;
if(empty($final_amount))$final_amount=$amount;
	
			if($service=='524288' or $service=='8'){
					if($amount<=400){
    $ch='5';
    
    
} else if ($amount<=1500 && $amount>=401){
     $ch='10';
  
    
} else if ($amount<=5000 && $amount>=1501){
     $ch='15';
  
    
}else{
    // $ch=($amount*0.01);
   $ch='25';
    
}
$final_amount=($final_amount+$ch);					
	$charge_rate=($charge_rate+$ch);				}	
		
		
		
					 // parent 
		 $parent1 = $this->db->get_where('reseller',array('id' =>$parent))->row()->p_id;
		 $parent2 = $this->db->get_where('reseller',array('id' =>$parent1))->row()->p_id;
		 $parent3 = $this->db->get_where('reseller',array('id' =>$parent2))->row()->p_id;
		 $parent4 = $this->db->get_where('reseller',array('id' =>$parent3))->row()->p_id;
		 $parent5 = $this->db->get_where('reseller',array('id' =>$parent4))->row()->p_id;
		 $parent6 = $this->db->get_where('reseller',array('id' =>$parent5))->row()->p_id;
		 $parent7 = $this->db->get_where('reseller',array('id' =>$parent6))->row()->p_id;
		 
		 $numberlenth=strlen($number); 
				$availablebal=$mybalance-$balancelimit;
				
				if($availablebal>=$final_amount && is_numeric($amount)){
			
						
				if($amount>=$minamount && $amount<=$maxamount) { 
       
				if($numberlenth>=$minlength && $numberlenth<=$maxlength) {
				
				
									
$blanc_chk=$this->mit->reverse_serviceBalanceUpdate($uid,$number,$final_amount,$service,$rendomid,$prcode,"chk"); 
if($blanc_chk=="TRUE") { 
			
					
					/// request here 
					if($pincodetype=="yes") {
							
					$utrye = 'user';
					
					if($requr_pin==0) {  
					$chking=true; 
					}else { 
					$chking=$this->mit->otpchk($pincode,$utrye,$uid); 
					} 
					
					$chking=$this->mit->otpchk($pincode,$utrye,$uid); 
					
					
					if($chking) {
					if($service==32) {
				
					$sql="INSERT INTO `bank_transfer` (`id`, `user_id`, `admin_id`, `prebal`, `bank_name`, `branch`, `date`, `s_date`, `holdername`, `account`, `balance`, `cost`, `tarif`, `type`, `remark`, `status`, `sid`, `ip`, `level`, `level2`, `level3`, `level4`, `level5`) VALUES (NULL, '$uid', '1', '$prebalance', '$pcode', '$area', '$create_date', '$idate', '$name', '$number', '$amount', '$final_amount', '$myacc_tarf', '$type', '$note', '0', '$rendomid', '$ip', '$parent1', '$parent2', '$parent3', '$parent4', '$parent5');";
						}else {					
					
					$sql="INSERT INTO `bill_pay` (`id`, `user_id`, `admin_id`, `prebal`, `provider_name`, `area`, `date`, `s_date`, `holdername`, `account`, `balance`, `cost`, `tarif`, `type`, `remark`, `status`, `sid`, `ip`, `level`, `level2`, `level3`, `level4`, `level5`) VALUES (NULL, '$uid', '1', '$prebalance', '$pcode', '$area', '$create_date', '$idate', '$name', '$number', '$amount', '$final_amount', '$myacc_tarf', '$type', '$note', '0', '$rendomid', '$ip', '$parent1', '$parent2', '$parent3', '$parent4', '$parent5');";
						}					
					
					
					$sendinsert = $this->db->query($sql);
				
				$idlast = $this->db->insert_id();
				
				if($sendinsert) {
				
				$this->mit->reverse_serviceBalanceUpdate($uid,$number,$final_amount,$service,$rendomid,$prcode,"minus"); 
				
			
				
						$irs_data = array(
						   'irserv' => 'success',
						   'irsmessage'     => $servicetitle.'- '.$tcom.'Request Submit Successfully'
						   
						 );
				}else {
					
					
						$irs_data = array(
						   'irserv' => 'error',
						   'irsmessage'     => ' Request Was Not Receive'
						   
						 );
					
				}
				
					
					}else {
						$irs_data = array(
						   'irserv' => 'error',
						   'irsmessage'     => 'Rong pin nambar please give balid pin Nambar'
						   
						 );
					}
					
					}else {
					
						$irs_data = array(
						   'irserv' => 'success',
						   'irsmessage'     => 'verify'
						   
						 );
						}
						
						}else { /// product Not pound
				
				$irs_data = array(
						   'irserv' => 'error',
						   'irsmessage'     => 'Dear User Your Product Not Found'
						   
						 );
						 
				}
					
					
					
					
				
				
				}else { /// number lenth min max
					
				$irs_data = array(
						   'irserv' => 'error',
						   'irsmessage'     => 'Minimum Number: '.$minlength. ' And Maximum Number: '. $maxlength
						   
						 );
					
				}
				}else { /// amount min max
				$irs_data = array(
						   'irserv' => 'error',
						   'irsmessage'     =>  'Minimum Amount:' .$minamount.'  And Maximum Amount: '.$maxamount
						   
						 );
					
				}
						


				

					
				}else { /// salf balance chk
				
				$irs_data = array(
						   'irserv' => 'error',
						   'irsmessage'     => 'Insufficient Balance In Your Account Please Recharge And Trey Agen'
						   
						 );
					
				}
					
	
		$this->session->set_userdata('irservicelog',$irs_data);
		
		}
	
		public	function send_emailnew($number, $amount){
		    
		    /*
		$to = $this->db->get_where('reseller',array('id' =>'1'))->row()->email;
		  $this->load->library('email');
             $config = Array(
            'protocol' => 'smtp',
            'smtp_host' => 'localhost',
            'smtp_port' => 25,
            'smtp_user' => '<EMAIL>',
            'smtp_pass' => 'rafiqvai@1122',
            'mailtype'  => 'html'
            );
               
$this->email->initialize($config);  
  
$this->email->set_newline("\r\n");
                  $server_email = "<EMAIL>"; 
                   $admin_email = $to;
         $this->email->from($server_email, 'EZZILOAD'); 
         $this->email->to($admin_email);
         
         $this->email->subject('New Request'); 
          $this->email->message("Number $number with amount $amount TK requested from your server.");
                $this->email->send();
*/	}
		
		public	function send_email($subject, $message){
		
		
		$to = $this->db->get_where('reseller',array('id' =>'1'))->row()->email;
		$company_name = $this->db->get_where('company',array('id' =>'1'))->row()->company_name;
		$company_title = $this->db->get_where('company',array('id' =>'1'))->row()->company_title;
      
        $headers = "From: ".$company_title." <".$company_name."> \r\n";
        $headers .= "Reply-To: ".$company_title." <".$company_name."> \r\n";
        $headers .= "MIME-Version: 1.0\r\n";
        $headers .= "Content-Type: text/html; charset=ISO-8859-1\r\n";
        
       if(!empty($to)) {
		   
         mail($to, $subject, $message, $headers);
	   }
        
	}
	
	

}
