* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

html,
body {
    margin: 0;
    background: rgb(2,0,36);
    background: linear-gradient(-135deg, #c850c0, #4158d0); 
   /* background: linear-gradient(90deg, rgba(2,0,36,1) 0%, rgba(28,42,69,1) 33%, rgba(0,212,255,1) 100%);*/
    font-family: "Poppins", sans-serif;
}

#socia_particles {
    z-index: 4;
    height: 100vh;
    background: #010713;
}

.socia_sec {
    position: absolute;
    z-index: 5;
    top: 10%;
    right: 0;
    left: 0;
    margin: 0 auto;
}

.logo {
    width: 30%;
    margin-top: -15%;
}

.ratio {
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    height: 0;
    padding-bottom: 100%;
    position: relative;
    width: 100%;
    box-shadow: 0px 9px 10px rgba(181, 181, 181, 0.38);
}

.img-circle {
    border-radius: 50%;
    background-color: white;
    background-size: contain;
    background-repeat: no-repeat;
    border: 1px solid #028dff;
}

.img-responsive {
    display: block;
    height: auto;
    max-width: 100%;
}

.cancel {
    position: relative;
    margin-top: -15%;
    padding: 3px;
}

.cancel>a {
    font-size: 22px;
    color: #028dff;
}

.cancel>a:hover {
    color: red !important;
}

.btn-link {
    color: #000000;
    font-size: 15px;
}

.btn-link:focus {
    color: #028dff;
    text-decoration: none;
    outline: none !important;
    box-shadow: none !important;
}

.btn-link:hover {
    text-decoration: none;
    color: #028dff;
}

#headingOne {
    padding: 8px !important;
}

#headingTwo {
    padding: 8px !important;
}

#headingThree {
    padding: 8px !important;
}

a {
    color: black;
}

a:hover {
    background-color: transparent;
    text-decoration: none;
    color: #028dff;
}

#cardbtn {
    color: #ffffff;
    text-decoration: none;
}

#cardbtn:hover {
    color: #000000;
    background-color: #028dff !important;
    text-decoration: none;
}

#cardbtn div {
    font-size: 13px !important;
}

.optionsCard {
    background-color: #028dff !important;
}

.cardService {
    font-weight: 500;
    text-decoration: none;
}

.mobileCard {
    font-weight: 500;
    text-decoration: none;
}

.content-title {
    color: #262626;
    padding-left: 10px;
    padding-right: 10px;
}

.support-sec-content {
    border: 1px solid #f4f4f4;
    margin-left: 10px;
    margin-right: 10px;
    background-color: #f4f4f4;
    margin-bottom: 5px;
    border-radius: 10px;
}

.support-sec-content img {
    width: 15%;
    padding: 10px;
}

.support-sec-content span {
    color: #262626;
    font-size: 14px;
}

.faq-option-sec {
    margin-top: 5%;
    padding-left: 10px;
    padding-right: 10px;
}

.optionsMobile {
    background-color:#028dff;
}

.sectionBtn:hover {
    color: #ffffff;
    background-color: #399ff1 !important;
    text-decoration: none;
}

.grayscale {
    filter: url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\'><filter id=\'grayscale\'><feColorMatrix type=\'matrix\' values=\'0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0\'/></filter></svg>#grayscale");
    /* Firefox 3.5+ */
    filter: gray;
    /* IE6-9 */
    -webkit-filter: grayscale(100%);
    /* Chrome 19+ & Safari 6+ */
    transition: all .2s ease-in-out;
    -webkit-transition: all .2s ease-in-out;
    cursor: pointer;
}

.grayscale:hover {
    filter: none;
    -webkit-filter: grayscale(0%);
    transition: all .2s ease-in-out;
    -webkit-transition: all .2s ease-in-out;
    cursor: pointer;
}

.pgw-box {
    transition: all .2s ease-in-out;
    -webkit-transition: all .2s ease-in-out;
    transform: scale(1.0);
}

.pgw-box:hover {
    transition: all .2s ease-in-out;
    -webkit-transition: all .2s ease-in-out;
    transform: scale(1.05);
}

.pay {
    margin-top: 10px;
    padding-bottom: 18px;
    font-size: 17px;
    font-weight: 600;
    background-color: #028dff;
    color: white;
    border: none !important;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.top-options-img {
    width: 25px;
    height: 25px;
}

#support-text,
#translate-support-text,
#details-text {
    margin-bottom: 5px;
    font-size: 14px !important;
}

#bangla-support-text,
#bangla-translate-support-text,
#bangla-details-text {
    margin-bottom: 5px;
    font-size: 14px !important;
}

.body-card {
    width: 80%;
    border: none;
    border-radius: 10px;
}

.prl-cus-15 {
    padding-left: 15px !important;
    padding-right: 15px !important;
}

/* Extra small devices (phones, 600px and down) */
@media only screen and (max-width: 600px) {
    .body-card {
        width: 100%;
    }

    .prl-cus-15 {
        padding-left: 2px !important;
        padding-right: 15px !important;
    }

    #cardbtn div {
        font-size: 11px !important;
    }
}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {
    .body-card {
        margin-top: 20px;
        width: 80%;
    }

    #cardbtn div {
        font-size: 11px !important;
    }
}

/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {
    .body-card {
        width: 70%;
    }
}

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {
    .body-card {
        width: 90%;
    }
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {}

.pb-0 {
    padding-bottom: 0px !important;
}

.pt-10 {
    padding-top: 10px;
}

.pt-20 {
    padding-top: 20px;
}

.prl-0 {
    padding-left: 0px !important;
    padding-right: 0px !important;
}

.mrl-0 {
    margin-left: 0px !important;
    margin-right: 0px !important;
}

.pt-5 {
    padding-top: 5px !important;
}

.pr-0 {
    padding-right: 0px !important;
}

.pl-0 {
    padding-left: 0px !important;
}

.prl-10 {
    padding-right: 10px;
    padding-left: 10px;
}

.prl-15 {
    padding-right: 15px;
    padding-left: 15px;
}

.prl-20 {
    padding-right: 20px;
    padding-left: 20px;
}

.mb-5 {
    margin-bottom: 5px;
}

.p-7{
    padding: 7px;
}

.mb-2{
    margin-bottom: 2px;
}

.mb-3{
    margin-bottom: 3px;
}

.mt-5 {
    margin-top: 5px;
}

.mt-2{
    margin-top: 2px;
}

.mt-3 {
    margin-top: 3px;
}

.mr-2 {
    margin-right: 2px;
}