<?php
defined('BASEPATH') OR exit('No direct script access allowed');
function str2($amount,$number,$pin,$ussd){
    			

$vars = array(
  '%amount%'       => $amount,
  '%number%'        => $number,
  '%pin%' => $pin
);

$usd= strtr($ussd, $vars);
$newString = str_replace(" ","",$usd);
if (strlen($newString) <= 0) {
$usd="no"; }
return $usd; 
}
class Modemcontest extends CI_Controller {
	
	function __construct()
    {
        parent::__construct();
        
        date_default_timezone_set('Asia/Dhaka');
     
		$this->load->library('form_validation');
		$this->load->library('user_agent');
    }
	
	 private function date_time(){
        return $create_date = date('Y-m-d H:i:s');
     }
	 

	
	
	
	public function index()
	{
	
	$dt = new DateTime('now', new DateTimezone('Asia/Dhaka')); 
	$last_login=$dt->format('j F Y g:i A'); 
	$idate=$dt->format('Y-m-d');

	if($_POST) {
	$this->form_validation->set_rules('username', 'Userid', 'trim|required|min_length[3]|max_length[30]');
	$this->form_validation->set_rules('password', 'Password', 'trim|required|min_length[4]|max_length[30]');
   
     
        if ($this->form_validation->run() == FALSE) {
		
		
		 $response = array(
					'success' => true,
					'errors'  => 'not empty',
					'message' => 'Please Enter Correct Login Info.'
					);
					
		}else {
			
	$username = $this->security->xss_clean($this->input->post('username'));
	$password = $this->security->xss_clean($this->input->post('password'));
	$deviceid = $this->security->xss_clean($this->input->post('deviceid'));
	
	
	if(!empty($username) && !empty($password) && !empty($deviceid) ) {
	//$this->load->model('company');
        $result = $this->mit->login($username,$password);
		 if ($result) {
		 $ucid=$result->id;
		 $logincount=$result->login_count;
		 
		$activests =$result->status;
		$uname =$result->username;
		$custype =$result->custype;
		$appsAccess=$result->appsAccess;
		 
		$loginplus = $logincount+1;
		 
		
		
		$sqlcont="SELECT * FROM device_list WHERE tokenid='$deviceid' and userid='$ucid'";

		$querycount = $this->db->query($sqlcont);
		
		foreach ($querycount->result() as $row3){
		  	 $mysafe_status=$row3->status;
		  	  $myfsttoken=$row3->tokenid;
		  	   $tblremember=$row3->remember;
		  	  
		  	  }
		
		
		if($logincount>0){

		 	$access='no';


		 }else {
		 	$access='yes';

		 }
		 

		if($mysafe_status==1 || $access=='yes') {
			 
		if($appsAccess==1) {
		 
		 if($activests==0) { 
		 
		 $response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'This ID is Block.'
					);
					
		 
		 }else {
		 
		 
		 $loginupdate = array(
						"ip" => $_SERVER['REMOTE_ADDR'],
						"login_count" => $loginplus,
						"last_login" => $this->date_time()
					);
		 
		 $this->mdb->update('reseller',$loginupdate,array('id'=>$ucid));
		 
		$ctype="user";
		$logtype="apps";
		$secretcode=sha1(uniqid(rand(1111111,9999999),true) ); 
		$this->mit->insertLog($ucid,$ctype,$secretcode,$logtype);
		//$this->session->set_userdata('member_session', $result);
		
		$msg= 'Successful Login';
		$this->mit->InsertActivetUser($ucid,$msg,'Apps');
		
		
		$query = $this->db->query("select * from apps_key where userid='$ucid'");
		$time= time();

		if($query->num_rows() == 0 ) {
		
		$applog_key_insert = "INSERT INTO `apps_key` (`id`, `userid`, `token`, `date`, `time`, `deviceid`, `status`) VALUES (NULL, '$ucid', '$secretcode', '$last_login', '$time', '$deviceid', '1')";
		
		$this->db->query($applog_key_insert);
		
		}else {
			
	
			 
			 $data = array(
					'token' => $secretcode, 
					'time' => $time,
					'deviceid' => $deviceid,
					'date' => $last_login
					);
					$this->db->where('userid',$ucid);
					
					$this->db->update('apps_key',$data);

			$bq="UPDATE `apps_key` SET `accesscount` = accesscount+1 WHERE `apps_key`.`userid` = '$ucid'"; 

			$this->db->query($bq);					
					
					//$this->mdb->update('apps_key', $apiupdate, array('userid'=>$ucid));
		}
		
		$response = array(
					'success' => true,
					'status'  => 1,
					'token' =>$secretcode, 
					'username' =>$uname,
					'otp' => 1,
					'message' => 'Successful Login.'
					);

		
		 
		 }
		 }else {
		$msg= 'Your Apps Access Not Allow';
		$response = array(
					'success' => false,
					'status'  => 2,
					'message' => $msg
					);		
			 
		 }
		 }else {
			 
		$remember=0;
      	$this->mit->devicelist($ucid,$remember,'yes',$deviceid,'Apps');
      	$msg= 'Access Denai';
		$trytype = 'password';
		
		$this->mit->increase_login_attempts($username,$password,$msg,$trytype);
			 
			 $response = array(
					'success' => false,
					'status'  => 2,
					'message' => $msg
					);
			 
		 }
		 
		 } else {
		
		$msg= 'Invalid ID and password';
		//$this->mit->InsertActivetUser($username,$msg);
		
		$this->mit->increase_login_attempts($username,$password,$msg);
		
		  $response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Invalid ID and password.'
					);
		 }
		 
		 }else {
			 $response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Please enter ID and password.'
					);
					
		 
		 }
		}
		}else {
			
			$response = array(
					'success' => false,
					'status'  => 2,
					'message' => 'Empty Not Work.'
					);
			
			
		}
		
		
		/* Output header */ 
			header('Content-type: application/json'); 
			echo json_encode($response); 
			 
		// 
			
	}
	
		public function updateres(){
		if($_POST) {	

	
	$get=$this->input->get();
		$var=$this->input->post();
				$pass = $var['pass'];
				
					
			$str = $var['body'];
		$sender = $var['sender'];
	    
			
 

  $queryi = "UPDATE `sendflexi` SET `apiresponse`='$str' WHERE `id` ='$sender'";
  
	
   $this->db->query($queryi);

   
 
				



echo 'suc'; 
			//header('Content-type: application/json'); 
			
		//cho json_encode($response); 
			
		//	$this->sendtoapi();
}
	
	}	



	
	
	
	
	

	public function update(){
		if($_POST) {
		    function paymentsimBalance($msg){
$searchd = preg_match_all("/Balance ([a-zA-Z0-9]+)(.+)\.[\w]/isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    if(empty($searchd)){
        $searchd = preg_match_all("/Balance: ([a-zA-Z0-9]+)(.+)\.[\w]/isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
        
    }

$int = str_replace('Balance', '', $matche[0][0]);
$int = str_replace('is', '', $int);
$int = str_replace('TK', '', $int);
$int = str_replace('Tk', '', $int);
$int = str_replace(',', '', $int);
$int = str_replace(':', '', $int);
$int = str_replace(' ', '', $int);
$int=utf8_encode(money_format('%.0n', floor($int)));
return $int; 
}
		    
		     function trxidn($msg){
preg_match('[TxnID:]', $msg,$m);

$searchd = preg_match_all("/$m[0].([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
$int = str_replace('Balance', '', $pieces[1]);
$int = str_replace('Bal', '', $int);
$int = str_replace(':', '', $int);
$int= trim(preg_replace('/\s\s+/', ' ', $int));
return $int; 
}


function trxid2($msg){
preg_match('[TxnId]', $msg,$m);

$searchd = preg_match_all("/$m[0].([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
$pieces = explode(":",$matche[0][0]);
$int = str_replace(' ', '', $pieces[1]);
return $int; 
}


function trxidbk($msg){
preg_match('[TrxID]', $msg,$m);

$searchd = preg_match_all("/$m[0].([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);

return $pieces[1]; 
}
		 function fee($msg){
$searchd = preg_match_all("/Fee ([a-zA-Z0-9]+)(.+)\.[\w]/isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    if(empty($searchd)){
        $searchd = preg_match_all("/Fee: ([a-zA-Z0-9]+)(.+)\.[\w]/isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
        
    }if(empty($searchd)){
        $searchd = preg_match_all("/Fee:([a-zA-Z0-9]+)(.+)\.[\w]/isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
      //print_r($matche);  
    }
    
    
 
$int = str_replace('Fee', '', $matche[0][0]);
$int = str_replace('is', '', $int);
$int = str_replace('TK', '', $int);
$int = str_replace('Tk', '', $int);
$int = str_replace(',', '', $int);
$int = str_replace(':', '', $int);
$int=utf8_encode(money_format('%.0n', floor($int)));
return $int; 
}
		    
		    
		  function bill_contact($msg){
$searchd = preg_match_all("/contact no: ([a-zA-Z0-9]+)(.+)\./isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    $int= $pieces[2];
$int = str_replace('.', '', $int);
 $int = mb_substr($int, 2);
$pis = explode(",",$msg);
$pi = explode(" ",$pis[0],2);
    
return "$int ".strtolower($pi[1]).""; 
}  
		    
function curl_bill($url)
{
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, $url);

    $data = curl_exec($ch);
    curl_close($ch);

    return $data;
}

function bill_contact_name($msg){

$piecesd = explode(",",$msg);
    $ints= $piecesd[0];
$ints = str_replace('Congrats', '', $ints);

$pieces = explode(":",$msg);
    $int= $pieces[1];
$int = str_replace(' ', '', $int);
$msisdn = preg_replace("/^(00)?(88)?0/", "", $int);
  
$tr=strtolower("0$msisdn $ints");
return $tr; 
}
		function bill_account($msg){
$searchd = preg_match_all("/Account no: ([a-zA-Z0-9]+)(.+)\./isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    $int= $pieces[2];
$int = str_replace('.', '', $int);
return $int; 
}

	function bill_id($msg){
preg_match('#is \((.*?)\)#', $msg, $match);
return $match[1];
}


	function billpin($msg){
preg_match('#Pin\((.*?)\)#', $msg, $match);
return $match[1];
}


function billno($msg){
preg_match('#No\((.*?)\)#', $msg, $matchs);
$no=$matchs[1];
if(empty($no)){
    
  preg_match('#Number\((.*?)\)#', $msg, $match);
$no=$match[1];  
}
if(empty($no)){
    
  preg_match('#number\((.*?)\)#', $msg, $match);
$no=$match[1];  
}
return $no;
}


function billamount($msg){
preg_match('#Amount\((.*?)\)#', $msg, $match);
return $match[1];
}
function billtrnxid($msg){
$searchd = preg_match_all("/Transaction Id:([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
$pieces = explode(":",$matche[0][0]);
    $int= $pieces[1];
$int = str_replace('.', '', $int);
return $int; 
}	    
		    
 function trxidtel($msg){
preg_match('[ID is]', $msg,$m);

$searchd = preg_match_all("/$m[0].([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);

return $pieces[2]; 
}
function ratettel($msg){
preg_match('[with]', $msg,$m);

$searchd = preg_match_all("/$m[0].([0-9]+)(.+)\./isU",$msg,$matche);
$int = filter_var($matche[0][0], FILTER_SANITIZE_NUMBER_INT);
if(strlen($int)>8)return false;
return $int; 

}

function mnumbertele($msg){
 

$searchd = preg_match_all("/$m[0].([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);

//return $pieces[1]; 
 
return "0".$matche[0][3];

}


function mnumberteleold($msg){
 
$search = preg_match_all("/(\d+|\-|\+|\(|\)|\ ){0,}(1)(\d+|\ |\-){9,10}/",$msg,$matches);

    $msg = str_replace(' ', '', $matches[0][1]);
 
return "0".$msg;

}


function skito($msg){
$searchd = preg_match_all("/recharge ([a-zA-Z0-9]+)(.+)\.[\w]/isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    
$int = str_replace('recharge', '', $pieces[1]);

$int = filter_var($int, FILTER_SANITIZE_NUMBER_INT);
if(strlen($int)>8)return false;
return $int; 
}

function simBalanceal($msg){
$searchd = preg_match_all("/balance is ([a-zA-Z0-9]+)(.+)\.[\w]/isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    
$int = str_replace('balance is', '', $matche[0][0]);

$int = str_replace('TK', '', $int);

return $int; 
}

function simBalanceal2($msg){
$searchd = preg_match_all("/balance is TK ([a-zA-Z0-9]+)(.+)\./isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    
$int = str_replace('balance is TK', '', $matche[0][0]);

$int = str_replace('.', '', $int);

return $int; 
}

function mnumbergp($msg){
 
$search = preg_match_all("/(\d+|\-|\+|\(|\)|\ ){0,}(1)(\d+|\ |\-){8,14}/",$msg,$matches);

    $msg = str_replace(' ', '', $matches[0][0]);
   $m = mb_substr($msg, 0, 1);
if($m!='0'){
 $msg='0'.$msg.'';}else{ $msg=$msg;}
 
 if(strlen($msg)>12){
     $msg = str_replace(' ', '', $matches[0][1]);
   $m = mb_substr($msg, 0, 1);
if($m!='0'){
 $msg='0'.$msg.'';}else{ $msg=$msg;}
     
     
 }else{ $msg=$msg;}
return $msg;

}   


function trxidrobib($msg){
preg_match('[ID]', $msg,$m);

$searchd = preg_match_all("/$m[0].([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);

$pieces = explode(" ",$matche[0][0]);
    $int= $pieces[1];
$pieces = explode("Your",$pieces[1]);
    $int= $pieces[0];
return $int; 
}


 function mnumber($msg){
 
$search = preg_match_all("/(\d+|\-|\+|\(|\)|\ ){0,}(1)(\d+|\ |\-){8,14}/",$msg,$matches);

    $msg = str_replace(' ', '', $matches[0][0]);
 
return $msg;

}

function trxidas($msg){
$searchd = preg_match_all("/Ref: ([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    $int= $pieces[1];
$int = str_replace('.', '', $int);
return $int; 
}

function trxid($msg){
$searchd = preg_match_all("/TrxId:([0-9]+)(.+)\./isU",$msg,$matche);
return $matche[0][0]; 
}

function trxidbl($msg){
preg_match('[ID]', $msg,$m);

$searchd = preg_match_all("/$m[0].([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
$pieces = explode(" ",$matche[0][0]);
    $int= $pieces[1];
return $int; 
}
function trxidrobi($msg){
preg_match('[is]', $msg,$m);

$searchd = preg_match_all("/$m[0].([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);

$pieces = explode(" ",$matche[0][1]);
    $int= $pieces[1];
$pieces = explode("Your",$pieces[1]);
    $int= $pieces[0];
return $int; 
}
function timeget($msg){
$searchd = preg_match_all("/@([0-9]+)(.+)\:/isU",$msg,$matche);
  $msg = str_replace('@', '', $matche[0][0]);
 
return $msg;
}

function rate($msg){
preg_match('[BK|DB|GP|GPP|RB|RBP|BL|BLP|AT|ATP|TT|TTP]', $msg,$m);

$searchd = preg_match_all("/$m[0].([0-9]+)(.+)\./isU",$msg,$matche);
$int = filter_var($matche[0][0], FILTER_SANITIZE_NUMBER_INT);
if(strlen($int)>8)return false;
return $int; 

}

function rate2($msg){
preg_match('[BK|DB|GP|GPP|RB|RBP|BL|BLP|AT|ATP|TT|TTP|TK|BDT]', $msg,$m);

$searchd = preg_match_all("/$m[0].([0-9]+)(.+)\./isU",$msg,$matche);
$int = filter_var($matche[0][0], FILTER_SANITIZE_NUMBER_INT);
if(strlen($int)>8)return false;
if(empty($int)){
    preg_match('[BK|DB|GP|GPP|RB|RBP|BL|BLP|AT|ATP|TT|TTP|Tk|BDT]', $msg,$m);

$searchd = preg_match_all("/$m[0].([0-9]+)(.+)\./isU",$msg,$matche);
$int = filter_var($matche[0][0], FILTER_SANITIZE_NUMBER_INT);
if(strlen($int)>8)return false;
    
    
}
return $int; 

}
function rate3($msg){
$pieces = explode(" ", $msg);
    $int= $pieces[5];
$int= preg_replace('/Tk/', '', $int);
$int = explode(".", $int);
    $int= $int[0];
$int= preg_replace('/,/', '', $int);
  if(!is_numeric($int)){
   $int= $pieces[0];
$int= preg_replace('/Tk/', '', $int);
$int = explode(".", $int);
    $int= $int[0];
$int= preg_replace('/,/', '', $int);
  
  
  }
return $int; 

}

function rate8($msg){
$pieces = explode(" ", $msg);
$int = filter_var($pieces[1], FILTER_SANITIZE_NUMBER_INT);
if(strlen($int)>8)return false;
return $int; 

}

function ratebl($msg){
$pieces = explode(" ", $msg);
$int = filter_var($pieces[4], FILTER_SANITIZE_NUMBER_INT);
if(strlen($int)>8)return false;
return $int; 

}
function ratear($msg){
$pieces = explode(" ", $msg);
$int = filter_var($pieces[1], FILTER_SANITIZE_NUMBER_INT);
if(strlen($int)>8)return false;
return $int; 

}
	
	$get=$this->input->get();
		$var=$this->input->post();
				$pass = $this->db->escape_like_str($var['pass']);
			$str = $this->db->escape_like_str($var['body']);
		$sender = $this->db->escape_like_str($var['sender']);
	    	$appreffer = $this->db->escape_like_str($var['ref']);
				$dt = new DateTime('now', new DateTimezone('Asia/Dhaka')); 
	$create_date=$dt->format('j F Y g:i A'); 
	$idate = $dt->format('Y-m-d'); 

		

$simBalancealance=simBalanceal($str);
if(empty($simBalancealance)){
$simBalancealance=simBalanceal2($str);    
    
}

$simbalance=$simBalancealance;




if (preg_match('/ok/isU', $str)){ 
   $pieces = explode(" ",$str);
    $number= $pieces[1];
     $pin= $pieces[2];
$billorder=$this->mdb->getData('sendflexi',array('phone'=>$number,'status'=>4)); 



$type=$billorder[0]['type'];
if($type==2){
  
$msg="REGS YES $pin";
$domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);
$url="http://$domain/offline/sms?pa=5463243&type=bill&to=727&message=".urlencode($msg).""; 

    $data=curl_bill($url);   
    
    
    
   //echo  $data;
}
}













if (preg_match('/727/isU', $sender)){
$str=html_entity_decode($str);
if (preg_match('/successfully registration completed for REB/isU', $str)){ 

 $number= bill_contact($str) ;
 	$bid=bill_id($str);
     
  
   $strignupt = "UPDATE `sendflexi` SET `phone`='$bid', `status`='0', `local`='0',`type`='1' WHERE LOWER(`name`)='$number' and `status`='4' and `local`='4' and `type`='2'"; 
 $this->db->query($strignupt);
    
 
}

if (preg_match('/REGS<>acc_no<>contact_no<>name/isU', $str)){ 
   
$number= bill_account($str) ;

$billorder=$this->mdb->getData('sendflexi',array('phone'=>$number,'status'=>4)); 
  $type=$billorder[0]['type'];
 
 
 if($type==2){
   $no= $billorder[0]['phone'];
  $na=$billorder[0]['name'];
$msg="REGS $no $na";
$domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);
$url="http://$domain/offline/sms?pa=5463243&type=bill&to=727&message=".urlencode($msg).""; 

    $data=curl_bill($url);   
    
    
 // echo  $data;
}
 
 
}

if (preg_match('/pin is sent/isU', $str)){ 

 $number= billno($str) ;
 $billorder=$this->mdb->getData('sendflexi',array('phone'=>$number,'status'=>4)); 
       $type=$billorder[0]['type'];
       
      
 if($type==2){
     
   $nom= $billorder[0]['phone'];
   $pieces = explode(" ",$billorder[0]['name'],2);
    $na= $pieces[1];
     $no= $pieces[0];
  
$msg="Dear $na, Your Received a pin code. To confirm, type ok<sp>$nom<sp>pin_no and reply this sms";
$domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);
$url="http://$domain/offline/sms?pa=5463243&to=$no&message=".urlencode($msg).""; 

    $data=curl_bill($url);   
    
    
 // echo  $data;
}

}

if (preg_match('/Yes/isU', $str)){ 
   
$number= billno($str);
$rate=billamount($str);
$pin=billpin($str);
$billorder=$this->mdb->getData('sendflexi',array('phone'=>$number,'status'=>4)); 
}


$amo=$billorder[0]['balance'];
$type=$billorder[0]['type'];
if($type==1){
 if($rate==$amo){
$msg="YES $pin %pin%";
$domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);
$url="http://$domain/offline/sms?pa=5463243&type=bill&to=727&message=".urlencode($msg).""; 

    $data=curl_bill($url);   
   //echo  $data;
}
}






 if (preg_match('/SUCCESSFUL|success|Successful|Transaction/isU', $str)){ 

$number= billno($str);
$rate=billamount($str);
$trnx=billtrnxid($str);
$work_done="done";

}

}



if (preg_match('/FlexiLoad|Skitto/isU', $sender)){

$number= mnumbergp($str);

if (preg_match('/Skitto/isU', $sender)){
$rate=skito($str);
}else{
$rate=rate2($str);
}
$trnx=trxidbl($str);
 if (preg_match('/SUCCESSFUL|success|successful|Transaction/isU', $str)){ 
$work_done="done";
}

}


if (preg_match('/461|492|iTopUP/isU', $sender)){

$number= mnumbergp($str);
$rate=ratebl($str);
$trnx=trxidbl($str);


 if (preg_match('/SUCCESSFUL|success|successful|accepted/isU', $str)){ 

$work_done="done";

}

}




if (preg_match('/8383/isU', $sender)){

$number= mnumbergp($str);
$rate=rate8($str);
if($rate==null){
$rate=ratear($str);
}
$trnx=trxidrobi($str);

 if (preg_match('/SUCCESSFUL|accepted|success|successful|commission/isU', $str)){
     
    
$work_done="done";

}

}





if (preg_match('/telecharge/isU', $sender)){

$number= mnumbertele($str);
if($number==0){$number=mnumberteleold($str);}
$rate=ratettel($str);
$trnx=trxidtel($str);


 if (preg_match('/SUCCESSFUL|successfully|successful|accepted/isU', $str)){ 
$work_done="done";


}

}



if (preg_match('/bKash/isU', $sender)){

$number= mnumber($str);
$rate=rate2($str);
$ctrnx=trxidbk($str);
 if (preg_match('/Comm|success|successful/isU', $str)){
  $work_done="done";   
  $trnx = $this->db->get_where('company',array('id' =>'1'))->row()->bkash;
  
  
 }
    
}


if (preg_match('/16216/isU', $sender)){

$number= mnumber($str);
$rate=rate3($str);
$ctrnx=trxid2($str);
 if (preg_match('/Comm|success|successful/isU', $str)){
$work_done="done";
$trnx = $this->db->get_where('company',array('id' =>'1'))->row()->rocket;
  


}

}













if (preg_match('/NAGAD/isU', $sender)){

$number= mnumber($str);
$rate=rate2($str);
$ctrnx=trxidn($str);
 if (preg_match('/Comm|success|Successful/isU', $str)){
$work_done="done";
$trnx = $this->db->get_where('company',array('id' =>'1'))->row()->nogad;
  


}

}


if(!empty($work_done)){
    
 	 $modem = $this->db->get_where('security_option',array('id' =>1))->row()->modem;
  if($modem==1){$stype='4';}else{$stype='0';}
    
 if(empty($rate)){
     
 

 $orde=$this->mdb->getData('sendflexi',array('phone'=>$number,'status'=>$stype));
     
 }else{
     
     $orde=$this->mdb->getData('sendflexi',array('phone'=>$number,'status'=>$stype,'balance'=>$rate)); 
   
 }   
    
$oid=$orde[0]['userid'];
$id=$orde[0]['id'];
$com=$orde[0]['commision'];
	$ank_p = $this->db->get_where('reseller',array('id' =>$oid))->row()->username;
	
		$ank_fb = $this->db->get_where('reseller',array('id' =>$oid))->row()->facebook;
		
		$ank_p_id = $this->db->get_where('reseller',array('id' =>$oid))->row()->p_id;
  if(empty($simbalance))$simbalance="not update";
   

  $queryi = "UPDATE `sendflexi` SET `status`='1',`actual_send_time`='$create_date',`trxid`='$trnx',`simBalance`='$simbalance' WHERE `id` ='$id' AND `status`='$stype'";
  
		$ifexds=$this->db->query($queryi);
  if($ife){	if($com!=0){
      	$direct_parent = $this->db->get_where('reseller',array('id' =>$oid))->row()->p_id;
      
      	$prebalance=$this->mit->accountBalance($oid); 

      
      $t="plus"; 
		$balchange = $this->mit->balanceUpdate($oid,$com,$t);
			$bal=$this->mit->accountBalance($oid); 
      
		 $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `service`, `flex_id`, `p_id`, `date`, `time`) VALUES (NULL, '$oid', 'Commision for  $number', '$prebalance', '0', '$com', '$bal', 'plus', 'transfer', '$trnx', '$direct_parent', '$idate', '$create_date')"; 

		 $this->db->query($sql_tr);

	$type="Transfer";
		$sql="INSERT INTO `pay_receive` (`id`, `trxid`, `userid`, `desc`, `remark`, `sender`, `p_id`, `credit`, `account`, `type`, `transfer_type`, `idate`, `date`) VALUES (NULL, '$decideid', '$oid', 'Commision By admin $description', 'By Admin', 'Admin', '$direct_parent', '$com', '$bal', '$type', 'recharge', '$idate', '$create_date')";

		$this->db->query($sql);
      
      
      $owne = $this->db->get_where('reseller',array('id' =>$oid))->row()->username;
      
      	$robis=substr($owne , 0, 2);
if($robis=='88'){
    
$msg ="You have Received Commision Tk $com New BDT $bal";
    
 	$re=sends("flexiload","SMS*$owne*$msg*DD");
    

}
      
      
      
  }
  }
  
  $ank_name = $this->db->get_where('reseller',array('id' =>$oid))->row()->name;
   $ank_name_r = $this->db->get_where('reseller',array('id' =>$ank_p_id))->row()->name;
 
   if($orde[0]['status']==0){}
       $pb=$orde[0]['prebalance'];
       $co=$orde[0]['cost'];
       $remark=$orde[0]['remark'];
  $pe=($pb-$co);
 if($ank[name]==NULL){$an=$ank_name_r;}else{$an=$ank_name;}
 $bal=$orde[0]['balance'];
 $msg="$an TrxId $trnx No $number to recharge $bal Tk  successful Cost $co, New BDT $pe";
$robi=substr($ank_p , 0, 2);
if($remark=='offline'){
    
    function curl_l($url)
{
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, $url);

    $data = curl_exec($ch);
    curl_close($ch);

    return $data;
}

$domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);
$url="http://$domain/offline/sms?pa=5463243&to=$ank_p&message=".urlencode($msg).""; 

    $data=curl_l($url); 
    /* $rendomid = uniqid();
 $sqll="INSERT INTO `sendflexi` (`id`, `sid`,`phone`, `smstext`, `pcode`,`status`,`local`) VALUES (NULL, '$rendomid','$ank_p', '$msg', 'SMS','0','0')";
				
			$this->db->query($sqll);  
   */
   
}
   
 if($remark=='fb'){
     
   
   

 
 
  $this->mdb->sendfb("flexiload","SMS*$ank_fb*$msg*DD"); 
   
   
   
   
     
 }
 
    
}

$se=$orde[0]['service'];
$po=$orde[0]['pcode'];

	$sql = "INSERT INTO `reportserver` (`msgbody`,`sender`,`pcode`,`servicid`,`datetime`,`idate`) VALUES ('$str','$sender','$po','$se','$create_date','$idate')";
	
		$this->db->query($sql);
	



if (preg_match('/bKash|NAGAD|16216/isU', $sender)){
$time=time();
$simbalance=paymentsimBalance($str); 




		
if(!empty($ctrnx) && !empty($simbalance)){
    $simbalax = $this->db->get_where('modem',array('sender' =>$sender))->row()->simbalance;
    $ctrnx = $this->mdb->passwordChanger('encrypt', $ctrnx);
$msg = $this->mdb->passwordChanger('encrypt', $str);

    

if (preg_match('/received|Received/isU', $str)){
$totalbal=($simbalax+$rate);
$plus=1;
}
if (preg_match('/Send|transferred/isU', $str)){
    if (!preg_match('/Sender/isU', $str)){
   $fee=  fee($str);
$totalbal=($simbalax-($rate+$fee));
    }

}

$sql = "SELECT * FROM `trnx` where trnx='$ctrnx'"; 
	$queryrtsloadm=$this->db->query($sql);




                   
		if(($queryrtsloadm->num_rows()==0) && ($totalbal==$simbalance)) {
		     $queryi = "UPDATE `modem` SET `simbalance`='$simbalance' WHERE `sender` ='$sender'";
  
		$ife=$this->db->query($queryi);
		    
		    


if($plus=='1'){
   
$time=time();


 	$sql = "INSERT INTO `trnx` (`time`,`amount`,`type`,`cat`,`sender`,`trnx`,`add_date`,`msg`) VALUES ('$time','$rate','$sender','$sender','$sender','$ctrnx','$create_date','$msg')";
	
		$this->db->query($sql);


		





    
}
}
}

}




if(!empty($appreffer)){
    $age = array("status"=>1);

echo json_encode($age);
    
}else{
echo "suc";
}

			//header('Content-type: application/json'); 
			
		//cho json_encode($response); 
			
		//	$this->sendtoapi();
}
	
	}	
	
	
	public function request(){
		
 
	
		
	if($_POST) {
		 function curl_ld($url)
{
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, $url);

    $data = curl_exec($ch);
    curl_close($ch);

    return $data;
}



	 $modem = $this->db->get_where('security_option',array('id' =>1))->row()->modem;
	 
	  $modem_pass = $this->db->get_where('company',array('id' =>1))->row()->pin;
	 $get=$this->input->get();
		$var=$this->input->post();
		$mpass = $var['pin'];
			$appreffer = $var['ref'];
		 if($modem_pass==$mpass){$job='1';}
		 
		 $job='1';
if(($modem==1) && ($job==1)){
	
				$id = $this->mdb->pcode($var['id']);
					$m1 = $this->mdb->pcode($var['m1']);
			$m2 = $this->mdb->pcode($var['m2']);
		$m3 = $var['m3'];
			$m4 = $var['m4'];
		$m5 = $var['m5'];
			$m6 = $var['m6'];
		$m7 = $var['m7'];
			$m8 = $var['m8'];
	    if($m1=='GP' OR $m2=='GP' OR $m3=='GP'){
	      $sk='SK';  
	    }
	    
			$dt = new DateTime('now', new DateTimezone('Asia/Dhaka')); 
	$create_date=$dt->format('j F Y g:i A'); 

//$apiuserm = "Select * from modem where status='1' and (pcode='$m3' or pcode='$m2' or pcode='$m1' or pcode='$sk') order by id asc limit 1"; 
		
		$apiuserm = "Select * from modem where status='1' and (pcode='$m8' or pcode='$m7' or pcode='$m6' or pcode='$m5' or pcode='$m4' or pcode='$m3' or pcode='$m2' or pcode='$m1' or pcode='$sk') order by id asc limit 1"; 
		
			$queryrtsloadm=$this->db->query($apiuserm);
			
			if($queryrtsloadm->num_rows()> 0) {
	       	
				foreach ($queryrtsloadm->result() as $user_datam)
					{
		  $mup = "UPDATE `modem` SET `time`='$create_date' WHERE `id`='$user_datam->id'"; 
		 
		$this->db->query($mup);
}
}
		$result = array(); 


		//$apiuser = "Select * from sendflexi where id='$id' and (route='modem' or route='') and ((SUBSTR(pcode, 1, 2)='$m3' or SUBSTR(pcode, 1, 2)='$m2' or SUBSTR(pcode, 1, 2)='$m1' or SUBSTR(pcode, 1, 2)='$sk')) order by id asc limit 1"; 
		
		
			$apiuser = "Select * from sendflexi where id='$id' and (route='modem' or route='') and (SUBSTR(pcode, 1, 2)='$m7' or SUBSTR(pcode, 1, 2)='$m6' or SUBSTR(pcode, 1, 2)='$m5' or SUBSTR(pcode, 1, 2)='$m4' or SUBSTR(pcode, 1, 2)='$m3' or SUBSTR(pcode, 1, 2)='$m2' or SUBSTR(pcode, 1, 2)='$m1' or SUBSTR(pcode, 1, 2)='$sk' or pcode='$m8') ORDER BY RAND() limit 1"; 
		
			$queryrtsload=$this->db->query($apiuser);
			
			if($queryrtsload->num_rows()> 0) {
	       	
				foreach ($queryrtsload->result() as $user_data)
					{
				if($user_data->resend_count==0){$title="Flexiload";}else{$title="Resend";} 
				if($user_data->pcode!='BILL'){
        	$chk_data=$this->mdb->getData('modem',array('pcode'=>substr($user_data->pcode, 0,2)));
				}else{
				   	$chk_data=$this->mdb->getData('modem',array('pcode'=>$user_data->pcode)); 
				    
				}
				$mid=$chk_data[0]['id'];
				$pin=$chk_data[0]['pin'];
				$slot=$chk_data[0]['modem_port'];
				
				/*
					$chk_sima=$this->mdb->getData('net_package',array('op'=>$mid,'price'=>$user_data->balance,'status'=>'1'));
					if(!empty($chk_sima)){
					 $chk_sim=$this->mdb->getData('siminfo',array('id_modem'=>$mid,'amount'=>'offer','status'=>'1'));
					}
					if(empty($chk_sim)){
					    
					$chk_sim=$this->mdb->getData('siminfo',array('id_modem'=>$mid));
					    
					}
					*/
					
					$chk_sim=$this->mdb->getData('siminfo',array('id_modem'=>$mid,'amount'=>$user_data->balance));
					if(empty($chk_sim)){
					$chk_sim=$this->mdb->getData('siminfo',array('id_modem'=>$mid));}
					
				$ussd=$chk_sim[0]['ussd'];
				
				

$vars = array(
  '%amount%'       => $user_data->balance,
  '%number%'        => $user_data->phone,
  '%pin%' => $pin
);



$usd= strtr($ussd, $vars);
if($user_data->pcode=='SMS' OR $user_data->pcode=='BILL'){
    $sms="1";
    $title="SMS";
    $pco="SMS";
    $smstext = $this->mdb->passwordChanger('decrypt', $user_data->smstext); 
   $smstext=  preg_replace('/%pin%/u', $pin,$smstext);
    
    
}else{
    $sms="0";
     $pco=$user_data->pcode;
    
}

if(empty($slot)){$slot="0";}
if(empty($chk_sim[0]['auto'])){$auto="0";}else{$auto=$chk_sim[0]['auto'];}

if(($user_data->pcode=='BILL') && empty($smstext)){
  $number='727';
  if($user_data->type==1){
      
  $smstext=$user_data->phone;
$id=strlen($user_data->phone);
 if($id<18){
$prevmonth = date('M', strtotime('-1 months'));
  $smstext="$user_data->phone $prevmonth";

}
  }else{ 
      $smstext="REGS $user_data->phone $user_data->name";
      
  } 
}else{$number=$user_data->phone;}


						   $slid=$user_data->id;
					   $rendomid=$user_data->sid;
									if(!empty($appreffer)){
   
						$response = array( 
                          	'resend' => $user_data->resend_count,
						'id' => $user_data->id,
						'sid' => $user_data->sid,
						'userid' => $user_data->userid, 
						'pcode' => $pco,
						'number' => $number, 
						'balance' => $user_data->balance, 
						'service' => $user_data->service, 
						'status' => $user_data->status, 
						'type' => $user_data->type,
						'ussd' => $usd,
						'slot' => $slot,
							'sms' => $sms,
								'smstext' => $smstext,
						'title' => $title,
						'auto' => $auto,
						'line' => $chk_sim[0]['offer'],
							'triger' => $chk_sim[0]['triger'],
							'1st' =>  str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['a']),
								'2nd' =>  str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['b']),
									'3rd' => str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['c']),
										'4rd' => str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['d']),
											'5th' => str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['e'])
					); 
    
}else{		   
						$response[] = array( 
						'id' => $user_data->id,
						'sid' => $user_data->sid,
						'userid' => $user_data->userid, 
						'pcode' => $pco,
						'number' => $number, 
						'balance' => $user_data->balance, 
						'service' => $user_data->service, 
						'status' => $user_data->status, 
						'type' => $user_data->type,
						'ussd' => $usd,
						'slot' => $slot,
							'sms' => $sms,
								'smstext' => $smstext,
						'title' => $title,
						'auto' => $auto,
							'triger' => $chk_sim[0]['triger'],
							'1st' =>  str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['a']),
								'2nd' =>  str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['b']),
									'3rd' => str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['c']),
										'4rd' => str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['d']),
											'5th' => str2($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['e'])
					); 
				
				
					}	
		//array_push($result, $json_data); 
		if($user_data->pcode=='SMS' OR $user_data->type=='bill'){
		 $strignupt = "UPDATE `sendflexi` SET `status`='1', `local`='1' WHERE `id`='$slid'"; 
		}else{ $strignupt = "UPDATE `sendflexi` SET `status`='4', `local`='4' WHERE `id`='$slid'"; }
 $this->db->query($strignupt);
		 
		 
		 
            }
			}else {
						if(!empty($appreffer)){
				$response = array("msg" => 'Not Found', "status" => 4);
}else{$response[] = array("msg" => 'Not Found', "status" => 4);}
			}			
			
	}else{
	    	if(!empty($appreffer)){
	    	$response = array("msg" => 'modem off', "status" => 4);
	    	}else{	$response[] = array("msg" => 'modem off', "status" => 4);}
	}
			
			header('Content-type: application/json'); 
			
			echo json_encode($response); 
			
		//	$this->sendtoapi();

	}
	

	}	
}
