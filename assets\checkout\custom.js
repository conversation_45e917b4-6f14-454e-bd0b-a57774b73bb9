var translate = true;

$("#supportbtn").on('click', function () {
    hideAll();
    $("#support-sec").removeAttr("hidden");
});

$("#faqbtn").on('click', function () {
    hideAll();
    $("#faq-sec").removeAttr("hidden");
});

$("#detailsbtn").on('click', function () {
    hideAll();
    $("#details-sec").removeAttr("hidden");
});

$("#mobile").on('click', function () {
    hideAll();
    $("#mobile").addClass("optionsCard");
    $("#mobile-sec").removeAttr("hidden");
});

$("#translatebtn").on('click', function () {
    translateAll();
});

function translateAll() {
    if (translate) {
        $("#support-text").attr("hidden", true);
        $("#bangla-support-text").removeAttr("hidden");

        $("#faq-support-text").attr("hidden", true);
        $("#bangla-faq-support-text").removeAttr("hidden");

        $("#translate-support-text").attr("hidden", true);
        $("#bangla-translate-support-text").removeAttr("hidden");

        $("#details-text").attr("hidden", true);
        $("#bangla-details-text").removeAttr("hidden");

        $("#mobile-btn").attr("hidden", true);
        $("#bangla-mobile-btn").removeAttr("hidden");

        $("#support-content-title").attr("hidden", true);
        $("#bangla-support-content-title").removeAttr("hidden");

        $("#support-chat").attr("hidden", true);
        $("#bangla-support-chat").removeAttr("hidden");

        $("#support-mail").attr("hidden", true);
        $("#bangla-support-mail").removeAttr("hidden");

        $("#suport-call").attr("hidden", true);
        $("#bangla-suport-call").removeAttr("hidden");

        $("#faq-title-1").attr("hidden", true);
        $("#bangla-faq-title-1").removeAttr("hidden");

        $("#faq-title-2").attr("hidden", true);
        $("#bangla-faq-title-2").removeAttr("hidden");

        $("#faq-title-3").attr("hidden", true);
        $("#bangla-faq-title-3").removeAttr("hidden");

        $("#faq-title-4").attr("hidden", true);
        $("#bangla-faq-title-4").removeAttr("hidden");

        $("#faq-title-5").attr("hidden", true);
        $("#bangla-faq-title-5").removeAttr("hidden");

        $("#faq-title-6").attr("hidden", true);
        $("#bangla-faq-title-6").removeAttr("hidden");

        $("#faq-title-7").attr("hidden", true);
        $("#bangla-faq-title-7").removeAttr("hidden");

        $("#faq-details-1").attr("hidden", true);
        $("#bangla-faq-details-1").removeAttr("hidden");

        $("#faq-details-2").attr("hidden", true);
        $("#bangla-faq-details-2").removeAttr("hidden");

        $("#faq-details-3").attr("hidden", true);
        $("#bangla-faq-details-3").removeAttr("hidden");

        $("#faq-details-4").attr("hidden", true);
        $("#bangla-faq-details-4").removeAttr("hidden");

        $("#faq-details-5").attr("hidden", true);
        $("#bangla-faq-details-5").removeAttr("hidden");

        $("#faq-details-6").attr("hidden", true);
        $("#bangla-faq-details-6").removeAttr("hidden");

        $("#faq-details-7").attr("hidden", true);
        $("#bangla-faq-details-7").removeAttr("hidden");

        $("#trx-title").attr("hidden", true);
        $("#bangla-trx-title").removeAttr("hidden");

        $("#payment-description").attr("hidden", true);
        $("#bangla-payment-description").removeAttr("hidden");
    } else {
        $("#support-text").removeAttr("hidden");
        $("#bangla-support-text").attr("hidden", true);

        $("#faq-support-text").removeAttr("hidden");
        $("#bangla-faq-support-text").attr("hidden", true);

        $("#translate-support-text").removeAttr("hidden");
        $("#bangla-translate-support-text").attr("hidden", true);

        $("#details-text").removeAttr("hidden");
        $("#bangla-details-text").attr("hidden", true);

        $("#mobile-btn").removeAttr("hidden");
        $("#bangla-mobile-btn").attr("hidden", true);

        $("#support-content-title").removeAttr("hidden");
        $("#bangla-support-content-title").attr("hidden", true);

        $("#support-chat").removeAttr("hidden");
        $("#bangla-support-chat").attr("hidden", true);

        $("#support-mail").removeAttr("hidden");
        $("#bangla-support-mail").attr("hidden", true);

        $("#suport-call").removeAttr("hidden");
        $("#bangla-suport-call").attr("hidden", true);

        $("#faq-title-1").removeAttr("hidden");
        $("#bangla-faq-title-1").attr("hidden", true);

        $("#faq-title-2").removeAttr("hidden");
        $("#bangla-faq-title-2").attr("hidden", true);

        $("#faq-title-3").removeAttr("hidden");
        $("#bangla-faq-title-3").attr("hidden", true);

        $("#faq-title-4").removeAttr("hidden");
        $("#bangla-faq-title-4").attr("hidden", true);

        $("#faq-title-5").removeAttr("hidden");
        $("#bangla-faq-title-5").attr("hidden", true);

        $("#faq-title-6").removeAttr("hidden");
        $("#bangla-faq-title-6").attr("hidden", true);

        $("#faq-title-7").removeAttr("hidden");
        $("#bangla-faq-title-7").attr("hidden", true);

        $("#faq-details-1").removeAttr("hidden");
        $("#bangla-faq-details-1").attr("hidden", true);

        $("#faq-details-2").removeAttr("hidden");
        $("#bangla-faq-details-2").attr("hidden", true);

        $("#faq-details-3").removeAttr("hidden");
        $("#bangla-faq-details-3").attr("hidden", true);

        $("#faq-details-4").removeAttr("hidden");
        $("#bangla-faq-details-4").attr("hidden", true);

        $("#faq-details-5").removeAttr("hidden");
        $("#bangla-faq-details-5").attr("hidden", true);

        $("#faq-details-6").removeAttr("hidden");
        $("#bangla-faq-details-6").attr("hidden", true);

        $("#faq-details-7").removeAttr("hidden");
        $("#bangla-faq-details-7").attr("hidden", true);

        $("#trx-title").removeAttr("hidden");
        $("#bangla-trx-title").attr("hidden", true);

        $("#payment-description").removeAttr("hidden");
        $("#bangla-payment-description").attr("hidden", true);
    }
    translate = !translate;
}

function hideAll() {
    $("#mobile-sec").attr("hidden", true);
    $("#faq-sec").attr("hidden", true);
    $("#support-sec").attr("hidden", true);
    $("#details-sec").attr("hidden", true);
}

// Clipboard
var clipboard = new ClipboardJS('.btn');
clipboard.on('success', function(e) {
    toastr.success('Copied!')
});