﻿@import url('calender.css');

body {
	/*font-size:12px;
	background-color: #ECF0F5;
	font-family: Helvetica Narrow, sans-serif;
	text-decoration:none;*/
	color:#333;
	/*overflow: visible !important;
	overflow: scroll !important;*/
	min-width: 1085px;
}
.btn {font-weight: 600;}
.content-wrapper {padding-bottom:50px;}
ins {
    text-decoration: none;
}
del {
    text-decoration: line-through;
}

td,
textarea,
input,
select {
    font-family: "Lucida Grande", Verdana, Arial, sans-serif;
}
textarea {
    line-height: 1.4em;
}
input,
select {
    line-height: 1em;
}

p {
    margin: 1em 0;
}

blockquote {
    margin: 1em;
}

label {
    cursor: pointer;
}
h1 {
  display: block;
  font-size: 2em;
  font-weight: bold;
  margin: .20em 0;
}

h2 {
  display: block;
  font-size: 1.5em;
  font-weight: bold;
  margin: .83em 0;
}

h3 {
  display: block;
  font-size: 1.17em;
  font-weight: bold;
  margin: 1em 0;
}

h4 {
  display: block;
  font-size: 1em;
  font-weight: bold;
  margin: 1.33em 0;
}

h5 {
  display: block;
  font-size: 0.83em;
  font-weight: bold;
  margin: 1.67em 0;
}

h6 {
  display: block;
  font-size: 0.67em;
  font-weight: bold;
  margin: 2.33em 0;
}
html {
	height: 100%;
}
a {
	color: #3B5998;
	text-decoration:none;
}
a:hover {
	text-decoration: none;
}
img {
	border: none;
}

input {
	vertical-align: middle;
}

#wrapper {
	width: 100%;
	min-width: 1000px;
	min-height: 100%;
	height: auto !important;
	height: 100%;
	background:#fff url(wrapper.png) repeat-y;
}



/* Header
-----------------------------------------------------------------------------*/
#header {
	background: #3B5998;
	color:#fff;
	text-shadow:-1px #122243;
    height: 40px;
    width:100%;
	border-bottom:2px solid #133783;
}


/* Middle
-----------------------------------------------------------------------------*/
#middle {
	width: 100%;
	padding: 0 0 30px;
	height: 1%;
	position: relative;
	padding-top:0px;
}
#middle:after {
	content: '.';
	display: block;
	clear: both;
	visibility: hidden;
	height: 0;
}
#container {
	width: 100%;
	float: left;
	overflow: hidden;
}



/* Sidebar Left
-----------------------------------------------------------------------------*/
#sideLeft {
	float: left;
	width: 180px;
	margin-left: -100%;
	position: relative;
	padding-top:0px;
}


/* Footer
-----------------------------------------------------------------------------*/
#footer {
	margin: -30px auto 0;
	min-width: 1000px;
	height: 29px;
	background: #f1f1f1;
	position: relative;
	border-top:1px solid #ccc;
}


#footer a:hover {text-decoration:underline;}
.fleft{width:40%;float:left;padding:5px 10px;}
.fright{width:40%;float:right;text-align:right;padding:5px 10px;}
.fright img{cursor:pointer;}

.hleft{width:400px;float:left;}
.hleft h2{font-size:20px;padding:5px 10px;margin-top:5px; padding-bottom:0; color:#fff;font-weight:bold;text-shadow:1px 1px #4A66A0;}
.hleft span{font-size:12px;color:#fff;float:left;margin-top:8px;font-weight:bold;}
.hright{width:600px;float:right;height:40px;text-align:right;color:#fff;}
.hright p{padding:10px;margin-right:20px;margin-top:10px;color:#fff;}
.hright p b{color:#fff;}
.hright a{color:#fff;height:40px;line-height:40px;padding:0px 10px;font-weight:bold;font-size:1.1em;}
.hright a:hover{text-decoration:underline;}

a.profile span{color:#fff;margin-right:20px;height:40px;line-height:40px;float:right;padding:0px 10px}
a.profile span:hover{background:#007112;}

/* a.logo{margin-left:10px;}
a.logo img{padding:10px 10px 10px 10px;max-height:70px;max-width:300px;}
a.logo img:hover{background:#2C7DCF;} */
.clear{clear:both;margin:2px 0;}
#content{padding-right:0px;}

#content h1{
font-weight:normal;
color:#333;
font-size:20px;
padding-bottom:10px;
margin:0px;
}


#content h1 span{float:right;}
#content h1 a{margin-left:5px;}

.filter{
border:1px solid #E9E9E9;
margin:10px 0px 10px 0px;
}

.filter table{
width:auto;
padding:5px;
}
.filter table td{padding:6px 10px 6px 5px;vertical-align:center;}


.filter input{width:108px;height:22px;padding:0px 5px;background:#fff;border:1px solid #BDC7D8;}
.filter select{height:22px;padding:2px;padding-right:2px;border:1px solid #BDC7D8;max-width: 150PX;}
.filter input.submit{width:80px;height:22px;padding:2px 5px 2px 8px;background:#627AAC;border:1px solid #29447E;color:#fff;font-weight:bold;cursor:pointer;}
.filter input.submit:active{background:#4F6AA3;}


#content table.data {
  width: 100%;
  display: table;
  margin:0px;
  float:left;
}

#content table.data  tr.rowc{background:#FFC6C6;}
#content table.data  tr.rowc:hover{background:#FFC6C6;}
#content table.data  tr.rowa{background:#fff;}
#content table.data  tr.rowb{background:#f3f3f3;}
#content table.data  tr:hover{background:#E7E7E7;}


#content table.data  th.short{
background:#3B5998 url(short.png) no-repeat right center;
cursor:pointer;
}
#content table.data  th{
  height: 25px;
  padding:3px 5px;
  vertical-align: middle;
  color: #ffffff;
  background:#3B5998;
  font-size: 12px;
  text-align:left;  
}
#content table.data  th.foot{
  height: 20px;
  padding:2px 5px;
  vertical-align: middle;
  color: #ffffff;
  background:#666;
  font-size: 11px;
  text-align:left;
  cursor:pointer;
}
#content table.data  td{
  height: 20px;
  padding:2px 5px;
  vertical-align: middle;
  color: #666666;
  font-size: 11px;
  text-align:left;
}
#content table.data  td a{font-size: 11px;font-weight:bold;}

a.button{
color:#fff;
background:#0072C6;
padding:6px 12px;
font-weight:bold;
cursor:pointer;
}
a.button:hover{background:#1E82CC}

.button{
color:#fff;
background:#0072C6;
padding:6px 12px;
font-weight:bold;
cursor:pointer;
border:none;}

.button:hover{background:#1E82CC}

.slim {padding:2px 10px;font-size:11px;font-weight:normal;}

.paging_full_numbers {
	width: 100%;
	height: 22px;
	line-height: 22px;
}

.paging_full_numbers span.paginate_button,
 	.paging_full_numbers span.paginate_active {
	border: 1px solid #3B5998;
	padding: 3px 5px;
	margin: 0 3px;
	font-size:11px;
	cursor: pointer;
	*cursor: hand;
	font-weight:bold;
}

.paging_full_numbers span.paginate_button {
	background-color: #fff;
}

.paging_full_numbers span.paginate_button:hover {
	background-color: #3B5998;color:#fff;
}

.paging_full_numbers span.paginate_active {
	background-color: #ddd;
}

.entry2 {margin:20px auto;width:750px;background:#f9f9f9;padding:10px 20px;margin-bottom:20px;border:1px solid #ddd;}
.entry2 td{padding:2px;font-size:12px;}
.entry2 p{padding:2px;border-bottom:1px solid #ddd;font-size:12px; font-weight:bold;margin:5px 0px;}
.entry2 input{height:26px;margin-left:4px;}
.entry2 select{margin-left:4px;}
.entry2 .size2{width:130px}
.entry2 .size9{width:230px}
.entry2 select.size2{width:140px;height:26px;}

.entry {margin:20px auto;width:100%;background:#f9f9f9;padding:10px 20px;margin-bottom:20px;border:1px solid #ddd;}
.entry td{padding:2px;font-size:12px;}
.entry p{padding:2px;border-bottom:1px solid #ddd;font-size:12px; font-weight:bold;margin:5px 0px;}
.entry input{height:26px;margin-left:4px;}
.entry select{margin-left:4px;}
.entry .size2{width:130px}
.entry .size9{width:230px}
.entry select.size2{width:140px;height:26px;}
div.role_item{width:220px;}

select.remover{padding:0px;}

.summery{width:300px;margin:0px auto;}
.summery h2{border-bottom:1px solid #ccc;font-weight:bold;padding:5px 0px;}
.summery p{margin:0px;}
.delip{color:red;font-weight:bold;font-size:11px;}
#ipResult ul{list-style-type: none; margin-left:0px;margin-right:10px;}
#ipResult li{border-bottom:1px solid #ddd;padding-bottom:3px;}
#content table.home {width:100%;border:1px solid #E9E9E9;background:#F2F2F2;padding:10px;}
#content table.home h2{margin:0px;font-weight:normal;font-size:11px;}
#content table.home  tr.rowa{background:#fff;}
#content table.home  tr.rowb{background:#f3f3f3;}
#content table.home  td{height: 20px;padding:2px 5px;vertical-align: middle;color: #666666;font-size: 11px;border-bottom:1px solid #ddd;}
#quickview{width:100%;margin:10px 0px;float:left;}

.dnhome{width:100%;margin:10px 0px;float:left;}

.box{background:#f7f7f7;width:130px;height:60px;padding:5px;border:1px solid #e2e2e2;margin-right:10px;float:left;}
.box p{text-align:center;font-size:11px;color:#3B5998;font-weight:bold;margin:5px 0px;}
.box h4{text-align:center;font-size:18px;color:#1C1D1F;font-weight:bold;margin:10px 0px;}

.dnhome h2{padding:2px 5px;font-weight:normal;}
.dnhome th{padding:3px 5px 3px;font-size:11px;font-weight:bold;border-bottom:1px solid #e5e5e5;background:#e5e5e5;text-align:left;}
.dnhome td{padding:3px 5px 3px;font-size:11px;font-weight:bold;border-bottom:1px solid #e5e5e5}
.dnhome a{padding-right:5px;font-size:11px;color:#3B5998;}

.buttonsmall{background:red;padding:2px 5px;color:#fff;font-weight:bold;margin:0px 5px;}

.home {margin:10px;}
.home td{padding:0px 10px;padding-bottom:10px;}
.home p{margin:0px;padding:0px;border-bottom:1px solid #ccc;font-size:13px;font-weight:bold;padding:5px 0px;margin-bottom:5px;}


a.action{width:80px;height:20px;padding:2px 2px 2px 2px;background:#627AAC;border:1px solid #29447E;color:#fff;font-weight:normal;cursor:pointer;margin-left:5px;}
a.action:active{background:#4F6AA3;}

/*Tooltip UI component by www.menucool.com */

/*For tooltip target element that has set class="tooltip" */
.tooltip {text-decoration: none; border-bottom:1px dotted #36c;color: #36c; outline: none; cursor:pointer;}

/*For tooltip box*/
div#mcTooltip 
{
    line-height:16px;
    border-width: 1px;   
    color:#333; 
    border-color:#BBBBBB;
    padding:10px;
    font-size: 12px;
    font-family: Verdana, Arial;
    border-radius:6px; /*Rounded corners. N/A for IE 8 and below.*/
    box-shadow: 0 1px 4px #AAAAAA; /*Drop shadow. N/A for IE 8 and below.*/
}
div#mcTooltip, div#mcTooltip div 
{
    background-color:#fff;
}

/* For hyperlink within tooltip */
div#mcTooltip a { color:blue;font-weight:bold; }

/*Close button. Only available when sticky or overlay has been set to true.*/
div#mcttCloseButton 
{
    width:14px;height:14px;position:absolute;background-image:url(closeBtn.gif);cursor:pointer; overflow:hidden;
    top:12px; right:12px; left:auto;
}            

/* Only applies when overlay has been set to true or 1.*/
div#mcOverlay 
{
    background-color: white;
    opacity:0.8; filter: alpha(opacity=80); 
    display:none;top:0;left:0;width:100%;height: 100%;overflow:visible;z-index:4; 
}

/*Only available when calling by tooltip.ajax(...). It will be a spinning image indicating a request is in progress.*/
div#tooltipAjaxSpin {margin:20px 50px; background:transparent url(loading.gif) no-repeat center center; width:50px; height:30px; font-size:0;}

/*The settings below should remain unchanged*/
div#mcTooltipWrapper {position:absolute;visibility:hidden;overflow:visible;z-index:9999999999;top:-2000px;}
div#mcTooltip {float:left;border-style:solid;position:relative;overflow:hidden;}
div#mcttCo {position:absolute;text-align:left;}
div#mcttCo em, div#mcttCo b {display:block; width:0; height:0;overflow:hidden;}

#styleswitcher{ float:left; margin-right:10px; }


#styleswitcher a:hover{text-decoration:none;}
a#defswitch {
	width:15px;
	height:10px;
	margin: 3px 0 3px 0;
	background-color:#3B5998;
	color:#3B5998;
	font-size: 8px;
	display:inline-block;
}
a#blakswitch {
	width:15px;
	height:10px;
	margin: 3px 0 3px 0;
	background-color:#000;
	font-size: 8px;
	color:#000;
	display:inline-block;
}
a#greenswitch {
	width:15px;
	height:10px;
	margin: 3px 0 3px 0;
	background-color:#008A17;
	font-size: 8px;
	color:#008A17;
	display:inline-block;
}
a#brownswitch {
	width:15px;
	height:10px;
	margin: 3px 0 3px 0;
	background-color:#D24726;
	font-size: 8px;
	color:#D24726;
	display:inline-block;
}
a#mixswitch {
	width:15px;
	height:10px;
	margin: 3px 0 3px 0;
	background-color:#990099;
	font-size: 8px;
	color:#990099;
	display:inline-block;
}
a#cynswitch {
	width:15px;
	height:10px;
	margin: 3px 0 3px 0;
	background-color:#36A9E1;
	font-size: 8px;
	color:#990099;
	display:inline-block;
}
a#redswitch {
	width:15px;
	height:10px;
	margin: 3px 0 3px 0;
	background-color:#DB000B;
	font-size: 8px;
	color:#990099;
	display:inline-block;
}

/* For summary page */

/* LINE ICONS CONFIGURATION */

.mtbox {
	margin-top: 80px;
	margin-bottom: 40px;
}

.box1 {
	padding: 15px;
	text-align: center;
	color: #989898;
	border-bottom: 1px solid #989898;
}

.box1 span {
	font-size: 50px;

}

.box1 h3 {
	text-align: center;
}

.box0:hover .box1 {
	border-bottom: 1px solid #ffffff;
}

.box0 p {
	text-align: center;
	font-size: 12px;
	color: #f2f2f2;
}

.box0:hover p {
	color: #ff865c;
}

.box0:hover {
	/*background: #ffffff;*/
	background: #c0ffee;
	box-shadow: 0 2px 1px rgba(0, 0, 0, 0.2);
}
.content-header h1{
	font-weight:bold !important;
	font-size:18px !important;
}

.content-header {
	padding: 0px 0px !important;
	
}
.sidebar-menu, .main-sidebar .user-panel, .sidebar-menu>li.header {
    font-size: 16px;
}

.usage{width:290px;border-top-left-radius:3px;border-top-right-radius:3px;border:1px solid;margin:10px 20px 20px 0px;min-height:355px;float:left;}
.usage table{padding:10px 17px;margin:0px;width:100%;}
.usage .table{min-height:261px; background-color:white;}
.usage td{padding:7px 7px;font-size:14px;color:#464a53;border-bottom:1px solid;}
.usage td.tk{text-align:right;color:#333333;font-weight:bold;font-size:15px;}
.usage h2{border-top-left-radius:1px;border-top-right-radius:1px;border-bottom:1px solid;height:46px;line-height:46px;margin:0px;padding:0px 17px; font-weight:bold;font-size:16px;color:#ffffff;}
.usage h3{height:46px;line-height:46px;margin:0px;padding:0px 17px; font-weight:normal;font-size:14px;}
.usage h3 span.left{float:left;width:150px;text-align:left;font-weight:normal;font-size:16px;color:white;}
.usage h3 span.right{float:right;width:100px;text-align:right;;font-weight:bold;font-size:15px;color:white;}


/************************/

/**********sticky footer**************/



  
/**********custom skin color for .skin-blue **************/
.skin-blue , .skin-blue .content-wrapper{
	background-color:#ecf4f9 !important;
}
  .skin-blue .content-wrapper   table.data  th {
	background-color:#31749b !important;
}

  .skin-blue .content-wrapper  table.data  th.short  {
	  background-color:#31749b !important;
}

.skin-blue .content-wrapper .filter form, .skin-blue .content-wrapper .filter form  table, .skin-blue .content-wrapper #content table.data  tr:hover {
	background-color:#c5deed;
}
.skin-blue .content-wrapper .btn-success {
	background-color:#183a4e !important;
	border-color:#183a4e !important;
}
.skin-blue .content-wrapper .usage h2, .skin-blue .content-wrapper .usage h3 {color:white;background-color:#3c8dbc; border-color:#3c8dbc;}
.skin-blue .content-wrapper .usage, .skin-blue .content-wrapper .usage td{border-color:#3c8dbc;}
.skin-blue .content-wrapper .usage {background-color:white;}
.skin-blue .content-wrapper .usage h3 a {color:white;}

/* .skin-blue .user-menu  i {
	color:white;
} */
/**********CLOSE custom skin color for .skin-blue **************/

  
/**********custom skin color for .skin-blue-light **************/
.skin-blue-light, .skin-blue-light .content-wrapper{
	background-color:#ecf4f9 !important;
}
  .skin-blue-light .content-wrapper   table.data  th {
	background-color:#31749b !important;
}

  .skin-blue-light.content-wrapper  table.data  th.short  {
	  background-color:#31749b !important;
}

.skin-blue-light .content-wrapper .filter form, .skin-blue-light .content-wrapper .filter form  table, .skin-blue-light .content-wrapper #content table.data  tr:hover {
	background-color:#c5deed;
}
.skin-blue-light .content-wrapper .btn-success {
	background-color:#183a4e !important;
	border-color:#183a4e !important;
}
.skin-blue-light .content-wrapper .usage h2, .skin-blue-light .content-wrapper .usage h3 {color:white;background-color:#3c8dbc; border-color:#3c8dbc;}
.skin-blue-light .content-wrapper .usage, .skin-blue-light .content-wrapper .usage td{border-color:#3c8dbc;}
.skin-blue-light .content-wrapper .usage {background-color:white;}
.skin-blue-light .content-wrapper .usage h3 a {color:white;}
/* .skin-blue .user-menu  i {
	color:white;
} */
/**********CLOSE custom skin color for .skin-blue-light **************/


/**********custom skin color for .skin-black **************/
.skin-black ,.skin-black .content-wrapper{
	background-color:#f1f1f1 !important;
}
.skin-black .content-wrapper .filter form, .skin-black .content-wrapper .filter form  table, .skin-black .content-wrapper #content table.data  tr:hover{
	/*background-color:#c5deed; */
}
.skin-black .content-wrapper   table.data  th, .skin-black .content-wrapper table.data  th.short  {
	 background-color:#1a2226 !important;
}
.skin-black .content-wrapper .usage h2, .skin-black .content-wrapper .usage h3 {color:black;background-color:#9096a2; border-color:black;}
.skin-black .content-wrapper .usage, .skin-black .content-wrapper .usage td{border-color:black;}
.skin-black .content-wrapper .usage {background-color:white;}
.skin-black .content-wrapper .usage h3 a {color:black;}
/**********CLOSE custom skin color for .skin-black **************/

/**********custom skin color for .skin-black-light **************/
.skin-black-light , .skin-black-light .content-wrapper{
	background-color:#f1f1f1 !important;
}
.skin-black-light .content-wrapper .filter form, .skin-black-light .content-wrapper .filter form  table, .skin-black-light .content-wrapper #content table.data  tr:hover{
	/*background-color:#c5deed; */
}
.skin-black-light .content-wrapper   table.data  th, .skin-black-light .content-wrapper table.data  th.short  {
	 background-color:#1a2226 !important;
}
.skin-black-light .content-wrapper .usage h2, .skin-black-light .content-wrapper .usage h3 {color:black;background-color:#9096a2; border-color:black;}
.skin-black-light .content-wrapper .usage, .skin-black-light .content-wrapper .usage td{border-color:black;}
.skin-black-light .content-wrapper .usage {background-color:white;}
.skin-black-light .content-wrapper .usage h3 a {color:black;}

/**********CLOSE custom skin color for .skin-black-light **************/

/**********custom skin color for .skin-purple **************/
.skin-purple, .skin-purple .content-wrapper {
	background-color:#efeef6 !important;
}
.skin-purple .content-wrapper .filter form, .skin-purple .content-wrapper .filter form  table, .skin-purple .content-wrapper #content table.data  tr:hover  {
	background-color:#dfdeed;
}
.skin-purple .content-wrapper   table.data  th, .skin-purple .content-wrapper  table.data  th.short  {
	background-color: #413e74 !important;
}

.skin-purple .content-wrapper .btn-primary {
	background-color:#605CA8 !important;
	border-color:#605CA8 !important;
}

.skin-purple .content-wrapper .btn-success {
	background-color:#383663 !important;
	border-color:#383663 !important;

}
.skin-purple .content-wrapper .usage h2, .skin-purple .content-wrapper .usage h3 {color:white;background-color:#605CA8; border-color:#605CA8;}
.skin-purple .content-wrapper .usage, .skin-purple .content-wrapper .usage td{border-color:#605CA8;}
.skin-purple .content-wrapper .usage {background-color:white;}
.skin-purple .content-wrapper .usage h3  a {color:white;}

/**********CLOSE custom skin color for .skin-purple **************/

/**********custom skin color for .skin-purple-light **************/
body.skin-purple-light, .skin-purple-light .content-wrapper {
	background-color:#efeef6 !important;
}
.skin-purple-light .content-wrapper .filter form, .skin-purple-light .content-wrapper .filter form  table, .skin-purple-light .content-wrapper #content table.data  tr:hover  {
	background-color:#dfdeed;
}
.skin-purple-light .content-wrapper   table.data  th, .skin-purple-light .content-wrapper  table.data  th.short  {
	background-color: #413e74 !important;
}

.skin-purple-light .content-wrapper .btn-primary {
	background-color:#605CA8 !important;
	border-color:#605CA8 !important;
}

.skin-purple-light .content-wrapper .btn-success {
	background-color:#383663 !important;
	border-color:#383663 !important;
}
.skin-purple-light .content-wrapper .usage h2, .skin-purple-light .content-wrapper .usage h3 {color:white;background-color:#605CA8; border-color:#605CA8;}
.skin-purple-light .content-wrapper .usage, .skin-purple-light .content-wrapper .usage td{border-color:#605CA8;}
.skin-purple-light .content-wrapper .usage {background-color:white;}
.skin-purple-light .content-wrapper .usage h3  a {color:white;}
/**********CLOSE custom skin color for .skin-purple-light **************/


/**********custom skin color for .skin-green **************/
body.skin-green, .skin-green .content-wrapper {
	background-color:#e6fff4 !important;
}
.skin-green .content-wrapper .filter form, .skin-green .content-wrapper .filter form  table, .skin-green .content-wrapper #content table.data  tr:hover {
	background-color:#ccffe8;
}
.skin-green .content-wrapper   table.data  th, .skin-green .content-wrapper  table.data  th.short  {
	background-color: #004d2a !important;
}
.skin-green .content-wrapper .btn-primary {
	background-color:#00A65A !important;
	border-color:#00A65A !important;
}
.skin-green .content-wrapper .btn-success {
	background-color:#006638 !important;
	border-color:#006638 !important;
}
.skin-green .content-wrapper .usage h2, .skin-green .content-wrapper .usage h3 {color:white;background-color:#00A65A; border-color:#00A65A;}
.skin-green .content-wrapper .usage, .skin-green .content-wrapper .usage td{border-color:#00A65A;}
.skin-green .content-wrapper .usage {background-color:white;}
.skin-green .content-wrapper .usage h3  a {color:white;}
/**********custom skin color for .skin-green **************/

/**********custom skin color for .skin-green-light **************/
body.skin-green-light, .skin-green-light .content-wrapper {
	background-color:#e6fff4 !important;
}
.skin-green-light .content-wrapper .filter form, .skin-green-light .content-wrapper .filter form  table, .skin-green-light .content-wrapper #content table.data  tr:hover {
	background-color:#b3ffdd;
}
.skin-green-light .content-wrapper   table.data  th, .skin-green-light .content-wrapper  table.data  th.short  {
	background-color: #004d2a !important;
}
.skin-green-light .content-wrapper .btn-primary {
	background-color:#00A65A !important;
	border-color:#00A65A !important;
}
.skin-green-light .content-wrapper .btn-success {
	background-color:#006638 !important;
	border-color:#006638 !important;
}
.skin-green-light .content-wrapper .usage h2, .skin-green-light .content-wrapper .usage h3 {color:white;background-color:#00A65A; border-color:#00A65A;}
.skin-green-light .content-wrapper .usage, .skin-green-light .content-wrapper .usage td{border-color:#00A65A;}
.skin-green-light .content-wrapper .usage {background-color:white;}
.skin-green-light .content-wrapper .usage h3  a {color:white;}
/**********CLOSE custom skin color for .skin-green-light **************/

/**********custom skin color for .skin-red  **************/
body.skin-red, .skin-red .content-wrapper {
	background-color:#fbebe9 !important;
}
.skin-red .content-wrapper .filter form, .skin-red .content-wrapper .filter form  table, .skin-red .content-wrapper #content table.data  tr:hover{
	background-color:#f8d8d3;
}
.skin-red .content-wrapper   table.data  th, .skin-red .content-wrapper  table.data  th.short  {
	background-color: #41110b !important;
}

.skin-red .content-wrapper .btn-primary {
	background-color:#DD4B39 !important;
	border-color:#DD4B39 !important;
}

.skin-red .content-wrapper .btn-danger {
	background-color:#ff0000 !important;
	border-color:#ff0000 !important;
}

.skin-red .content-wrapper .btn-success {
	background-color:#57170f !important;
	border-color:#57170f !important;
}
.skin-red .content-wrapper .usage h2, .skin-red .content-wrapper .usage h3 {color:white;background-color:#DD4B39; border-color:#DD4B39;}
.skin-red .content-wrapper .usage, .skin-red .content-wrapper .usage td{border-color:#DD4B39;}
.skin-red .content-wrapper .usage {background-color:white;}
.skin-red .content-wrapper .usage h3  a {color:white;}
/**********CLOSE custom skin color for .skin-red  **************/

/**********custom skin color for .skin-red-light  **************/
body.skin-red-light, .skin-red-light .content-wrapper {
	background-color:#fbebe9 !important;
}
.skin-red-light .content-wrapper .filter form, .skin-red-light .content-wrapper .filter form  table, .skin-red-light .content-wrapper #content table.data  tr:hover{
	background-color:#f8d8d3;
}
.skin-red-light .content-wrapper   table.data  th, .skin-red-light .content-wrapper  table.data  th.short  {
	background-color: #41110b !important;
}

.skin-red-light .content-wrapper .btn-primary {
	background-color:#DD4B39 !important;
	border-color:#DD4B39 !important;
}

.skin-red-light .content-wrapper .btn-danger {
	background-color:#ff0000 !important;
	border-color:#ff0000 !important;
}

.skin-red-light .content-wrapper .btn-success {
	background-color:#57170f !important;
	border-color:#57170f !important;
}
.skin-red-light .content-wrapper .usage h2, .skin-red-light .content-wrapper .usage h3 {color:white;background-color:#DD4B39; border-color:#DD4B39;}
.skin-red-light .content-wrapper .usage, .skin-red-light .content-wrapper .usage td{border-color:#DD4B39;}
.skin-red-light .content-wrapper .usage {background-color:white;}
.skin-red-light .content-wrapper .usage h3  a {color:white;}
/**********CLOSE custom skin color for .skin-red-light  **************/

/**********custom skin color for .skin-yellow  **************/
body.skin-yellow, .skin-yellow .content-wrapper {
	background-color:#fef5e7 !important;
}
.skin-yellow .content-wrapper .filter form, .skin-yellow .content-wrapper .filter form  table, .skin-yellow .content-wrapper #content table.data  tr:hover{
	background-color:#fbe1b6;
}
.skin-yellow .content-wrapper   table.data  th, .skin-yellow .content-wrapper  table.data  th.short  {
	background-color: #492e04 !important;
}

.skin-yellow .content-wrapper .btn-primary {
	background-color:#cc6600 !important;
	border-color:#cc6600 !important;
}

.skin-yellow .content-wrapper .btn-success {
	background-color:#613e05 !important;
	border-color:#613e05 !important;
}
.skin-yellow .content-wrapper .usage h2, .skin-yellow .content-wrapper .usage h3 {color:white;background-color:#cc6600; border-color:#cc6600;}
.skin-yellow .content-wrapper .usage, .skin-yellow .content-wrapper .usage td{border-color:#cc6600;}
.skin-yellow .content-wrapper .usage {background-color:white;}
.skin-yellow .content-wrapper .usage h3  a {color:white;}
/**********CLOSE custom skin color for .skin-yellow  **************/

/**********custom skin color for .skin-yellow-light  **************/
body.skin-yellow-light, .skin-yellow-light .content-wrapper {
	background-color:#fef5e7 !important;
}
.skin-yellow-light .content-wrapper .filter form, .skin-yellow-light .content-wrapper .filter form  table, .skin-yellow-light .content-wrapper #content table.data  tr:hover{
	background-color:#fbe1b6;
}
.skin-yellow-light .content-wrapper   table.data  th, .skin-yellow-light .content-wrapper  table.data  th.short  {
	background-color: #492e04 !important;
}

.skin-yellow-light .content-wrapper .btn-primary {
	background-color:#cc6600 !important;
	border-color:#cc6600 !important;
}

.skin-yellow-light .content-wrapper .btn-success {
	background-color:#613e05 !important;
	border-color:#613e05 !important;
}
.skin-yellow-light .content-wrapper .usage h2, .skin-yellow-light .content-wrapper .usage h3 {color:white;background-color:#cc6600; border-color:#cc6600;}
.skin-yellow-light .content-wrapper .usage, .skin-yellow-light .content-wrapper .usage td{border-color:#cc6600;}
.skin-yellow-light .content-wrapper .usage {background-color:white;}
.skin-yellow-light .content-wrapper .usage h3  a {color:white;}
/**********CLOSE custom skin color for .skin-yellow-light  **************/

.main-footer {
overflow:hidden;
	position: fixed !important;
	left:0;
	right:0;
    bottom: 0 !important;
	max-height:40px;
	z-index: 1030;
	margin-left:0;
	padding:10px;
	box-sizing: border-box!important;

 }
 
 .main-sidebar, .left-side  {
	 padding-bottom:40px;
 }
 


/* Fixed layout */
/* .fixed_header .main-header,
.fixed_header .main-sidebar,
.fixed_header .left-side {
 position: fixed;
} */

.fixed_header .main-header {
  top: 0;
  right: 0;
  left: 0;
  position: fixed;
}
.fixed_header .content-wrapper,
.fixed_header .right-side {
  padding-top: 50px;
}
/* Fixed layout */


@media (max-width: 1085px) {
body { overflow: scroll !important;}
.main-footer {
	padding-top:0px;
	padding-bottom:10px;
	max-height:25px;
 }
 .main-sidebar, .left-side  {padding-bottom:25px; padding-top: 50px;}
}

@media (max-width: 761px) {
  .fixed_header .content-wrapper,
  .fixed_header .right-side {
    padding-top: 50px;
  }
  
.fixed_header.layout-boxed .wrapper {
  max-width: 100%;
}
}

 /* .fixed_header .content-wrapper,
  .fixed_header .right-side {
    padding-top: 0!important;
  } */
  