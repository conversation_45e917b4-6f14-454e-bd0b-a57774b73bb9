<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Errorsite extends CI_Controller {


function __construct(){
        parent::__construct();
        
        $this->load->library('session');
		$this->load->library('form_validation');
		$this->load->library('user_agent');
		$this->load->helper('security');
		$this->load->library("pagination");
        
        date_default_timezone_set('Asia/Dhaka');
     
    }
	
	public function index()
	{
		$this->load->view('errors/405');
	}
	public function getoperators()
	{
		$var=$this->input->get();

		$country=$var['country']; 
		$data['id'] = $country;
		$this->load->view('mit/others/getop', $data);

	}
	public function get_code()
	{
		
		$var=$this->input->get();

		$operator=$var['operator']; 

		$data['id'] = $operator;
		$this->load->view('mit/others/get_code', $data);
	}

	public function rate_code()
	{
		
		$var=$this->input->get();

		$country=$var['country']; 

		$data['id'] = $country;
		$this->load->view('admin/country/rate_code', $data);
	}

	
	public function getop_code()
	{
		
		$var=$this->input->get();

		$operator=$var['operator']; 

		$data['id'] = $operator;
		$this->load->view('admin/country/get_op_code', $data);
	}
	
	public function otp() {
		
	if ($this->session->userdata('member_session')) {
		
		$user=$this->session->userdata('member_session');
		
		$memberdata=$this->mdb->getData('reseller',array('id'=>$user->id));
				
		$username         = $memberdata[0]['username'];
		$ucid           = $memberdata[0]['id'];
		$enotp           = $memberdata[0]['enbale_otp'];
		
		
		
		if($enotp=="1") 
		{ 
		$otpcodsame=$memberdata[0]['otp'];; 
		}else { 
		$otpcodsame=$memberdata[0]['pincode'];
		} 
		
		
		if($_POST) {
		$this->form_validation->set_rules('pincode', 'PIN', 'trim|required|min_length[3]|max_length[30]');
		$pincode = $this->security->xss_clean($this->input->post('pincode'));
		$remember = $this->security->xss_clean($this->input->post('remember'));
		
		if ($this->form_validation->run() == FALSE) {
		$this->session->set_flashdata('error', 'Please Enter Correct Info');
		$this->load->view('otp_lock');
		}else {
		$utype='user';
		$chking = $this->mit->otpchk($pincode,$utype,$ucid);
		if($chking) {
		$this->mit->devicelist($ucid,$remember);
		$this->mit->clear_login_attempts($username);
		
		$lastlogID = $this->mit->lastlogID($ucid,'user');
		
		 $otpupdate = array(
							"otpcode" => $otpcodsame
						);
			 
			 $this->mdb->update('userlog',$otpupdate,array('id'=>$lastlogID));
		
		$referrer = $this->session->userdata('user_requested_page') ? $this->session->userdata('user_requested_page') : 'main';
		 redirect($referrer);
		 
		
		}else {
			$msg= 'Invalid CODE';
			$trytype = 'pin';
			
			$this->mit->increase_login_attempts($username,$pincode,$msg,$trytype);
			
		$this->session->set_flashdata('error', 'Invalid CODE.');
		$this->load->view('otp_lock');
		}
			 
		}
		
				
		}else {
			
		$this->load->view('otp_lock');
		}
			
		}else {
			redirect('login', 'refresh');
		}
		}
		
		public function adminotp() {
		
	if ($this->session->userdata('admin_session')) {
		
		$user=$this->session->userdata('admin_session');
	
		$memberdata=$this->mdb->getData('reseller',array('id'=>$user->id));
				
		$username         = $memberdata[0]['username'];
		$ucid           = $memberdata[0]['id'];
		$enotp           = $memberdata[0]['enbale_otp'];
		
		
		
		if($enotp=="1") 
		{ 
		$otpcodsame=$memberdata[0]['otp'];; 
		}else { 
		$otpcodsame=$memberdata[0]['pincode'];
		} 
		
		if($_POST) {
		$this->form_validation->set_rules('pincode', 'PIN', 'trim|required|min_length[3]|max_length[30]');
		$pincode = $this->security->xss_clean($this->input->post('pincode'));
		$remember = $this->security->xss_clean($this->input->post('remember'));
		
		if ($this->form_validation->run() == FALSE) {
		$this->session->set_flashdata('error', 'Please Enter Correct Info');
		$this->load->view('admin/login/admin_otp');
		}else {
		$utype='admin';
		$chking = $this->mit->otpchk($pincode,$utype,$ucid);
		if($chking) {
		$this->mit->devicelist($ucid,$remember);
		$this->mit->clear_login_attempts($username);
		
		$lastlogID = $this->mit->lastlogID($ucid,'admin');
		
		 $otpupdate = array(
							"otpcode" => $otpcodsame
						);
			 
			 $this->mdb->update('userlog',$otpupdate,array('id'=>$lastlogID));
		
		$referrer = $this->session->userdata('user_requested_page') ? $this->session->userdata('user_requested_page') : 'admin';
		 redirect($referrer);
		 
		
		}else {
			$msg= 'Invalid CODE';
			$trytype = 'pin';
			
			$this->mit->increase_login_attempts($username,$pincode,$msg,$trytype);
			
		$this->session->set_flashdata('error', 'Invalid pincode.');
		$this->load->view('admin/login/admin_otp');
		}
			 
		}
		
				
		}else {
			
		$this->load->view('admin/login/admin_otp');
		}
			
		}else {
			redirect('superadmin', 'refresh');
		}
		}
		
		public function get_api_url()
		{
		$var=$this->input->get();

		
		$data['id'] = $var['service']; 
		$this->load->view('admin/others/choose_api_set', $data);
		}
		
		
}
