<?php
// Include the database connection information
include 'mydata.php';

// Check if the username is provided
if (isset($_POST['username'])) {
    // Sanitize and get the username
    $username = mysqli_real_escape_string($conn, $_POST['username']);

    // Query to get user's balance, email, mobile, and name
    $query = "SELECT balance, email, mobile, name FROM reseller WHERE username = '$username'";

    // Execute the query
    $result = mysqli_query($conn, $query);

    // Check if the query was successful
    if ($result) {
        // Check if the username exists
        if (mysqli_num_rows($result) > 0) {
            // Fetch the balance, email, mobile, and name
            $row = mysqli_fetch_assoc($result);
            $balance = $row['balance'];
            $email = $row['email'];
            $mobile = $row['mobile'];
            $name = $row['name'];

            // Return the data as JSON
            echo json_encode(['success' => true, 'balance' => $balance, 'email' => $email, 'mobile' => $mobile, 'name' => $name]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Username not found']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Query failed: ' . mysqli_error($conn)]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Username not provided']);
}

// Close the database connection
mysqli_close($conn);
?>