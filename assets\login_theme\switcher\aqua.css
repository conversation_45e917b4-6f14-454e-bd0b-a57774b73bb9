.spinner,
.tp-loader.spinner0,
#fancybox-loading div {
    border-left: 3px solid rgba(40,184,216,.15);
    border-right: 3px solid rgba(40,184,216,.15);
    border-bottom: 3px solid rgba(40,184,216,.15);
    border-top: 3px solid rgba(40,184,216,.8);
}
a {
    color: #28b8d8;
}
.nocolor:hover {
    color: #28b8d8
}
.post-title a:hover {
    color: #28b8d8
}
.main-title.text-center:after {
    color: #28b8d8;
}
ul.circled li:before {
    color: #28b8d8;
}
.meta a:hover,
.more:hover {
    color: #28b8d8
}
footer a:hover {
    color: #28b8d8 !important
}
.btn,
.vanilla-form label.custom-select span {
    background: #28b8d8;
}
.btn:hover,
.btn:focus,
.btn:active,
.btn.active,
.pagination ul > li > a:hover,
.pagination ul > li > a:focus,
.pagination ul > .active > a,
.pagination ul > .active > span {
    background: #00a1c4;
}
.navbar .navbar-nav > li > a:focus,
.navbar .nav > li > a:hover,
.navbar .nav > li.current > a {
    color: #28b8d8 !important;
}
.navbar .dropdown-menu li a:hover,
.navbar .dropdown-menu li a.active {
    color: #28b8d8 !important;
}
.navbar .nav .open > a,
.navbar .nav .open > a:hover,
.navbar .nav .open > a:focus {
    color: #28b8d8;
}
.navbar .dropdown-menu > li > a:hover,
.navbar .dropdown-menu > li > a:focus,
.navbar .dropdown-submenu:hover > a,
.navbar .dropdown-submenu:focus > a,
.navbar .dropdown-menu > .active > a,
.navbar .dropdown-menu > .active > a:hover,
.navbar .dropdown-menu > .active > a:focus {
    color: #28b8d8
}
.navbar .top-bar a:hover {
	color: #28b8d8;
}
.yamm .yamm-content a:hover {
    color: #28b8d8
}
.steps .icon {
    color: #28b8d8;
}
.steps .steps-item .number {
    background: #28b8d8;
}
.steps .steps-item:hover {
    border-color: #28b8d8
}
.feature .icon {
    color: #28b8d8;
}
.icon-large {
    color: #28b8d8;
}
.owl-item .item:hover .box {
    box-shadow: 0 2px 0 #28b8d8
}
.post-types .date-wrapper {
    background: #28b8d8;
}
.post-types .post .format-wrapper {
    background: #28b8d8;
}
.sidebox a:hover {
    color: #28b8d8
}
.widget .post-list h5 a:hover {
    color: #28b8d8
}
.widget .post-list .meta em a:hover {
    color: #28b8d8
}
footer ul.tag-list li a:hover {
    background: #28b8d8 !important
}
.testimonials2 .quote:hover {
    border: 2px solid #28b8d8
}
.isotope-filter ul li a:hover,
.isotope-filter ul li a.is-checked {
    color: #28b8d8
}
.isotope-filter ul li:after {
    color: #28b8d8;
}
.price {
    color: #28b8d8;
}
.progress-list li em {
    color: #28b8d8;
}
.progress.plain .bar {
    background: #28b8d8;
}
.bordered .progress.plain {
    border: 1px solid #28b8d8;
}
.bordered .progress.plain .bar {
    background: #28b8d8
}
.tabs-top.bordered .tab a {
    color: #28b8d8;
    border: 2px solid #28b8d8;
}
.tabs-top .tab a:hover,
.tabs-top .tab.active a {
    color: #28b8d8;
    box-shadow: 0 2px 0 #28b8d8;
}
.tabs-top.bordered .tab a:hover,
.tabs-top.bordered .tab.active a {
    border: 2px solid #28b8d8;
    background: #28b8d8;
}
.tabs-bottom .tab:hover .icon.box,
.tabs-bottom .tab.active .icon.box {
    box-shadow: 0 2px 0 #28b8d8
}
.panel-group .panel-active a,
.panel-group .panel-title > a:hover {
    color: #28b8d8
}
.panel-group .panel-heading .panel-title:hover,
.panel-group .panel-active .panel-heading .panel-title {
    box-shadow: 0 2px 0 #28b8d8;
    color: #28b8d8;
}
.bordered .panel-heading .panel-title {
    color: #28b8d8;
    border: 2px solid #28b8d8;
}
.bordered .panel-heading .panel-title:hover {
    background: #28b8d8;
    border: 2px solid #28b8d8;
}
.bordered .panel-title > a {
    color: #28b8d8
}
.bordered .panel-title:hover,
.bordered .panel-active .panel-heading .panel-title,
.bordered .panel-active .panel-heading .panel-title:hover {
    border: 2px solid #28b8d8;
    background: #28b8d8;
}
.tooltip-inner {
    background-color: #28b8d8;
}
.tooltip.top .tooltip-arrow,
.tooltip.top-left .tooltip-arrow,
.tooltip.top-right .tooltip-arrow {
    border-top-color: #28b8d8
}
.tooltip.right .tooltip-arrow {
    border-right-color: #28b8d8
}
.tooltip.left .tooltip-arrow {
    border-left-color: #28b8d8
}
.tooltip.bottom .tooltip-arrow,
.tooltip.bottom-left .tooltip-arrow,
.tooltip.bottom-right .tooltip-arrow {
    border-bottom-color: #28b8d8
}
.vanilla-form input[type="radio"]:focus + span,
.vanilla-form input[type="checkbox"]:focus + span,
.vanilla-form input[type="radio"]:active + span,
.vanilla-form input[type="checkbox"]:active + span {
    border-color: #28b8d8
}
.vanilla-form input[type="radio"] + span::after {
    background-color: #28b8d8;
    border-color: #28b8d8;
}
.vanilla-form input[type="radio"]:checked + span,
.vanilla-form input[type="checkbox"]:checked + span {
    border: 2px solid #28b8d8
}
.vanilla-form input[type="checkbox"] + span::after {
    border: 0 solid #28b8d8;
}
#comments .info h2 a:hover {
    color: #28b8d8
}
#comments a.reply-link:hover {
    color: #28b8d8
}
#contact-info-button:hover,
#btn-close-modal:hover {
    color: #28b8d8
}
@media (max-width: 991px) { 
	.navbar .navbar-nav .open .dropdown-menu > li > a:hover,
	.navbar .navbar-nav .open .dropdown-menu > li > a:focus {
	    color: #28b8d8 !important
	}
}
@media (max-width: 767px) { 
	.isotope-filter ul li a:hover,
	.isotope-filter ul li a.is-checked {
	    box-shadow: 0 2px 0 #28b8d8 !important
	}
}