<?php


defined('BASEPATH') OR exit('No direct script access allowed');

class Gateway extends CI_Controller {

	 	function __construct() {
		parent::__construct();
		
		//$this->load->database();

		date_default_timezone_set('Asia/Dhaka');
		$this->load->library('encryption');

		$this->load->helper('security');
		}
		
		private function date_time(){
       
        return $create_date = date('Y-m-d H:i:s');
     }
	 
	 private function dates(){
        
        return $create_date = date('Y-m-d');
     }
	 
	 
	 

	 
	 
	public function index()
	{
	    $this->load->view('errors/405');
	}
	
	public function addTransaction()
{
    // Check if the request method is POST
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Retrieve and sanitize POST data
        $gateway = $_POST["Gateway"] ?? '';
        $amount = $_POST["Amount"] ?? 0;
        $sender = $_POST["Sender"] ?? '';
        $trxid = $_POST["TrxID"] ?? '';
        
        // Check if all POST data is empty and terminate if so
        if (empty($gateway) && empty($amount) && empty($sender) && empty($trxid)) {
            die("All POST data is empty");
        }
        
        // Proceed with data processing if any of the POST data is present
        $time = time();
        $create_date = date('j F Y g:i A');
        $idate = date('Y-m-d');
        
        $str = 'Gateway/Sender: ' . $gateway . ' TRansaction ID: ' . $trxid . ' Amount: ' . $amount;
        
        $ctrnx = $this->mdb->passwordChanger('encrypt', $trxid);
        $msg = $this->mdb->passwordChanger('encrypt', $str);
        
        $sql = "SELECT * FROM `trnx` WHERE trnx='$ctrnx'";
        $queryrtsloadm = $this->db->query($sql);
        
        if ($queryrtsloadm->num_rows() == 0) {
            $sql = "INSERT INTO `trnx` (`time`,`amount`,`type`,`cat`,`sender`,`trnx`,`add_date`,`msg`) 
                    VALUES ('$time','$amount','$gateway','$gateway','$gateway','$ctrnx','$create_date','$msg')";
            
            $this->db->query($sql);
            // Assuming $newbal is defined somewhere in your code
            $change = array('simbalance' => $newbal);
            $this->mdb->update('modem', $change, array('sender' => $gateway));
            
            echo 'New Payment Added';
        } else {
            die('Transaction Already Exists');
        }
    } else {
        die("Good Boy");
    }
}

}