<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Service extends CI_Controller {
	
		function __construct() {
		parent::__construct();
		
		//$this->load->database();

		date_default_timezone_set('Asia/Dhaka');
		
		if (!$this->session->userdata('member_session')) {
        	
			 redirect('login', 'refresh');
        	
        }

		$this->load->library('form_validation');
		$this->load->library('user_agent');
		$this->load->helper('security');
		$this->load->library("pagination");
		}
		
		private function date_time(){
       
        return $create_date = date('Y-m-d H:i:s');
     }
	 
	 private function dates(){
        
        return $create_date = date('Y-m-d');
     }

	
	
	public function chooseamount(){
	
	$varget=$this->input->get();
	$data['id'] = $varget['operator'];
	$this->load->view('chooseamount',$data);
	
	 }
	
	
}
