@charset "utf-8";
/* CSS Document */

body {
	color: #4f6b72;
	background: #fff;
}

a {
	color: #333;
}

#mytable {
	width: 100%;
	padding: 0;
	margin: 0;
}

caption {
	padding: 0 0 5px 0;
	width: 500px;
	text-align: center;
}

th {
	color: #4f6b72;
	letter-spacing: 2px;
	text-align: left;
	padding: 5px;
}

th.nobg {
	border-top: 0;
	border-left: 0;
	border-right: 1px solid #C1DAD7;
	background: none;
}

td {
	padding: 5px;
	color: #4f6b72;
}


td.alt {
	color: #797268;
	padding:0px 5px;
	border-left:1px solid #f3f3f3;
}

th.spec {
	border-left: 1px solid #C1DAD7;
	border-top: 0;
	font: bold 10px "Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
}

th.specalt {
	border-left: 1px solid #C1DAD7;
	border-top: 0;
	font: bold 10px "Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
	color: #797268;
	width:160px;
}
tr.a{background:#f1f1f1;}
tr.b{background:#f9f9f9;}