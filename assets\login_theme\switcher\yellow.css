.spinner,
.tp-loader.spinner0,
#fancybox-loading div {
    border-left: 3px solid rgba(245,196,26,.15);
    border-right: 3px solid rgba(245,196,26,.15);
    border-bottom: 3px solid rgba(245,196,26,.15);
    border-top: 3px solid rgba(245,196,26,.8);
}
a {
    color: #f5c41a;
}
.nocolor:hover {
    color: #f5c41a
}
.post-title a:hover {
    color: #f5c41a
}
.main-title.text-center:after {
    color: #f5c41a;
}
ul.circled li:before {
    color: #f5c41a;
}
.meta a:hover,
.more:hover {
    color: #f5c41a
}
footer a:hover {
    color: #f5c41a !important
}
.btn,
.vanilla-form label.custom-select span {
    background: #f5c41a;
}
.btn:hover,
.btn:focus,
.btn:active,
.btn.active,
.pagination ul > li > a:hover,
.pagination ul > li > a:focus,
.pagination ul > .active > a,
.pagination ul > .active > span {
    background: #f0ba00;
}
.navbar .navbar-nav > li > a:focus,
.navbar .nav > li > a:hover,
.navbar .nav > li.current > a {
    color: #f5c41a !important;
}
.navbar .dropdown-menu li a:hover,
.navbar .dropdown-menu li a.active {
    color: #f5c41a !important;
}
.navbar .nav .open > a,
.navbar .nav .open > a:hover,
.navbar .nav .open > a:focus {
    color: #f5c41a;
}
.navbar .dropdown-menu > li > a:hover,
.navbar .dropdown-menu > li > a:focus,
.navbar .dropdown-submenu:hover > a,
.navbar .dropdown-submenu:focus > a,
.navbar .dropdown-menu > .active > a,
.navbar .dropdown-menu > .active > a:hover,
.navbar .dropdown-menu > .active > a:focus {
    color: #f5c41a
}
.navbar .top-bar a:hover {
	color: #f5c41a;
}
.yamm .yamm-content a:hover {
    color: #f5c41a
}
.steps .icon {
    color: #f5c41a;
}
.steps .steps-item .number {
    background: #f5c41a;
}
.steps .steps-item:hover {
    border-color: #f5c41a
}
.feature .icon {
    color: #f5c41a;
}
.icon-large {
    color: #f5c41a;
}
.owl-item .item:hover .box {
    box-shadow: 0 2px 0 #f5c41a
}
.post-types .date-wrapper {
    background: #f5c41a;
}
.post-types .post .format-wrapper {
    background: #f5c41a;
}
.sidebox a:hover {
    color: #f5c41a
}
.widget .post-list h5 a:hover {
    color: #f5c41a
}
.widget .post-list .meta em a:hover {
    color: #f5c41a
}
footer ul.tag-list li a:hover {
    background: #f5c41a !important
}
.testimonials2 .quote:hover {
    border: 2px solid #f5c41a
}
.isotope-filter ul li a:hover,
.isotope-filter ul li a.is-checked {
    color: #f5c41a
}
.isotope-filter ul li:after {
    color: #f5c41a;
}
.price {
    color: #f5c41a;
}
.progress-list li em {
    color: #f5c41a;
}
.progress.plain .bar {
    background: #f5c41a;
}
.bordered .progress.plain {
    border: 1px solid #f5c41a;
}
.bordered .progress.plain .bar {
    background: #f5c41a
}
.tabs-top.bordered .tab a {
    color: #f5c41a;
    border: 2px solid #f5c41a;
}
.tabs-top .tab a:hover,
.tabs-top .tab.active a {
    color: #f5c41a;
    box-shadow: 0 2px 0 #f5c41a;
}
.tabs-top.bordered .tab a:hover,
.tabs-top.bordered .tab.active a {
    border: 2px solid #f5c41a;
    background: #f5c41a;
}
.tabs-bottom .tab:hover .icon.box,
.tabs-bottom .tab.active .icon.box {
    box-shadow: 0 2px 0 #f5c41a
}
.panel-group .panel-active a,
.panel-group .panel-title > a:hover {
    color: #f5c41a
}
.panel-group .panel-heading .panel-title:hover,
.panel-group .panel-active .panel-heading .panel-title {
    box-shadow: 0 2px 0 #f5c41a;
    color: #f5c41a;
}
.bordered .panel-heading .panel-title {
    color: #f5c41a;
    border: 2px solid #f5c41a;
}
.bordered .panel-heading .panel-title:hover {
    background: #f5c41a;
    border: 2px solid #f5c41a;
}
.bordered .panel-title > a {
    color: #f5c41a
}
.bordered .panel-title:hover,
.bordered .panel-active .panel-heading .panel-title,
.bordered .panel-active .panel-heading .panel-title:hover {
    border: 2px solid #f5c41a;
    background: #f5c41a;
}
.tooltip-inner {
    background-color: #f5c41a;
}
.tooltip.top .tooltip-arrow,
.tooltip.top-left .tooltip-arrow,
.tooltip.top-right .tooltip-arrow {
    border-top-color: #f5c41a
}
.tooltip.right .tooltip-arrow {
    border-right-color: #f5c41a
}
.tooltip.left .tooltip-arrow {
    border-left-color: #f5c41a
}
.tooltip.bottom .tooltip-arrow,
.tooltip.bottom-left .tooltip-arrow,
.tooltip.bottom-right .tooltip-arrow {
    border-bottom-color: #f5c41a
}
.vanilla-form input[type="radio"]:focus + span,
.vanilla-form input[type="checkbox"]:focus + span,
.vanilla-form input[type="radio"]:active + span,
.vanilla-form input[type="checkbox"]:active + span {
    border-color: #f5c41a
}
.vanilla-form input[type="radio"] + span::after {
    background-color: #f5c41a;
    border-color: #f5c41a;
}
.vanilla-form input[type="radio"]:checked + span,
.vanilla-form input[type="checkbox"]:checked + span {
    border: 2px solid #f5c41a
}
.vanilla-form input[type="checkbox"] + span::after {
    border: 0 solid #f5c41a;
}
#comments .info h2 a:hover {
    color: #f5c41a
}
#comments a.reply-link:hover {
    color: #f5c41a
}
#contact-info-button:hover,
#btn-close-modal:hover {
    color: #f5c41a
}
@media (max-width: 991px) { 
	.navbar .navbar-nav .open .dropdown-menu > li > a:hover,
	.navbar .navbar-nav .open .dropdown-menu > li > a:focus {
	    color: #f5c41a !important
	}
}
@media (max-width: 767px) { 
	.isotope-filter ul li a:hover,
	.isotope-filter ul li a.is-checked {
	    box-shadow: 0 2px 0 #f5c41a !important
	}
}